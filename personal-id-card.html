<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="بطاقة شخصية للحالة المدنية - المملكة المغربية">
    <meta name="author" content="مكتب الحالة المدنية - أيير">
    <title>بطاقة شخصية للحالة المدنية - مكتب أيير</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇲🇦</text></svg>">

    <!-- الأنماط المحسنة للبطاقة الشخصية -->
    <style>
        /* ===== إعدادات عامة ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* ألوان المملكة المغربية */
            --morocco-red: #C1272D;
            --morocco-green: #006233;
            --morocco-gold: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #34495e;
            --background-light: #f8f9fa;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);

            /* خطوط */
            --font-primary: 'Times New Roman', 'Amiri', serif;
            --font-secondary: 'Arial', 'Tahoma', sans-serif;

            /* أحجام نصف ورقة A4 (أفقي) مع هوامش صحيحة */
            --card-width: 190mm;  /* عرض نصف ورقة A4 مع هوامش 1 سم */
            --card-height: 128.5mm; /* ارتفاع نصف ورقة A4 مع هوامش 1 سم */
            --border-radius: 8px;
            --spacing-xs: 2mm;
            --spacing-sm: 4mm;
            --spacing-md: 6mm;
            --spacing-lg: 8mm;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, var(--background-light) 0%, #e8f4f8 100%);
            min-height: 100vh;
            padding: var(--spacing-md);
            color: var(--text-dark);
            line-height: 1.4;
        }

        /* ===== حاوي التطبيق الرئيسي ===== */
        #app {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* ===== حاوي الورقة الموحدة ===== */
        .document {
            width: var(--card-width);
            height: var(--card-height);
            margin: 10mm auto; /* هامش 1 سم من الأعلى والأسفل، توسيط أفقي */
            margin-left: calc(50% - var(--card-width)/2 - 10mm); /* إزاحة 1 سم نحو اليسار */
            background: white;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-medium);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* ===== أنماط الهيدر ===== */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 3mm 5mm;            /* تقليل الحشو للأبعاد الجديدة */
            font-size: 11px;             /* تقليل لتناسب المساحة الأصغر */
            font-weight: 600;
            color: var(--text-dark);
            min-height: 20mm;            /* تقليل الارتفاع */
        }

        .header-left {
            flex: 1;
            text-align: right;
            direction: rtl;
            padding-right: 0;
            margin-right: 0;
        }

        .header-center {
            flex: 1;
            text-align: center;
            margin: 0 var(--spacing-sm);
        }

        .header-right {
            flex: 1;
            text-align: left;
            direction: ltr;
            padding-left: 0;
            margin-left: 0;
        }

        /* ===== أنماط قسم المعلومات الشخصية ===== */
        .personal-info {
            padding: 2mm 5mm;    /* تقليل الحشو للأبعاد الجديدة */
            flex: 1;             /* يملأ المساحة المتبقية */
            margin-top: 0;       /* إزالة الرفع لتوزيع أفضل */
            display: flex;
            flex-direction: column;
            justify-content: space-evenly; /* توزيع متساوي للعناصر */
            min-height: 70mm;    /* تقليل الارتفاع للمساحة الأصغر */
        }

        .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 0.5mm;  /* تقليص الفراغ بين الحقول */
            padding: 0.8mm 0;      /* تقليص الحشو العمودي */
            direction: rtl;
            text-align: right;
            line-height: 1.2;      /* تقليص ارتفاع السطر */
            min-height: 5.5mm;     /* تقليص الارتفاع الأدنى */
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;     /* إزالة الحشو السفلي للصف الأخير */
        }

        /* تقليص الفراغ في الصف الأخير من البيانات الهامشية */
        .marginal-row {
            margin-bottom: 0.3mm !important;  /* تقليص أكثر للصف الأخير */
            padding-bottom: 0.5mm !important;
        }

        .info-label {
            font-weight: 700;
            color: var(--text-dark);
            font-size: 12px;           /* تقليل للمساحة الأصغر */
            margin-left: 1.5mm;        /* تقليل الهامش */
            min-width: 35mm;           /* تقليل العرض للمساحة الأصغر */
        }

        /* إضافة فراغ بين التسمية والخط */
        .info-row {
            gap: 2mm;
        }

        .info-row .info-label {
            min-width: auto;
            margin-left: 0;
        }

        .info-value {
            flex: 1;
            padding: 0.3mm 1mm;             /* تقليص الحشو أكثر */
            border-bottom: 1.5px solid #333;
            min-height: 4mm;                /* تقليص الارتفاع الأدنى */
            font-weight: 700;
            color: var(--text-dark);
            background: transparent;
            font-size: 11px;                /* تقليص حجم الخط */
            line-height: 1.0;               /* تقليص ارتفاع السطر أكثر */
        }

        .french-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 10px;                 /* تقليص للمساحة المضغوطة */
            margin-right: 0.5mm;             /* تقليص الهامش أكثر */
            direction: ltr;
            text-align: left;
        }

        .hijri-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 10px;                 /* تقليص للمساحة المضغوطة */
            margin-left: 0.5mm;              /* تقليص الهامش أكثر */
            direction: rtl;
            text-align: right;
        }

        .location-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 11px;
            margin-left: var(--spacing-xs);
            direction: rtl;
            text-align: right;
        }

        /* قص خط قسم ساكن حاليا ب */
        #currentAddress {
            width: calc(50% - 15mm);  /* العرض الأصلي - 15mm (قص إجمالي 5.5 سنتيمتر) */
            flex: none;
        }

        /* ===== أنماط البيانات الهامشية ===== */
        .marginal-x-marks {
            font-family: monospace;
            letter-spacing: 1px;
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 2mm;
        }

        #marginalData {
            flex: 1;
            padding: var(--spacing-xs);
            border-bottom: 1px dotted #333;  /* تغيير الخط إلى نقاط */
            min-height: 20px;
            font-weight: 500;
            color: var(--text-dark);
            background: transparent;
        }

        .marginal-row {
            padding-bottom: 1mm;           /* تقليل الحشو السفلي */
            margin-bottom: 1mm;            /* تقليل المسافة السفلية */
        }

        /* ===== تقسيم الجزء الفارغ ===== */
        .bottom-section {
            display: flex;
            width: 100%;            /* استخدام العرض الكامل */
            height: 35mm;           /* تقليل الارتفاع للمساحة الأصغر */
            position: relative;
            margin-top: auto;       /* دفع القسم للأسفل */
            padding: 0 5mm;         /* تقليل الحشو */
        }

        .left-section {
            width: 50%;             /* نصف العرض */
            height: 35mm;           /* مطابقة الارتفاع الجديد */
            padding: 3mm;           /* تقليل الحشو */
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .right-section {
            width: 50%;             /* نصف العرض */
            height: 35mm;           /* مطابقة الارتفاع الجديد */
            padding: 3mm;           /* تقليل الحشو */
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* خط عمودي في الوسط تماماً */
        .bottom-section::after {
            content: '';
            position: absolute;
            left: 50%;              /* في الوسط بالنسبة المئوية */
            top: 0;
            width: 2px;             /* زيادة سمك الخط */
            height: 100%;
            background: #333;
            transform: translateX(-50%); /* توسيط دقيق */
        }

        /* خط فاصل بنفس لون الخط العمودي */
        hr {
            border: none;
            height: 1px;
            background: #333;
            margin: 0.5mm 0;               /* تقليص المسافة حول الخط الفاصل أكثر */
        }

        /* ===== أنماط الجزء الأيسر ===== */
        .witness-text {
            font-size: 14px;                /* زيادة من 11px إلى 14px */
            line-height: 1.4;
            margin-bottom: var(--spacing-sm);
            text-align: justify;
            color: var(--text-dark);
        }

        .witness-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 12px;                /* تقليص للمساحة المضغوطة */
            margin-left: 1mm;               /* تقليص الهامش */
            margin-bottom: 0.3mm;           /* تقليص المسافة السفلية */
        }

        .witness-value {
            flex: 0.5;
            padding: 0.5px;                  /* تقليص الحشو */
            border-bottom: 1px dotted #333;
            min-height: 12px;                /* تقليص الارتفاع الأدنى */
            font-weight: 700;
            color: var(--text-dark);
            background: transparent;
            margin-left: 1px;                /* تقليص الهامش */
            font-size: 11px;                 /* تحديد حجم خط أصغر */
        }

        .signature-text {
            font-size: 12px;                /* تقليص للمساحة المضغوطة */
            font-weight: 600;
            text-align: center;
            margin-top: 1mm;                /* تقليص المسافة العلوية */
            margin-bottom: 0;               /* إزالة المسافة السفلية */
            color: var(--text-dark);
        }

        .witness-paragraph {
            font-size: 12px;       /* تقليص للمساحة المضغوطة */
            line-height: 1.1;      /* تقليص ارتفاع السطر */
            text-align: justify;
            color: var(--text-dark);
            margin-bottom: 0.5mm;  /* تقليص المسافة السفلية */
            margin-top: 0;         /* إزالة المسافة العلوية */
            font-weight: 600;
        }

        /* تقليص أكثر للفقرة القانونية */
        .right-section .witness-paragraph:last-of-type {
            font-size: 10px;       /* تقليص أكثر للمساحة المضغوطة */
            font-weight: 700;
            line-height: 1.0;      /* تقليص ارتفاع السطر أكثر */
            margin-bottom: 0.3mm;  /* تقليص أكثر */
            margin-top: 0.5mm;     /* تقليص أكثر */
        }

        .inline-field {
            display: inline-block;
            min-width: 20mm;                 /* تقليص العرض الأدنى */
            border-bottom: 1px dotted #333;
            height: 14px;                    /* تقليص الارتفاع */
            margin: 0 1px;                   /* تقليص الهامش */
            font-weight: 700;
            font-size: 11px;                /* تقليص حجم الخط */
        }

        /* تقليص المسافات في الجزء الأيسر */
        .left-section .info-row,
        .left-section .witness-paragraph,
        .left-section .witness-label {
            margin-bottom: 0.3mm;  /* تقليص أكثر للجزء الأيسر */
            padding: 0.2mm 0;      /* تقليص الحشو أكثر */
            line-height: 1.0;      /* تقليص ارتفاع السطر أكثر */
        }

        /* تقليص المسافات أكثر في الجزء الأيمن */
        .right-section .info-row,
        .right-section .witness-paragraph,
        .right-section .witness-label {
            margin-bottom: 0.4mm;  /* تقليص أكثر للجزء الأيمن */
            padding: 0.3mm 0;      /* تقليص الحشو أكثر */
            line-height: 1.1;      /* تقليص ارتفاع السطر أكثر */
        }

        /* تنسيق خاص للحقول المملوءة من قاعدة البيانات في القسم الأيمن */
        .right-section #witnessName1,
        .right-section #witnessAddress1 {
            font-weight: 800;      /* سمك خط أكبر للبيانات المجلبة */
            color: var(--text-dark);
        }



        .date-signature-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-sm);
        }

        .date-section {
            display: flex;
            align-items: center;
        }

        .signature-section-left {
            font-size: 14px;                /* زيادة من 11px إلى 14px */
            font-weight: 600;
            color: var(--text-dark);
        }

        /* ===== أنماط أزرار التحكم ===== */
        .control-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .print-btn, .home-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .print-btn {
            background: var(--morocco-green);
            color: white;
        }

        .print-btn:hover {
            background: #004d2a;
            transform: translateY(-2px);
        }

        .home-btn {
            background: var(--morocco-red);
            color: white;
        }

        .home-btn:hover {
            background: #a01e23;
            transform: translateY(-2px);
        }

        /* إخفاء عناصر عند الطباعة */
        @media print {
            .control-buttons {
                display: none;
            }

            /* إزالة هوامش وعناوين الطباعة */
            @page {
                margin: 0;
                size: A4;
                /* إخفاء header و footer المتصفح */
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            body {
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            /* إخفاء عنوان الصفحة والوقت */
            html {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact; /* الخاصية القياسية للتوافق */
            }
        }

        /* ===== أنماط البطاقة الرئيسية ===== */
        .id-card {
            width: var(--card-width);
            height: var(--card-height);
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius);
            box-shadow:
                0 10px 30px var(--shadow-medium),
                0 0 0 1px var(--border-color);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* ===== ترويسة البطاقة ===== */
        .card-header {
            background: linear-gradient(135deg, var(--morocco-red) 0%, #a91e22 100%);
            color: white;
            padding: var(--spacing-sm);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.05) 10px,
                rgba(255, 255, 255, 0.05) 20px
            );
            animation: shimmer 20s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .kingdom-title {
            font-size: 18px;                /* زيادة من 14px إلى 18px */
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .ministry-info {
            font-size: 14px;                /* زيادة من 11px إلى 14px */
            opacity: 0.9;
            line-height: 1.3;
        }

        /* ===== محتوى البطاقة ===== */
        .card-content {
            flex: 1;
            padding: var(--spacing-md);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        /* ===== أنماط الحقول ===== */
        .field-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs) 0;
            border-bottom: 1px solid #ecf0f1;
            transition: all 0.3s ease;
        }

        .field-row:hover {
            background: rgba(52, 152, 219, 0.05);
            border-radius: 4px;
            padding-left: var(--spacing-xs);
            padding-right: var(--spacing-xs);
        }

        .field-arabic, .field-french {
            flex: 1;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .field-arabic {
            direction: rtl;
            text-align: right;
        }

        .field-french {
            direction: ltr;
            text-align: left;
        }

        .field-label {
            font-weight: 600;
            color: var(--text-dark);
            min-width: 80px;
            font-size: 14px;                /* زيادة من 11px إلى 14px */
        }

        .field-value {
            flex: 1;
            padding: 2px var(--spacing-xs);
            border-bottom: 2px solid var(--morocco-green);
            min-height: 20px;
            font-weight: 700;
            color: var(--text-dark);
            background: rgba(0, 98, 51, 0.05);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .field-value:hover {
            background: rgba(0, 98, 51, 0.1);
            transform: translateY(-1px);
        }

        /* ===== أزرار التحكم ===== */
        .control-buttons {
            text-align: center;
            margin-top: var(--spacing-lg);
            padding: var(--spacing-md);
            background: var(--background-light);
            border-radius: var(--border-radius);
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 var(--spacing-xs);
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.6s ease;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--morocco-red) 0%, #a91e22 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(193, 39, 45, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--morocco-green) 0%, #004d28 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 98, 51, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* ===== أنماط الطباعة لنصف ورقة A4 - مطابقة عقد الازدياد ===== */
        @media print {
            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                font-size: 11px !important;
                width: 210mm !important;
                height: 148.5mm !important;
                overflow: hidden !important;
            }

            .control-buttons {
                display: none !important;
            }

            .document {
                width: 210mm !important;    /* ملء العرض الكامل مثل عقد الازدياد */
                height: 148.5mm !important; /* ملء الارتفاع الكامل مثل عقد الازدياد */
                box-shadow: none !important; /* إزالة الظل للطباعة */
                border: 2px solid #000 !important;
                margin: 0 !important;       /* إزالة جميع الهوامش مثل عقد الازدياد */
                padding: 10mm !important;   /* الهوامش كحشو داخلي مثل عقد الازدياد */
                page-break-inside: avoid !important;
                border-radius: 0 !important; /* إزالة الحواف المدورة للطباعة */
                display: flex !important;
                flex-direction: column !important;
                background: white !important;
                position: absolute !important; /* مطابقة عقد الازدياد */
                top: 0 !important;
                left: 0 !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
                transform: none !important;
                zoom: 1 !important;
            }

            /* إعدادات الصفحة للطباعة - نصف ورقة A4 أفقي */
            @page {
                margin: 0 !important; /* إزالة جميع هوامش الطباعة مثل عقد الازدياد */
                size: 210mm 148.5mm !important; /* حجم نصف ورقة A4 أفقي */
                padding: 0 !important;
            }

            .header {
                padding: 4mm 6mm !important;      /* تحسين للمساحة الكاملة */
                font-size: 11px !important;
                min-height: 20mm !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: flex-start !important;
                color: var(--text-dark) !important;
                font-weight: 600 !important;
                position: relative !important;
                z-index: 1 !important;
            }

            .personal-info {
                padding: 2mm 6mm !important;      /* تحسين للمساحة الكاملة */
                flex: 1 !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: space-between !important; /* توزيع أفضل */
                position: relative !important;
                z-index: 0 !important;
            }

            .info-row {
                margin-bottom: 0.5mm !important;  /* مطابقة المعاينة */
                padding: 0.8mm 0 !important;      /* مطابقة المعاينة */
                min-height: 5.5mm !important;     /* مطابقة المعاينة */
                display: flex !important;
                align-items: center !important;
                direction: rtl !important;
                text-align: right !important;
                line-height: 1.2 !important;      /* مطابقة المعاينة */
                gap: 2mm !important;               /* مطابقة المعاينة */
            }

            .info-value {
                min-height: 4mm !important;       /* مطابقة المعاينة */
                font-size: 11px !important;       /* مطابقة المعاينة */
                padding: 0.3mm 1mm !important;    /* مطابقة المعاينة */
                line-height: 1.0 !important;      /* مطابقة المعاينة */
                flex: 1 !important;
                border-bottom: 1.5px solid #333 !important; /* مطابقة المعاينة */
                font-weight: 700 !important;
                color: var(--text-dark) !important;
                background: transparent !important;
            }

            .info-label {
                font-size: 12px !important;       /* مطابقة المعاينة */
                flex: none !important;             /* عدم أخذ مساحة زائدة */
                white-space: nowrap !important;    /* منع كسر السطر */
                font-weight: 700 !important;
                color: var(--text-dark) !important;
                margin-left: 0 !important;        /* إزالة الهامش الزائد */
            }

            .bottom-section {
                height: 40mm !important;          /* زيادة الارتفاع للمساحة الكاملة */
                padding: 0 6mm !important;        /* تحسين للمساحة الكاملة */
                display: flex !important;
                width: 100% !important;
                position: relative !important;
                margin-top: auto !important;
            }

            .left-section, .right-section {
                padding: 3mm !important;
                height: 40mm !important;          /* مطابقة الارتفاع الجديد */
                width: 50% !important;
                box-sizing: border-box !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: space-between !important;
            }

            .bottom-section::after {
                content: '' !important;
                position: absolute !important;
                left: 50% !important;             /* مطابقة المعاينة */
                top: 0 !important;
                width: 2px !important;            /* مطابقة المعاينة */
                height: 100% !important;
                background: #333 !important;
                transform: translateX(-50%) !important; /* مطابقة المعاينة */
            }

            /* تحسين تنسيق الجزء الأيسر للمساحة الكاملة */
            .left-section .info-row,
            .left-section .witness-paragraph,
            .left-section .witness-label {
                margin-bottom: 0.4mm !important; /* زيادة قليلة للمساحة الكاملة */
                padding: 0.3mm 0 !important;     /* زيادة قليلة */
                line-height: 1.1 !important;     /* تحسين القراءة */
            }

            /* تحسين تنسيق الجزء الأيمن للمساحة الكاملة */
            .right-section .info-row,
            .right-section .witness-paragraph,
            .right-section .witness-label {
                margin-bottom: 0.5mm !important; /* زيادة قليلة للمساحة الكاملة */
                padding: 0.4mm 0 !important;     /* زيادة قليلة */
                line-height: 1.2 !important;     /* تحسين القراءة */
            }

            .right-section .witness-paragraph:last-of-type {
                font-size: 10px !important;      /* مطابقة المعاينة */
                line-height: 1.0 !important;     /* مطابقة المعاينة */
                margin-bottom: 0.3mm !important; /* مطابقة المعاينة */
                margin-top: 0.5mm !important;    /* مطابقة المعاينة */
            }

            .witness-value {
                min-height: 12px !important;     /* مطابقة المعاينة */
                font-size: 11px !important;      /* مطابقة المعاينة */
                flex: 0.5 !important;
                padding: 0.5px !important;       /* مطابقة المعاينة */
                border-bottom: 1px dotted #333 !important; /* مطابقة المعاينة */
                font-weight: 700 !important;
                color: var(--text-dark) !important;
                background: transparent !important;
                margin-left: 1px !important;     /* مطابقة المعاينة */
            }

            .witness-paragraph {
                font-size: 12px !important;      /* مطابقة المعاينة */
                line-height: 1.1 !important;     /* مطابقة المعاينة */
                margin-bottom: 0.5mm !important; /* مطابقة المعاينة */
                text-align: justify !important;
                color: var(--text-dark) !important;
                font-weight: 600 !important;
                margin-top: 0 !important;
            }

            .witness-label {
                font-size: 12px !important;      /* مطابقة المعاينة */
                margin-bottom: 0.3mm !important; /* مطابقة المعاينة */
                font-weight: 600 !important;
                color: var(--text-dark) !important;
                margin-left: 1mm !important;     /* مطابقة المعاينة */
            }

            .signature-text {
                font-size: 12px !important;      /* مطابقة المعاينة */
                margin-top: 1mm !important;      /* مطابقة المعاينة */
                margin-bottom: 0 !important;
                font-weight: 600 !important;
                text-align: center !important;
                color: var(--text-dark) !important;
            }

            .inline-field {
                min-width: 20mm !important;      /* مطابقة المعاينة */
                height: 14px !important;         /* مطابقة المعاينة */
                font-size: 11px !important;      /* مطابقة المعاينة */
                display: inline-block !important;
                border-bottom: 1px dotted #333 !important; /* مطابقة المعاينة */
                margin: 0 1px !important;        /* مطابقة المعاينة */
                font-weight: 700 !important;
            }

            .french-label, .hijri-label {
                font-size: 10px !important;      /* مطابقة المعاينة */
                margin-right: 0.5mm !important;  /* مطابقة المعاينة */
                margin-left: 0.5mm !important;   /* مطابقة المعاينة */
                font-weight: 600 !important;
                color: var(--text-dark) !important;
            }

            hr {
                margin: 0.5mm 0 !important;      /* مطابقة المعاينة */
                border: none !important;
                height: 1px !important;
                background: #333 !important;
            }

            /* مطابقة قص خط ساكن حاليا ب */
            #currentAddress {
                width: calc(50% - 15mm) !important; /* مطابقة المعاينة */
                flex: none !important;
            }

            /* مطابقة البيانات الهامشية */
            .marginal-x-marks {
                font-family: monospace !important;
                letter-spacing: 1px !important;
                font-weight: 500 !important;
                color: var(--text-dark) !important;
                margin-right: 2mm !important;
            }

            #marginalData {
                flex: 1 !important;
                padding: var(--spacing-xs) !important;
                border-bottom: 1px dotted #333 !important;
                min-height: 20px !important;
                font-weight: 500 !important;
                color: var(--text-dark) !important;
                background: transparent !important;
            }
            .card-header::before {
                display: none;
            }

            .field-value {
                border-bottom: 1px solid #000 !important;
                background: transparent !important;
            }

            .field-label {
                font-weight: bold !important;
            }
        }

        /* ===== أنماط الاستجابة ===== */
        @media (max-width: 768px) {
            .id-card {
                width: 95vw;
                height: auto;
                min-height: 60vh;
            }

            .field-row {
                flex-direction: column;
                gap: var(--spacing-xs);
            }

            .field-arabic, .field-french {
                width: 100%;
            }
        }

        /* ===== حالة التحميل ===== */
        .loading {
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--text-light);
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--morocco-red);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: var(--spacing-sm);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- الورقة الموحدة -->
    <div class="document">
        <!-- Header -->
        <div class="header">
            <!-- القسم الأول - اليسار -->
            <div class="header-left">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
            </div>

            <!-- القسم الثاني - الوسط -->
            <div class="header-center">
                <div>بطاقة شخصية للحالة المدنية</div>
                <div>تقوم مقام موجز عقد الولادة، طبقا للظهير الشريف</div>
                <div>الصادر في 03 أكتوبر 2002</div>
                <div>بإحداث نظام الحالة المدنية</div>
            </div>

            <!-- القسم الثالث - اليمين -->
            <div class="header-right">
                <div>Royaume du Maroc</div>
                <div>Ministère de l'Intérieur</div>
                <div>Région Marrakech - Safi</div>
                <div>Province de Safi</div>
                <div>Commune Ayir</div>
                <div>Bureau Etat Civil</div>
            </div>
        </div>

        <!-- قسم المعلومات الشخصية -->
    <div class="personal-info">
        <!-- الإسم الشخصي -->
        <div class="info-row">
            <span class="info-label">الإسم الشخصي:</span> <span class="info-value" id="firstName"></span>
            <span class="french-label">Prénom: <span id="firstNameFr"></span></span>
        </div>

        <!-- الاسم العائلي -->
        <div class="info-row">
            <span class="info-label">الاسم العائلي:</span> <span class="info-value" id="familyName"></span>
            <span class="french-label">Nom: <span id="familyNameFr"></span></span>
        </div>

        <!-- تاريخ الولادة -->
        <div class="info-row">
            <span class="info-label">تاريخ الولادة:</span> <span class="info-value" id="birthDate"></span>
            <span class="hijri-label">هجري</span>
        </div>

        <!-- موافق -->
        <div class="info-row">
            <span class="info-label">موافق:</span> <span class="info-value" id="corresponding"></span>
            <span class="hijri-label">ميلادي</span>
        </div>

        <!-- مكان الولادة -->
        <div class="info-row">
            <span class="info-label">مكان الولادة:</span> <span class="info-value" id="birthPlace"></span>
        </div>

        <!-- والده(ا) -->
        <div class="info-row">
            <span class="info-label">والده(ا):</span> <span class="info-value" id="father"></span>
        </div>

        <!-- والدته(ا) -->
        <div class="info-row">
            <span class="info-label">والدته(ا):</span> <span class="info-value" id="mother"></span>
        </div>

        <!-- ساكن حاليا ب -->
        <div class="info-row">
            <span class="info-label">ساكن حاليا ب:</span> <span class="info-value" id="currentAddress"></span>
            <span class="location-label">جماعة أيير، إقليم آسفي</span>
        </div>

        <!-- البيانات الهامشية -->
        <div class="info-row marginal-row">
            <span class="info-label">البيانات الهامشية:</span> <span class="marginal-x-marks"></span> <span class="info-value" id="marginalData"></span>
        </div>

        <!-- سطر فاصل عادي -->
        <hr>

        <!-- الجزء الفارغ مقسم إلى جزأين -->
        <div class="bottom-section">
            <div class="left-section">
                <div class="witness-paragraph">
                    يشهد ضابط الحالة المدنية لجماعة أيير الموقع أسفله بمطابقة المعلومات الواردة في هذه البطاقة لكناش التعريف والحالة المدنية أو الدفتر العائلي أو بوت. موجز عقد الولادة رقم <span class="inline-field" id="actNumber"></span> سنة <span class="inline-field" id="actYear"></span> المسلم من مكتب الحالة المدنية لجماعة <span class="inline-field" id="issuingCommune"></span><br>
                    بتاريخ <span class="inline-field" id="issueDate"></span>
                </div>

                <div class="info-row" style="margin-left: 20mm;">
                    <span class="witness-label">أيير في:</span> <span class="info-value witness-value" id="certificationDate"></span>
                </div>

                <div class="signature-text">
                    الإمضاء وطابع المكتب
                </div>
            </div>
            <div class="right-section">
                <div class="info-row">
                    <span class="witness-label">أنا الموقع أسفله:</span> <span class="info-value witness-value" id="witnessName1"></span>
                </div>
                <div class="info-row">
                    <span class="witness-label">الساكن حاليا ب:</span> <span class="info-value witness-value" id="witnessAddress1"></span>
                </div>
                <div class="witness-paragraph">
                    أشهد بصحة المعلومات الواردة في هذه البطاقة.
                </div>
                <div class="witness-label">
                    إمضاء أو بصمة صاحب الطلب
                </div>

                <div class="witness-paragraph">
                    يعاقب بناء علي الفصل 663 من القانون الجنائي بالحبس من 6 أشهر إلي عامين وبغرامة من 120 إلي 1000 درهم أو بإحدي هاتين العقوبتين فقط من صنع عن علم شهادة تتضمن وقائع غير صحيحة أو زور أو عدل بأية وسيلة كانت، شهادة صحيحة الأصل ما لم يكن الفعل جريمة أشد
                </div>
            </div>
        </div>
    </div>
    <!-- إغلاق الورقة الموحدة -->

    <!-- أزرار التحكم -->
    <div class="control-buttons">
        <button onclick="printPage()" class="print-btn">
            🖨️ طباعة
        </button>
        <button onclick="window.location.href='main-dashboard.html'" class="home-btn">
            🏠 الصفحة الرئيسية
        </button>
    </div>

    <script>
        function printPage() {
            // حفظ العنوان الأصلي
            const originalTitle = document.title;

            // تغيير العنوان إلى فارغ أثناء الطباعة
            document.title = '';

            // طباعة الصفحة
            window.print();

            // إعادة العنوان الأصلي بعد الطباعة
            setTimeout(() => {
                document.title = originalTitle;
            }, 1000);
        }

        // دالة تحويل الأرقام إلى عربية
        function convertToArabicNumbers(str) {
            if (!str) return str;
            const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return str.toString().replace(/[0-9]/g, function(match) {
                return arabicNumbers[parseInt(match)];
            });
        }

        // ملء النموذج بالبيانات من URL أو بيانات تجريبية
        window.onload = function() {
            // استخراج البيانات من URL
            const urlParams = new URLSearchParams(window.location.search);

            // إذا وجدت بيانات في URL
            if (urlParams.has('firstNameAr')) {
                // ملء البيانات من URL
                document.getElementById('firstName').innerHTML = urlParams.get('firstNameAr') || '';
                document.getElementById('firstNameFr').innerHTML = urlParams.get('firstNameFr') || '';
                document.getElementById('familyName').innerHTML = urlParams.get('familyNameAr') || '';
                document.getElementById('familyNameFr').innerHTML = urlParams.get('familyNameFr') || '';

                // تاريخ الولادة: هجري أولاً
                document.getElementById('birthDate').innerHTML = urlParams.get('birthDateHijri') || '';

                // موافق: ميلادي ثانياً
                const gregorianDate = urlParams.get('birthDateGregorian');
                if (gregorianDate) {
                    // تحويل من YYYY-MM-DD إلى DD/MM/YYYY
                    const dateParts = gregorianDate.split('-');
                    if (dateParts.length === 3) {
                        document.getElementById('corresponding').innerHTML = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
                    } else {
                        document.getElementById('corresponding').innerHTML = gregorianDate;
                    }
                } else {
                    document.getElementById('corresponding').innerHTML = '';
                }

                document.getElementById('birthPlace').innerHTML = (urlParams.get('birthPlaceAr') || '') + ' / ' + (urlParams.get('birthPlaceFr') || '');
                document.getElementById('father').innerHTML = urlParams.get('fatherName') || '';
                document.getElementById('mother').innerHTML = urlParams.get('motherName') || '';
                document.getElementById('currentAddress').innerHTML = urlParams.get('currentAddressAr') || '';
                document.getElementById('marginalData').innerHTML = urlParams.get('marginalData') || '';

                // ملء بيانات القسم السفلي بأرقام فرنسية
                document.getElementById('actNumber').innerHTML = urlParams.get('actNumber') || '';
                document.getElementById('actYear').innerHTML = urlParams.get('actYear') || new Date().getFullYear();
                document.getElementById('issuingCommune').innerHTML = urlParams.get('issuingCommune') || 'أيير';
                document.getElementById('issueDate').innerHTML = urlParams.get('issueDate') || new Date().toLocaleDateString('en-US');
                document.getElementById('certificationDate').innerHTML = urlParams.get('certificationDate') || new Date().toLocaleDateString('en-US');

                // بيانات الشاهد
                document.getElementById('witnessName1').innerHTML = urlParams.get('witnessName') || '';
                document.getElementById('witnessAddress1').innerHTML = urlParams.get('witnessAddress') || '';
            } else {
                // بيانات تجريبية إذا لم توجد بيانات في URL
                document.getElementById('firstName').innerHTML = 'محمد';
                document.getElementById('firstNameFr').innerHTML = 'Mohamed';
                document.getElementById('familyName').innerHTML = 'العلوي';
                document.getElementById('familyNameFr').innerHTML = 'Alaoui';
                document.getElementById('birthDate').innerHTML = '25/06/1411'; // هجري
                document.getElementById('corresponding').innerHTML = '15/03/1990'; // ميلادي
                document.getElementById('birthPlace').innerHTML = 'أيير / Ayir';
                document.getElementById('father').innerHTML = 'عبد الله العلوي';
                document.getElementById('mother').innerHTML = 'فاطمة الزهراني';
                document.getElementById('currentAddress').innerHTML = 'حي السلام';
                document.getElementById('marginalData').innerHTML = '';

                // بيانات تجريبية للقسم السفلي بأرقام فرنسية
                document.getElementById('actNumber').innerHTML = '15/2024';
                document.getElementById('actYear').innerHTML = '2024';
                document.getElementById('issuingCommune').innerHTML = 'أيير';
                document.getElementById('issueDate').innerHTML = new Date().toLocaleDateString('en-US');
                document.getElementById('certificationDate').innerHTML = new Date().toLocaleDateString('en-US');

                // بيانات تجريبية للشاهد
                document.getElementById('witnessName1').innerHTML = 'عبد الرحمن العلوي';
                document.getElementById('witnessAddress1').innerHTML = 'حي النهضة أيير';
            }
        }
    </script>

</body>
</html>