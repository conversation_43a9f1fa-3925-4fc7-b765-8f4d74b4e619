// قاعدة بيانات تسجيل الدخول - منفصلة تماماً
class LoginDB {
    constructor() {
        this.dbName = 'LoginDatabase';
        this.version = 1;
        this.db = null;
        this.isInitialized = false;
        this.fallbackMode = false;
    }

    // تهيئة قاعدة البيانات
    async init() {
        try {
            if (!window.indexedDB) {
                console.warn('IndexedDB غير مدعوم، سيتم استخدام التخزين المحلي');
                this.fallbackMode = true;
                await this.initLocalStorageFallback();
                return true;
            }

            console.log('🔧 تهيئة قاعدة بيانات تسجيل الدخول...');
            await this.initIndexedDB();
            this.isInitialized = true;
            console.log('✅ تم تهيئة قاعدة بيانات تسجيل الدخول بنجاح');

            // إنشاء مستخدم افتراضي إذا لم يكن موجود
            await this.createDefaultUser();

            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة بيانات تسجيل الدخول:', error);
            console.log('🔄 التبديل إلى وضع التخزين المحلي...');
            
            try {
                this.fallbackMode = true;
                await this.initLocalStorageFallback();
                await this.createDefaultUser();
                console.log('✅ تم تهيئة التخزين المحلي بنجاح كبديل');
                return true;
            } catch (fallbackError) {
                console.error('❌ فشل في تهيئة التخزين المحلي أيضاً:', fallbackError);
                throw new Error('فشل في تهيئة أي نوع من أنواع التخزين');
            }
        }
    }

    // تهيئة IndexedDB
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('❌ خطأ في فتح قاعدة بيانات تسجيل الدخول:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('✅ تم فتح قاعدة بيانات تسجيل الدخول بنجاح');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                console.log('🔄 إنشاء/تحديث قاعدة بيانات تسجيل الدخول...');

                // إنشاء جدول المستخدمين
                if (!this.db.objectStoreNames.contains('users')) {
                    const usersStore = this.db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
                    
                    // إنشاء فهارس للبحث السريع
                    usersStore.createIndex('username', 'username', { unique: true });
                    usersStore.createIndex('email', 'email', { unique: true });
                    usersStore.createIndex('role', 'role', { unique: false });
                    usersStore.createIndex('createdAt', 'createdAt', { unique: false });
                    
                    console.log('✅ تم إنشاء جدول المستخدمين');
                }

                // إنشاء جدول سجل النشاطات
                if (!this.db.objectStoreNames.contains('activities')) {
                    const activitiesStore = this.db.createObjectStore('activities', { keyPath: 'id', autoIncrement: true });
                    
                    // إنشاء فهارس
                    activitiesStore.createIndex('userId', 'userId', { unique: false });
                    activitiesStore.createIndex('action', 'action', { unique: false });
                    activitiesStore.createIndex('timestamp', 'timestamp', { unique: false });
                    
                    console.log('✅ تم إنشاء جدول النشاطات');
                }

                // إنشاء جدول الجلسات
                if (!this.db.objectStoreNames.contains('sessions')) {
                    const sessionsStore = this.db.createObjectStore('sessions', { keyPath: 'id', autoIncrement: true });
                    
                    // إنشاء فهارس
                    sessionsStore.createIndex('userId', 'userId', { unique: false });
                    sessionsStore.createIndex('token', 'token', { unique: true });
                    sessionsStore.createIndex('expiresAt', 'expiresAt', { unique: false });
                    
                    console.log('✅ تم إنشاء جدول الجلسات');
                }
            };
        });
    }

    // تهيئة التخزين المحلي كبديل
    async initLocalStorageFallback() {
        try {
            const testKey = 'test_login_storage_' + Date.now();
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            
            console.log('✅ تم تهيئة التخزين المحلي لتسجيل الدخول');
            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة التخزين المحلي:', error);
            throw new Error('لا يمكن تهيئة التخزين المحلي');
        }
    }

    // إنشاء مستخدم افتراضي
    async createDefaultUser() {
        try {
            // التحقق من وجود مستخدم افتراضي
            const existingUser = await this.getUserByUsername('admin');
            
            if (!existingUser) {
                const defaultUser = {
                    username: 'admin',
                    password: await this.hashPassword('admin123'), // كلمة مرور مشفرة
                    email: '<EMAIL>',
                    fullName: 'مدير النظام',
                    firstName: 'مدير',
                    lastName: 'النظام',
                    phone: null,
                    role: 'admin',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };

                await this.addUser(defaultUser);
                console.log('✅ تم إنشاء المستخدم الافتراضي: admin / admin123');
            }
        } catch (error) {
            console.error('❌ خطأ في إنشاء المستخدم الافتراضي:', error);
        }
    }

    // تشفير كلمة المرور (تشفير بسيط للتطوير)
    async hashPassword(password) {
        // في بيئة الإنتاج، استخدم تشفير أقوى مثل bcrypt
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'salt_key_2024');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // إضافة مستخدم جديد
    async addUser(user) {
        if (this.fallbackMode) {
            return this.addUserLocalStorage(user);
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');
            
            // إضافة timestamp إذا لم يكن موجود
            if (!user.createdAt) {
                user.createdAt = new Date().toISOString();
            }

            const request = store.add(user);

            request.onsuccess = () => {
                console.log(`✅ تم إضافة المستخدم: ${user.username}`);
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('❌ خطأ في إضافة المستخدم:', request.error);
                if (request.error.name === 'ConstraintError') {
                    reject(new Error(`اسم المستخدم ${user.username} موجود بالفعل`));
                } else {
                    reject(request.error);
                }
            };
        });
    }

    // إضافة مستخدم في التخزين المحلي
    async addUserLocalStorage(user) {
        try {
            const users = JSON.parse(localStorage.getItem('login_users') || '[]');
            
            // التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
            const existingUser = users.find(u => u.username === user.username || u.email === user.email);
            if (existingUser) {
                throw new Error(`اسم المستخدم أو البريد الإلكتروني موجود بالفعل`);
            }

            // إضافة ID تلقائي
            user.id = users.length > 0 ? Math.max(...users.map(u => u.id || 0)) + 1 : 1;
            
            if (!user.createdAt) {
                user.createdAt = new Date().toISOString();
            }

            users.push(user);
            localStorage.setItem('login_users', JSON.stringify(users));
            
            console.log(`✅ تم إضافة المستخدم في التخزين المحلي: ${user.username}`);
            return user.id;
        } catch (error) {
            console.error('❌ خطأ في إضافة المستخدم في التخزين المحلي:', error);
            throw error;
        }
    }

    // البحث عن مستخدم بواسطة اسم المستخدم
    async getUserByUsername(username) {
        if (this.fallbackMode) {
            return this.getUserByUsernameLocalStorage(username);
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');
            const index = store.index('username');
            const request = index.get(username);

            request.onsuccess = () => {
                resolve(request.result || null);
            };

            request.onerror = () => {
                console.error('❌ خطأ في البحث عن المستخدم:', request.error);
                reject(request.error);
            };
        });
    }

    // البحث عن مستخدم في التخزين المحلي
    async getUserByUsernameLocalStorage(username) {
        try {
            const users = JSON.parse(localStorage.getItem('login_users') || '[]');
            return users.find(user => user.username === username) || null;
        } catch (error) {
            console.error('❌ خطأ في البحث في التخزين المحلي:', error);
            return null;
        }
    }

    // التحقق من صحة بيانات المستخدم
    async authenticateUser(username, password) {
        try {
            const user = await this.getUserByUsername(username);
            
            if (!user) {
                console.log('❌ المستخدم غير موجود:', username);
                return null;
            }

            if (!user.isActive) {
                console.log('❌ المستخدم غير نشط:', username);
                return null;
            }

            const hashedPassword = await this.hashPassword(password);
            
            if (user.password === hashedPassword) {
                // تحديث آخر تسجيل دخول
                user.lastLogin = new Date().toISOString();
                await this.updateUser(user);
                
                console.log('✅ تم التحقق من المستخدم بنجاح:', username);
                
                // إرجاع بيانات المستخدم بدون كلمة المرور
                const { password: _, ...userWithoutPassword } = user;
                return userWithoutPassword;
            } else {
                console.log('❌ كلمة المرور غير صحيحة للمستخدم:', username);
                return null;
            }
        } catch (error) {
            console.error('❌ خطأ في التحقق من المستخدم:', error);
            throw error;
        }
    }

    // تحديث بيانات المستخدم
    async updateUser(user) {
        if (this.fallbackMode) {
            return this.updateUserLocalStorage(user);
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');
            
            user.updatedAt = new Date().toISOString();
            const request = store.put(user);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('❌ خطأ في تحديث المستخدم:', request.error);
                reject(request.error);
            };
        });
    }

    // تحديث مستخدم في التخزين المحلي
    async updateUserLocalStorage(user) {
        try {
            const users = JSON.parse(localStorage.getItem('login_users') || '[]');
            const userIndex = users.findIndex(u => u.id === user.id);
            
            if (userIndex === -1) {
                throw new Error(`المستخدم غير موجود: ${user.id}`);
            }

            user.updatedAt = new Date().toISOString();
            users[userIndex] = user;
            localStorage.setItem('login_users', JSON.stringify(users));
            
            return user.id;
        } catch (error) {
            console.error('❌ خطأ في تحديث المستخدم في التخزين المحلي:', error);
            throw error;
        }
    }

    // تسجيل نشاط المستخدم
    async logUserActivity(userId, action, details = null) {
        const activity = {
            userId: userId,
            action: action,
            details: details,
            timestamp: new Date().toISOString(),
            ipAddress: 'localhost', // في بيئة الإنتاج، احصل على IP الحقيقي
            userAgent: navigator.userAgent
        };

        if (this.fallbackMode) {
            return this.logUserActivityLocalStorage(activity);
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['activities'], 'readwrite');
            const store = transaction.objectStore('activities');
            const request = store.add(activity);

            request.onsuccess = () => {
                console.log(`📝 تم تسجيل النشاط: ${action} للمستخدم ${userId}`);
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('❌ خطأ في تسجيل النشاط:', request.error);
                reject(request.error);
            };
        });
    }

    // تسجيل نشاط في التخزين المحلي
    async logUserActivityLocalStorage(activity) {
        try {
            const activities = JSON.parse(localStorage.getItem('login_activities') || '[]');
            
            activity.id = activities.length > 0 ? Math.max(...activities.map(a => a.id || 0)) + 1 : 1;
            activities.push(activity);
            
            // الاحتفاظ بآخر 1000 نشاط فقط
            if (activities.length > 1000) {
                activities.splice(0, activities.length - 1000);
            }
            
            localStorage.setItem('login_activities', JSON.stringify(activities));
            console.log(`📝 تم تسجيل النشاط في التخزين المحلي: ${activity.action}`);
            return activity.id;
        } catch (error) {
            console.error('❌ خطأ في تسجيل النشاط في التخزين المحلي:', error);
            throw error;
        }
    }

    // الحصول على جميع المستخدمين (للإدارة)
    async getAllUsers() {
        if (this.fallbackMode) {
            const users = JSON.parse(localStorage.getItem('login_users') || '[]');
            return users.map(({ password, ...user }) => user); // إزالة كلمات المرور
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');
            const request = store.getAll();

            request.onsuccess = () => {
                const users = request.result.map(({ password, ...user }) => user);
                resolve(users);
            };

            request.onerror = () => {
                console.error('❌ خطأ في جلب المستخدمين:', request.error);
                reject(request.error);
            };
        });
    }

    // الحصول على إحصائيات تسجيل الدخول
    async getLoginStats() {
        try {
            const users = await this.getAllUsers();
            const totalUsers = users.length;
            const activeUsers = users.filter(u => u.isActive).length;
            const adminUsers = users.filter(u => u.role === 'admin').length;
            
            return {
                totalUsers,
                activeUsers,
                adminUsers,
                inactiveUsers: totalUsers - activeUsers
            };
        } catch (error) {
            console.error('❌ خطأ في جلب الإحصائيات:', error);
            return {
                totalUsers: 0,
                activeUsers: 0,
                adminUsers: 0,
                inactiveUsers: 0
            };
        }
    }
}
