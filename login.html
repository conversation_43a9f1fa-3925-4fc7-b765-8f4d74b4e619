<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مكتب الحالة المدنية أيير</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇲🇦</text></svg>">
    
    <style>
        /* ===== إعدادات عامة ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* ألوان المملكة المغربية */
            --morocco-red: #C1272D;
            --morocco-green: #006233;
            --morocco-gold: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #bdc3c7;
            --background-light: #f8f9fa;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);
            
            /* خطوط */
            --font-primary: 'Segoe UI', 'Amiri', 'Times New Roman', serif;
            --font-secondary: 'Arial', 'Tahoma', sans-serif;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: var(--text-dark);
            line-height: 1.6;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--morocco-green) 0%, var(--morocco-red) 50%, var(--morocco-green) 100%);
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .morocco-emblem {
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, var(--morocco-gold) 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            border: 3px solid rgba(196, 30, 58, 0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
            margin: 0 auto 20px;
        }

        .login-title {
            color: var(--morocco-red);
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .login-subtitle {
            color: var(--text-light);
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .login-department {
            color: var(--morocco-green);
            font-size: 0.95em;
            font-weight: 600;
        }

        .login-form {
            margin-top: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            font-family: var(--font-primary);
            transition: all 0.3s ease;
            background: white;
            direction: rtl;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--morocco-green);
            box-shadow: 0 0 0 3px rgba(0, 98, 51, 0.1);
            transform: translateY(-2px);
        }

        .form-group input:invalid {
            border-color: var(--morocco-red);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2em;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--morocco-green);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            font-size: 0.9em;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remember-me input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .forgot-password {
            color: var(--morocco-red);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: var(--morocco-green);
            text-decoration: underline;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--morocco-red), #8b0000);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(196, 30, 58, 0.4);
            background: linear-gradient(135deg, #8b0000, var(--morocco-red));
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .register-link {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .register-link a {
            color: var(--morocco-green);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .register-link a:hover {
            color: var(--morocco-red);
            text-decoration: underline;
        }

        /* رسائل التنبيه */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
            display: none;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(0, 98, 51, 0.1), rgba(0, 77, 38, 0.1));
            color: var(--morocco-green);
            border: 1px solid rgba(0, 98, 51, 0.3);
        }

        .alert-error {
            background: linear-gradient(135deg, rgba(196, 30, 58, 0.1), rgba(139, 0, 0, 0.1));
            color: var(--morocco-red);
            border: 1px solid rgba(196, 30, 58, 0.3);
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .login-title {
                font-size: 1.8em;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 2em;
            }

            .remember-forgot {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }

        /* تأثيرات التحميل */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="morocco-emblem">🇲🇦</div>
            <h1 class="login-title">تسجيل الدخول</h1>
            <p class="login-subtitle">مكتب الحالة المدنية - أيير</p>
            <p class="login-department">قسم التسجيل والتوثيق</p>
        </div>

        <div id="alertContainer"></div>

        <form class="login-form" id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required placeholder="أدخل اسم المستخدم">
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                    <button type="button" class="password-toggle" onclick="togglePassword()">👁️</button>
                </div>
            </div>

            <div class="remember-forgot">
                <div class="remember-me">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    <label for="rememberMe">تذكرني</label>
                </div>
                <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span id="loginBtnText">تسجيل الدخول</span>
            </button>
        </form>

        <div class="register-link">
            <p>ليس لديك حساب؟ <a href="#" onclick="showRegister()">إنشاء حساب جديد</a></p>
        </div>
    </div>

    <script src="login-db.js"></script>
    <script>
        // إدارة تسجيل الدخول
        class LoginManager {
            constructor() {
                this.loginDB = new LoginDB();
                this.init();
            }

            async init() {
                await this.loginDB.init();
                this.setupEventListeners();
                this.checkRememberedUser();
            }

            setupEventListeners() {
                document.getElementById('loginForm').addEventListener('submit', (e) => this.handleLogin(e));
            }

            async handleLogin(event) {
                event.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('rememberMe').checked;

                if (!username || !password) {
                    this.showAlert('يرجى ملء جميع الحقول', 'error');
                    return;
                }

                this.setLoading(true);

                try {
                    const user = await this.loginDB.authenticateUser(username, password);
                    
                    if (user) {
                        // تسجيل دخول ناجح
                        await this.loginDB.logUserActivity(user.id, 'login');
                        
                        if (rememberMe) {
                            localStorage.setItem('rememberedUser', username);
                        } else {
                            localStorage.removeItem('rememberedUser');
                        }

                        // حفظ جلسة المستخدم
                        sessionStorage.setItem('currentUser', JSON.stringify(user));
                        
                        this.showAlert('تم تسجيل الدخول بنجاح! سيتم توجيهك للصفحة الرئيسية...', 'success');

                        // إعادة توجيه للصفحة الرئيسية بعد ثانيتين
                        setTimeout(() => {
                            window.location.href = 'main-dashboard.html';
                        }, 2000);
                        
                    } else {
                        this.showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
                    }
                } catch (error) {
                    console.error('خطأ في تسجيل الدخول:', error);
                    this.showAlert('حدث خطأ أثناء تسجيل الدخول', 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            checkRememberedUser() {
                const rememberedUser = localStorage.getItem('rememberedUser');
                if (rememberedUser) {
                    document.getElementById('username').value = rememberedUser;
                    document.getElementById('rememberMe').checked = true;
                }
            }

            setLoading(loading) {
                const btn = document.getElementById('loginBtn');
                const btnText = document.getElementById('loginBtnText');
                
                if (loading) {
                    btn.disabled = true;
                    btnText.innerHTML = 'جاري تسجيل الدخول... <span class="loading"></span>';
                } else {
                    btn.disabled = false;
                    btnText.textContent = 'تسجيل الدخول';
                }
            }

            showAlert(message, type) {
                const alertContainer = document.getElementById('alertContainer');
                const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
                
                alertContainer.innerHTML = `
                    <div class="alert ${alertClass}" style="display: block;">
                        ${message}
                    </div>
                `;

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // وظائف مساعدة
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        function showForgotPassword() {
            window.location.href = 'forgot-password.html';
        }

        function showRegister() {
            window.location.href = 'register.html';
        }

        // تهيئة مدير تسجيل الدخول
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });
    </script>
</body>
</html>
