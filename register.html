<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - مكتب الحالة المدنية أيير</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇲🇦</text></svg>">
    
    <style>
        /* ===== إعدادات عامة ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* ألوان المملكة المغربية */
            --morocco-red: #C1272D;
            --morocco-green: #006233;
            --morocco-gold: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #bdc3c7;
            --background-light: #f8f9fa;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);
            
            /* خطوط */
            --font-primary: 'Segoe UI', 'Amiri', 'Times New Roman', serif;
            --font-secondary: 'Arial', 'Tahoma', sans-serif;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: var(--text-dark);
            line-height: 1.6;
            padding: 20px;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            width: 100%;
            max-width: 500px;
            position: relative;
            overflow: hidden;
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--morocco-green) 0%, var(--morocco-red) 50%, var(--morocco-green) 100%);
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: radial-gradient(circle, var(--morocco-gold) 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2em;
            border: 3px solid rgba(196, 30, 58, 0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
            margin: 0 auto 15px;
        }

        .register-title {
            color: var(--morocco-red);
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .register-subtitle {
            color: var(--text-light);
            font-size: 1em;
            margin-bottom: 8px;
        }

        .register-department {
            color: var(--morocco-green);
            font-size: 0.9em;
            font-weight: 600;
        }

        .register-form {
            margin-top: 25px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-row.single {
            grid-template-columns: 1fr;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 6px;
            font-size: 1em;
        }

        .required {
            color: var(--morocco-red);
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 0.95rem;
            font-family: var(--font-primary);
            transition: all 0.3s ease;
            background: white;
            direction: rtl;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--morocco-green);
            box-shadow: 0 0 0 3px rgba(0, 98, 51, 0.1);
            transform: translateY(-2px);
        }

        .form-group input:invalid {
            border-color: var(--morocco-red);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1em;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--morocco-green);
        }

        .password-strength {
            margin-top: 5px;
            font-size: 0.8em;
            display: none;
        }

        .strength-weak { color: var(--morocco-red); }
        .strength-medium { color: #f39c12; }
        .strength-strong { color: var(--morocco-green); }

        .terms-container {
            margin: 20px 0;
            padding: 15px;
            background: rgba(0, 98, 51, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(0, 98, 51, 0.2);
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .terms-checkbox input[type="checkbox"] {
            width: auto;
            margin: 0;
            margin-top: 3px;
        }

        .terms-text {
            font-size: 0.9em;
            line-height: 1.5;
        }

        .terms-link {
            color: var(--morocco-red);
            text-decoration: none;
            font-weight: 600;
        }

        .terms-link:hover {
            text-decoration: underline;
        }

        .register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--morocco-green), #004d26);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-top: 10px;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .register-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 98, 51, 0.4);
            background: linear-gradient(135deg, #004d26, var(--morocco-green));
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .login-link a {
            color: var(--morocco-red);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: var(--morocco-green);
            text-decoration: underline;
        }

        /* رسائل التنبيه */
        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: 500;
            display: none;
            font-size: 0.9em;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(0, 98, 51, 0.1), rgba(0, 77, 38, 0.1));
            color: var(--morocco-green);
            border: 1px solid rgba(0, 98, 51, 0.3);
        }

        .alert-error {
            background: linear-gradient(135deg, rgba(196, 30, 58, 0.1), rgba(139, 0, 0, 0.1));
            color: var(--morocco-red);
            border: 1px solid rgba(196, 30, 58, 0.3);
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .register-container {
                padding: 25px 20px;
                margin: 10px;
            }

            .register-title {
                font-size: 1.7em;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 2em;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .form-group {
                margin-bottom: 15px;
            }
        }

        /* تأثيرات التحميل */
        .loading {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <div class="morocco-emblem">🇲🇦</div>
            <h1 class="register-title">إنشاء حساب جديد</h1>
            <p class="register-subtitle">مكتب الحالة المدنية - أيير</p>
            <p class="register-department">قسم التسجيل والتوثيق</p>
        </div>

        <div id="alertContainer"></div>

        <form class="register-form" id="registerForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">الاسم الأول <span class="required">*</span>:</label>
                    <input type="text" id="firstName" name="firstName" required placeholder="الاسم الأول">
                </div>
                <div class="form-group">
                    <label for="lastName">الاسم الأخير <span class="required">*</span>:</label>
                    <input type="text" id="lastName" name="lastName" required placeholder="الاسم الأخير">
                </div>
            </div>

            <div class="form-row single">
                <div class="form-group">
                    <label for="username">اسم المستخدم <span class="required">*</span>:</label>
                    <input type="text" id="username" name="username" required placeholder="اختر اسم مستخدم فريد" minlength="3">
                </div>
            </div>

            <div class="form-row single">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني <span class="required">*</span>:</label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="password">كلمة المرور <span class="required">*</span>:</label>
                    <div class="password-container">
                        <input type="password" id="password" name="password" required placeholder="كلمة مرور قوية" minlength="6">
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">👁️</button>
                    </div>
                    <div id="passwordStrength" class="password-strength"></div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور <span class="required">*</span>:</label>
                    <div class="password-container">
                        <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="أعد كتابة كلمة المرور">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">👁️</button>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="phone">رقم الهاتف:</label>
                    <input type="tel" id="phone" name="phone" placeholder="0612345678">
                </div>
                <div class="form-group">
                    <label for="role">الدور:</label>
                    <select id="role" name="role">
                        <option value="user">مستخدم عادي</option>
                        <option value="employee">موظف</option>
                        <option value="admin">مدير</option>
                    </select>
                </div>
            </div>

            <div class="terms-container">
                <div class="terms-checkbox">
                    <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                    <label for="agreeTerms" class="terms-text">
                        أوافق على <a href="#" class="terms-link" onclick="showTerms()">شروط الاستخدام</a> 
                        و <a href="#" class="terms-link" onclick="showPrivacy()">سياسة الخصوصية</a> 
                        الخاصة بمكتب الحالة المدنية
                    </label>
                </div>
            </div>

            <button type="submit" class="register-btn" id="registerBtn">
                <span id="registerBtnText">إنشاء الحساب</span>
            </button>
        </form>

        <div class="login-link">
            <p>لديك حساب بالفعل؟ <a href="login.html">تسجيل الدخول</a></p>
            <p style="margin-top: 10px; font-size: 0.9em;">
                <a href="forgot-password.html" style="color: var(--morocco-red);">نسيت كلمة المرور؟</a>
            </p>
        </div>
    </div>

    <script src="login-db.js"></script>
    <script>
        // إدارة إنشاء الحساب
        class RegisterManager {
            constructor() {
                this.loginDB = new LoginDB();
                this.init();
            }

            async init() {
                await this.loginDB.init();
                this.setupEventListeners();
            }

            setupEventListeners() {
                document.getElementById('registerForm').addEventListener('submit', (e) => this.handleRegister(e));
                document.getElementById('password').addEventListener('input', (e) => this.checkPasswordStrength(e.target.value));
                document.getElementById('confirmPassword').addEventListener('input', (e) => this.checkPasswordMatch());
                document.getElementById('username').addEventListener('blur', (e) => this.checkUsernameAvailability(e.target.value));
                document.getElementById('email').addEventListener('blur', (e) => this.checkEmailAvailability(e.target.value));
            }

            async handleRegister(event) {
                event.preventDefault();
                
                const formData = new FormData(event.target);
                const userData = Object.fromEntries(formData.entries());

                // التحقق من صحة البيانات
                if (!this.validateForm(userData)) {
                    return;
                }

                this.setLoading(true);

                try {
                    // إنشاء كائن المستخدم
                    const newUser = {
                        username: userData.username.trim(),
                        password: userData.password, // سيتم تشفيرها في قاعدة البيانات
                        email: userData.email.trim().toLowerCase(),
                        fullName: `${userData.firstName.trim()} ${userData.lastName.trim()}`,
                        firstName: userData.firstName.trim(),
                        lastName: userData.lastName.trim(),
                        phone: userData.phone?.trim() || null,
                        role: userData.role || 'user',
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    };

                    // تشفير كلمة المرور
                    newUser.password = await this.loginDB.hashPassword(userData.password);

                    // إضافة المستخدم إلى قاعدة البيانات
                    const userId = await this.loginDB.addUser(newUser);
                    
                    // تسجيل النشاط
                    await this.loginDB.logUserActivity(userId, 'register', {
                        username: newUser.username,
                        email: newUser.email,
                        role: newUser.role
                    });

                    this.showAlert('تم إنشاء الحساب بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...', 'success');
                    
                    // إعادة توجيه لصفحة تسجيل الدخول بعد 3 ثوان
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);

                } catch (error) {
                    console.error('خطأ في إنشاء الحساب:', error);
                    
                    if (error.message.includes('موجود بالفعل')) {
                        this.showAlert('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error');
                    } else {
                        this.showAlert('حدث خطأ أثناء إنشاء الحساب، يرجى المحاولة مرة أخرى', 'error');
                    }
                } finally {
                    this.setLoading(false);
                }
            }

            validateForm(userData) {
                // التحقق من الحقول المطلوبة
                if (!userData.firstName || !userData.lastName || !userData.username || !userData.email || !userData.password || !userData.confirmPassword) {
                    this.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return false;
                }

                // التحقق من طول اسم المستخدم
                if (userData.username.length < 3) {
                    this.showAlert('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
                    return false;
                }

                // التحقق من صحة البريد الإلكتروني
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(userData.email)) {
                    this.showAlert('يرجى إدخال بريد إلكتروني صحيح', 'error');
                    return false;
                }

                // التحقق من طول كلمة المرور
                if (userData.password.length < 6) {
                    this.showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                    return false;
                }

                // التحقق من تطابق كلمة المرور
                if (userData.password !== userData.confirmPassword) {
                    this.showAlert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'error');
                    return false;
                }

                // التحقق من الموافقة على الشروط
                if (!userData.agreeTerms) {
                    this.showAlert('يجب الموافقة على شروط الاستخدام', 'error');
                    return false;
                }

                return true;
            }

            checkPasswordStrength(password) {
                const strengthDiv = document.getElementById('passwordStrength');
                
                if (password.length === 0) {
                    strengthDiv.style.display = 'none';
                    return;
                }

                strengthDiv.style.display = 'block';
                
                let strength = 0;
                let feedback = [];

                // طول كلمة المرور
                if (password.length >= 8) strength++;
                else feedback.push('8 أحرف على الأقل');

                // أحرف كبيرة وصغيرة
                if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
                else feedback.push('أحرف كبيرة وصغيرة');

                // أرقام
                if (/\d/.test(password)) strength++;
                else feedback.push('أرقام');

                // رموز خاصة
                if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
                else feedback.push('رموز خاصة');

                if (strength < 2) {
                    strengthDiv.className = 'password-strength strength-weak';
                    strengthDiv.textContent = `ضعيفة - يحتاج: ${feedback.join(', ')}`;
                } else if (strength < 3) {
                    strengthDiv.className = 'password-strength strength-medium';
                    strengthDiv.textContent = `متوسطة - يحتاج: ${feedback.join(', ')}`;
                } else {
                    strengthDiv.className = 'password-strength strength-strong';
                    strengthDiv.textContent = 'قوية ✓';
                }
            }

            checkPasswordMatch() {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const confirmInput = document.getElementById('confirmPassword');

                if (confirmPassword.length > 0) {
                    if (password === confirmPassword) {
                        confirmInput.style.borderColor = 'var(--morocco-green)';
                    } else {
                        confirmInput.style.borderColor = 'var(--morocco-red)';
                    }
                }
            }

            async checkUsernameAvailability(username) {
                if (username.length < 3) return;

                try {
                    const existingUser = await this.loginDB.getUserByUsername(username);
                    const usernameInput = document.getElementById('username');
                    
                    if (existingUser) {
                        usernameInput.style.borderColor = 'var(--morocco-red)';
                        this.showAlert('اسم المستخدم غير متاح', 'error');
                    } else {
                        usernameInput.style.borderColor = 'var(--morocco-green)';
                    }
                } catch (error) {
                    console.error('خطأ في التحقق من اسم المستخدم:', error);
                }
            }

            async checkEmailAvailability(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) return;

                try {
                    const users = await this.loginDB.getAllUsers();
                    const existingUser = users.find(user => user.email === email.toLowerCase());
                    const emailInput = document.getElementById('email');
                    
                    if (existingUser) {
                        emailInput.style.borderColor = 'var(--morocco-red)';
                        this.showAlert('البريد الإلكتروني مستخدم بالفعل', 'error');
                    } else {
                        emailInput.style.borderColor = 'var(--morocco-green)';
                    }
                } catch (error) {
                    console.error('خطأ في التحقق من البريد الإلكتروني:', error);
                }
            }

            setLoading(loading) {
                const btn = document.getElementById('registerBtn');
                const btnText = document.getElementById('registerBtnText');
                
                if (loading) {
                    btn.disabled = true;
                    btnText.innerHTML = 'جاري إنشاء الحساب... <span class="loading"></span>';
                } else {
                    btn.disabled = false;
                    btnText.textContent = 'إنشاء الحساب';
                }
            }

            showAlert(message, type) {
                const alertContainer = document.getElementById('alertContainer');
                const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
                
                alertContainer.innerHTML = `
                    <div class="alert ${alertClass}" style="display: block;">
                        ${message}
                    </div>
                `;

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // وظائف مساعدة
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleBtn = passwordInput.nextElementSibling;
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        function showTerms() {
            alert('شروط الاستخدام:\n\n1. استخدام النظام للأغراض الرسمية فقط\n2. عدم مشاركة بيانات الدخول مع الآخرين\n3. الحفاظ على سرية المعلومات\n4. الإبلاغ عن أي مشاكل أمنية');
        }

        function showPrivacy() {
            alert('سياسة الخصوصية:\n\n1. حماية البيانات الشخصية\n2. عدم مشاركة المعلومات مع جهات خارجية\n3. تشفير البيانات الحساسة\n4. الحق في حذف البيانات');
        }

        // تهيئة مدير إنشاء الحساب
        document.addEventListener('DOMContentLoaded', () => {
            new RegisterManager();
        });
    </script>
</body>
</html>
