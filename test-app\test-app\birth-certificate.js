// Birth Certificate Management System - JavaScript

// Configuration for birth certificates
const BIRTH_CERT_CONFIG = {
    storageKey: 'test_birth_certificates',
    currentYear: new Date().getFullYear(),
    hijriYear: 1445, // Current Hijri year (approximate)
    regions: [
        'الرباط سلا القنيطرة',
        'الدار البيضاء سطات',
        'فاس مكناس',
        'مراكش آسفي',
        'طنجة تطوان الحسيمة',
        'الشرق',
        'بني ملال خنيفرة',
        'درعة تافيلالت',
        'سوس ماسة',
        'كلميم واد نون',
        'العيون الساقية الحمراء',
        'الداخلة وادي الذهب'
    ]
};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeBirthCertForm();
    setupFormValidation();
    setDefaultValues();
});

// Initialize birth certificate form
function initializeBirthCertForm() {
    console.log('Birth Certificate Form Initialized');

    // Set current date as default for declaration date
    const today = new Date().toISOString().split('T')[0];
    const declarationDateField = document.getElementById('declarationDate');
    if (declarationDateField) {
        declarationDateField.value = today;
    }

    // Set current year
    const gregorianYearField = document.getElementById('gregorianYear');
    if (gregorianYearField) {
        gregorianYearField.value = BIRTH_CERT_CONFIG.currentYear;
    }

    // Set Hijri year
    const hijriYearField = document.getElementById('hijriYear');
    if (hijriYearField) {
        hijriYearField.value = BIRTH_CERT_CONFIG.hijriYear;
    }
}

// Set default values
function setDefaultValues() {
    // Generate automatic act number
    generateActNumber();

    // Set default nationality
    document.getElementById('nationality').value = 'مغربية';
    document.getElementById('fatherNationality').value = 'مغربية';
    document.getElementById('motherNationality').value = 'مغربية';

    // Set default marital status
    document.getElementById('parentsMaritalStatus').value = 'متزوجان';

    // Set default declaration place
    document.getElementById('declarationPlace').value = 'مكتب الحالة المدنية';
}

// Generate automatic act number
function generateActNumber() {
    const certificates = getBirthCertificates();
    const currentYear = new Date().getFullYear();
    const yearCertificates = certificates.filter(cert =>
        new Date(cert.createdAt).getFullYear() === currentYear
    );

    const nextNumber = yearCertificates.length + 1;
    const actNumber = `${currentYear}/${nextNumber.toString().padStart(4, '0')}`;

    document.getElementById('actNumber').value = actNumber;
}

// Setup form validation
function setupFormValidation() {
    const form = document.getElementById('birthCertificateForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (validateForm()) {
            saveBirthCertificate();
        }
    });

    // Real-time validation
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });
    });
}

// Validate individual field
function validateField(field) {
    const value = field.value.trim();
    const isValid = value !== '';

    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
    }

    return isValid;
}

// Validate entire form
function validateForm() {
    const form = document.getElementById('birthCertificateForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    // Additional validations
    const birthDate = new Date(document.getElementById('birthDate').value);
    const today = new Date();

    if (birthDate > today) {
        showErrorMessage('تاريخ الازدياد لا يمكن أن يكون في المستقبل');
        isValid = false;
    }

    if (!isValid) {
        showErrorMessage('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
    }

    return isValid;
}

// Save birth certificate
function saveBirthCertificate() {
    try {
        const formData = collectFormData();
        const certificates = getBirthCertificates();

        // Add metadata
        formData.id = Date.now();
        formData.createdAt = new Date().toISOString();
        formData.type = 'عقد ازدياد';
        formData.status = 'مسجل';

        certificates.push(formData);
        localStorage.setItem(BIRTH_CERT_CONFIG.storageKey, JSON.stringify(certificates));

        showSuccessMessage('تم حفظ عقد الازدياد بنجاح');

        // Ask if user wants to print
        setTimeout(() => {
            if (confirm('هل تريد طباعة عقد الازدياد الآن؟')) {
                printBirthCertificate(formData);
            } else if (confirm('هل تريد إضافة عقد ازدياد جديد؟')) {
                resetForm();
                generateActNumber();
            } else {
                goBack();
            }
        }, 1500);

    } catch (error) {
        showErrorMessage('حدث خطأ أثناء حفظ البيانات: ' + error.message);
    }
}

// Collect form data
function collectFormData() {
    return {
        // Document info
        actNumber: document.getElementById('actNumber').value,
        hijriYear: document.getElementById('hijriYear').value,
        gregorianYear: document.getElementById('gregorianYear').value,

        // Child info
        firstName: document.getElementById('firstName').value,
        familyName: document.getElementById('familyName').value,
        birthDate: document.getElementById('birthDate').value,
        birthPlace: document.getElementById('birthPlace').value,
        gender: document.getElementById('gender').value,
        nationality: document.getElementById('nationality').value,
        parentsMaritalStatus: document.getElementById('parentsMaritalStatus').value,

        // Father info
        fatherName: document.getElementById('fatherName').value,
        fatherNationality: document.getElementById('fatherNationality').value,

        // Mother info
        motherName: document.getElementById('motherName').value,
        motherNationality: document.getElementById('motherNationality').value,

        // Declarant info
        declarantName: document.getElementById('declarantName').value,
        declarantRelation: document.getElementById('declarantRelation').value,
        declarationDate: document.getElementById('declarationDate').value,
        declarationPlace: document.getElementById('declarationPlace').value,

        // Additional info
        notes: document.getElementById('notes').value
    };
}

// Get birth certificates from storage
function getBirthCertificates() {
    const certificates = localStorage.getItem(BIRTH_CERT_CONFIG.storageKey);
    return certificates ? JSON.parse(certificates) : [];
}

// Preview certificate
function previewCertificate() {
    if (!validateForm()) {
        return;
    }

    const formData = collectFormData();
    openPreviewWindow(formData);
}

// Open preview window
function openPreviewWindow(data) {
    const previewWindow = window.open('', '_blank', 'width=900,height=700');
    const previewContent = generateEnhancedCertificateHTML(data);

    previewWindow.document.write(previewContent);
    previewWindow.document.close();
}

// Generate certificate HTML for preview/print (Exact Moroccan Format)
function generateCertificateHTML(data) {
    return `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>مستخرج من رسم الولادة - ${data.firstName} ${data.familyName}</title>
        <style>
            body {
                font-family: 'Times New Roman', serif;
                margin: 0;
                padding: 20px;
                direction: rtl;
                font-size: 12px;
                line-height: 1.4;
            }
            .document {
                width: 210mm;
                min-height: 297mm;
                margin: 0 auto;
                border: 2px solid #000;
                padding: 15px;
                background: white;
            }
            .header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 20px;
                border-bottom: 1px solid #000;
                padding-bottom: 10px;
            }
            .header-left {
                text-align: left;
                font-size: 11px;
            }
            .header-center {
                text-align: center;
                flex: 1;
                margin: 0 20px;
            }
            .header-right {
                text-align: right;
                font-size: 11px;
            }
            .title-box {
                border: 2px solid #000;
                padding: 8px;
                text-align: center;
                margin: 20px auto;
                width: 300px;
                font-weight: bold;
                font-size: 14px;
            }
            .form-section {
                margin: 15px 0;
            }
            .form-row {
                display: flex;
                justify-content: space-between;
                margin: 8px 0;
                align-items: center;
            }
            .form-field {
                display: flex;
                align-items: center;
                margin: 5px 0;
            }
            .form-field label {
                margin-left: 10px;
                font-weight: normal;
            }
            .form-field .value {
                border-bottom: 1px solid #000;
                min-width: 150px;
                padding: 2px 5px;
                display: inline-block;
            }
            .signature-section {
                margin-top: 40px;
                display: flex;
                justify-content: space-between;
            }
            .signature-box {
                text-align: center;
                width: 200px;
            }
            .stamp-area {
                width: 100px;
                height: 100px;
                border: 1px solid #000;
                margin: 10px auto;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
            }
            @media print {
                body { margin: 0; padding: 0; }
                .document { border: none; }
            }
        </style>
    </head>
    <body>
        <div class="document">
            <!-- Header -->
            <div class="header">
                <div class="header-right">
                    <div>المملكة المغربية</div>
                    <div>وزارة الداخلية</div>
                </div>
                <div class="header-center">
                    <div style="font-weight: bold; font-size: 14px;">مستخرج من سجلات مكتب الحالة المدنية</div>
                    <div style="font-size: 12px; margin-top: 5px;">Extrait des registres du bureau de l'État civil</div>
                </div>
                <div class="header-left">
                    <div>ROYAUME DU MAROC</div>
                    <div>Ministère de l'Intérieur</div>
                </div>
            </div>

            <!-- Act Number and Year -->
            <div class="form-row">
                <div class="form-field">
                    <label>رقم القيد:</label>
                    <span class="value">${data.actNumber}</span>
                </div>
                <div class="form-field">
                    <label>السنة:</label>
                    <span class="value">${data.gregorianYear}</span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-field">
                    <label>السنة الهجرية:</label>
                    <span class="value">${data.hijriYear}</span>
                </div>
                <div class="form-field">
                    <label>السنة الميلادية:</label>
                    <span class="value">${data.gregorianYear}</span>
                </div>
            </div>

            <!-- Title with Act Information -->
            <div style="display: flex; align-items: flex-start; margin: 2mm 0; padding: 2mm;">
                <!-- French Act Info (Left) -->
                <div style="flex: 1; text-align: left; direction: ltr; font-size: 8px;">
                    <div>Acte N°: <span style="border-bottom: 1px solid #000; min-width: 25mm; display: inline-block;">${data.actNumber}</span></div>
                    <div style="margin-top: 2mm; display: flex; align-items: flex-start;">
                        <span style="margin-right: 3mm;">Année</span>
                        <div style="display: flex; flex-direction: column;">
                            <span style="font-size: 7px;">Hégirienne</span>
                            <span style="font-size: 7px; margin-top: 1mm;">Grégorienne</span>
                        </div>
                        <div style="display: flex; flex-direction: column; margin-left: 2mm;">
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;">${data.hijriYear}</span>
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block; margin-top: 1mm;">${data.gregorianYear}</span>
                        </div>
                    </div>
                </div>

                <!-- Centered Title -->
                <div style="flex: 2; text-align: center; font-weight: bold;">
                    <div style="font-size: 12px; margin-bottom: 2mm;">نسخة موجزة من رسم الولادة</div>
                    <div style="font-size: 10px;">COPIE ABRÉGÉE D'ACTE DE NAISSANCE</div>
                </div>

                <!-- Arabic Act Info (Right) -->
                <div style="flex: 1; text-align: right; direction: rtl; font-size: 8px;">
                    <div>رقم القيد: <span style="border-bottom: 1px solid #000; min-width: 25mm; display: inline-block;">${data.actNumber}</span></div>
                    <div style="margin-top: 2mm; display: flex; align-items: flex-start; justify-content: flex-end;">
                        <span style="margin-left: 3mm;">سنة</span>
                        <div style="display: flex; flex-direction: column;">
                            <span style="font-size: 7px;">هجرية</span>
                            <span style="font-size: 7px; margin-top: 1mm;">ميلادية</span>
                        </div>
                        <div style="display: flex; flex-direction: column; margin-left: 2mm;">
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;">${data.hijriYear}</span>
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block; margin-top: 1mm;">${data.gregorianYear}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="form-section">
                <div class="form-field">
                    <label>الاسم الشخصي:</label>
                    <span class="value">${data.firstName}</span>
                </div>

                <div class="form-field">
                    <label>الاسم العائلي:</label>
                    <span class="value">${data.familyName}</span>
                </div>

                <div class="form-field">
                    <label>ولد في:</label>
                    <span class="value">${formatArabicDate(data.birthDate)}</span>
                </div>

                <div class="form-field">
                    <label>بـ:</label>
                    <span class="value">${data.birthPlace}</span>
                </div>

                <div class="form-row">
                    <div class="form-field">
                        <label>من:</label>
                        <span class="value">${data.fatherName}</span>
                    </div>
                    <div class="form-field">
                        <label>الجنسية:</label>
                        <span class="value">${data.fatherNationality}</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-field">
                        <label>و:</label>
                        <span class="value">${data.motherName}</span>
                    </div>
                    <div class="form-field">
                        <label>الجنسية:</label>
                        <span class="value">${data.motherNationality}</span>
                    </div>
                </div>

                <div class="form-field">
                    <label>بين الوالدين في حالة زواج شرعي:</label>
                    <span class="value">${data.parentsMaritalStatus}</span>
                </div>

                <div class="form-field">
                    <label>مستخرج طبقاً لسجلات الحالة المدنية المحفوظة تحت رقم:</label>
                    <span class="value">${data.actNumber}</span>
                </div>

                <div class="form-field">
                    <label>بمكتب الحالة المدنية لـ:</label>
                    <span class="value">${data.declarationPlace}</span>
                </div>

                <div class="form-row">
                    <div class="form-field">
                        <label>صرح به:</label>
                        <span class="value">${data.declarantName || data.fatherName}</span>
                    </div>
                    <div class="form-field">
                        <label>في:</label>
                        <span class="value">${data.declarationDate}</span>
                    </div>
                </div>

                <div class="form-field">
                    <label>المطابق لـ:</label>
                    <span class="value">${data.declarationDate}</span>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="signature-section">
                <div class="signature-box">
                    <div>ضابط الحالة المدنية</div>
                    <div>L'Officier de l'État-Civil</div>
                    <div class="stamp-area">
                        <div style="text-align: center;">
                            <div>ختم</div>
                            <div>مكتب</div>
                            <div>الحالة المدنية</div>
                        </div>
                    </div>
                </div>
                <div class="signature-box">
                    <div style="margin-top: 20px;">
                        <div>التاريخ: ${data.declarationDate}</div>
                        <div style="margin-top: 10px;">عام ${data.hijriYear}</div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Print birth certificate
function printBirthCertificate(data) {
    const printWindow = window.open('', '_blank');
    const printContent = generateEnhancedCertificateHTML(data);

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

// Reset form
function resetForm() {
    document.getElementById('birthCertificateForm').reset();
    setDefaultValues();

    // Remove validation classes
    const fields = document.querySelectorAll('.form-control, .form-select');
    fields.forEach(field => {
        field.classList.remove('is-valid', 'is-invalid');
    });

    showSuccessMessage('تم إعادة تعيين النموذج');
}

// Go back to main page
function goBack() {
    window.location.href = 'index.html';
}

// Message functions
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

function showErrorMessage(message) {
    showMessage(message, 'error');
}

function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-message');
    existingMessages.forEach(msg => msg.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show alert-message`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';

    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Enhanced certificate HTML generator (Half A4 - Arabic/French)
function generateEnhancedCertificateHTML(data) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>مستخرج من رسم الولادة - ${data.firstName} ${data.familyName}</title>
        <style>
            body {
                font-family: 'Times New Roman', serif;
                margin: 0;
                padding: 5mm;
                font-size: 9px;
                line-height: 1.2;
                background: white;
            }
            .document {
                width: 190mm;
                height: 135mm;
                margin: 0 auto;
                border: 2px solid #000;
                padding: 3mm;
                background: white;
                display: flex;
                flex-direction: column;
            }
            /* Header */
            .header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 3mm;
                padding-bottom: 2mm;
                font-size: 8px;
                font-weight: bold;
            }
            .header-right { text-align: right; direction: rtl; }
            .header-center { text-align: center; flex: 1; margin: 0 5mm; }
            .header-left { text-align: left; direction: ltr; }

            /* Two sections layout */
            .sections {
                display: flex;
                height: calc(100% - 20mm);
            }
            .section {
                flex: 1;
                padding: 2mm;
                display: flex;
                flex-direction: column;
            }
            .arabic-section {
                direction: rtl;
                text-align: right;
            }
            .french-section {
                direction: ltr;
                text-align: left;
            }

            /* Title */
            .section-title {
                text-align: center;
                font-weight: bold;
                font-size: 10px;
                margin: 2mm 0;
                padding: 2mm;
                border: 1px solid #000;
            }

            /* Content lines */
            .line {
                margin: 0.5mm 0;
                font-size: 8px;
                display: flex;
                align-items: baseline;
            }
            .label {
                font-weight: normal;
                margin-right: 2mm;
                white-space: nowrap;
                min-width: 20mm;
            }
            .french-section .label {
                margin-left: 2mm;
                margin-right: 0;
            }
            .underline {
                border-bottom: 0.5px solid #000;
                min-width: 15mm;
                padding: 0 1mm;
                flex: 1;
                min-height: 2mm;
            }

            /* Act info */
            .act-info {
                margin: 2mm 0;
                font-size: 8px;
            }
            .act-line {
                display: flex;
                justify-content: space-between;
                margin: 1mm 0;
            }

            /* Signature area */
            .signature {
                margin-top: auto;
                text-align: center;
                font-size: 7px;
                padding-top: 3mm;
                border-top: 0.5px solid #000;
            }
            .stamp {
                width: 15mm;
                height: 15mm;
                border: 1px solid #000;
                margin: 2mm auto;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 6px;
                line-height: 1;
            }

            @media print {
                body {
                    margin: 0;
                    padding: 0;
                    -webkit-print-color-adjust: exact;
                }
                .document {
                    border: 1px solid #000;
                    width: 190mm;
                    height: 135mm;
                }
            }
        </style>
    </head>
    <body>
        <div class="document">
            <!-- Header -->
            <div class="header">
                <div class="header-right">
                    <div>المملكة المغربية</div>
                    <div>وزارة الداخلية</div>
                    <div>إقليم أسفي</div>
                    <div>جماعة أيير</div>
                    <div>مكتب الحالة المدنية أيير</div>
                </div>
                <div class="header-center">
                    <div style="font-size: 7px; line-height: 1.2;">
                        طبقا للقانون رقم 37.99 المتعلق بالحالة المدنية والصادر بتنفيذه<br>
                        الظهير الشريف رقم 1.02.239<br>
                        بتاريخ 25 رجب 1423 (3) أكتوبر (2002)
                    </div>
                </div>
                <div class="header-left">
                    <div>ROYAUME DU MAROC</div>
                    <div>Ministère de l'Intérieur</div>
                    <div>Province de Safi</div>
                    <div>Commune d'Ayir</div>
                    <div>Bureau de l'État-Civil d'Ayir</div>
                </div>
            </div>

            <!-- Two sections: French and Arabic -->
            <div class="sections">
                <!-- French Section -->
                <div class="section french-section">
                    <div class="section-title">
                        EXTRAIT D'ACTE DE NAISSANCE
                    </div>

                    <div class="act-info">
                        <div class="act-line">
                            <span>Act N°: <span class="underline">${data.actNumber}</span></span>
                            <span>Année: <span class="underline">${data.gregorianYear}</span></span>
                        </div>
                        <div class="act-line">
                            <span>Hégirienne: <span class="underline">${data.hijriYear}</span></span>
                            <span>Grégorienne: <span class="underline">${data.gregorianYear}</span></span>
                        </div>
                    </div>

                    <div class="line">
                        <span class="label">Prénom</span>
                        <span class="underline">${data.firstName}</span>
                    </div>

                    <div class="line">
                        <span class="label">Nom de famille</span>
                        <span class="underline">${data.familyName}</span>
                    </div>

                    <div class="line">
                        <span class="label">Né le</span>
                        <span class="underline">${formatFrenchDate(data.birthDate)}</span>
                    </div>

                    <div class="line">
                        <span class="label">À</span>
                        <span class="underline">${data.birthPlace}</span>
                    </div>

                    <div class="line">
                        <span class="label">De</span>
                        <span class="underline">${data.fatherName}</span>
                    </div>

                    <div class="line">
                        <span class="label">Nationalité</span>
                        <span class="underline">${translateNationality(data.fatherNationality)}</span>
                    </div>

                    <div class="line">
                        <span class="label">Et de</span>
                        <span class="underline">${data.motherName}</span>
                    </div>

                    <div class="line">
                        <span class="label">Nationalité</span>
                        <span class="underline">${translateNationality(data.motherNationality)}</span>
                    </div>

                    <div class="line">
                        <span class="label">Mariage légitime</span>
                        <span class="underline">${data.parentsMaritalStatus === 'متزوجان' ? 'Oui' : 'Non'}</span>
                    </div>

                    <div class="line">
                        <span class="label">Extrait sous N°</span>
                        <span class="underline">${data.actNumber}</span>
                    </div>

                    <div class="line">
                        <span class="label">Bureau État-Civil</span>
                        <span class="underline">${data.declarationPlace}</span>
                    </div>

                    <div class="line">
                        <span class="label">Déclaré par</span>
                        <span class="underline">${data.declarantName || data.fatherName}</span>
                    </div>

                    <div class="line">
                        <span class="label">Le</span>
                        <span class="underline">${formatFrenchDate(data.declarationDate)}</span>
                    </div>

                    <div class="line">
                        <span class="label">Correspondant au</span>
                        <span class="underline">${formatFrenchDate(data.declarationDate)}</span>
                    </div>

                    <div class="signature">
                        <div>L'Officier de l'État-Civil</div>
                        <div class="stamp">Cachet</div>
                        <div>Date: ${formatFrenchDate(data.declarationDate)}</div>
                        <div>An ${data.hijriYear}</div>
                    </div>
                </div>

                <!-- Arabic Section -->
                <div class="section arabic-section">
                    <div class="section-title">
                        مستخرج من رسم الولادة
                    </div>

                    <div class="act-info">
                        <div class="act-line">
                            <span>رقم القيد: <span class="underline">${data.actNumber}</span></span>
                            <span>السنة: <span class="underline">${data.gregorianYear}</span></span>
                        </div>
                        <div class="act-line">
                            <span>الهجرية: <span class="underline">${data.hijriYear}</span></span>
                            <span>الميلادية: <span class="underline">${data.gregorianYear}</span></span>
                        </div>
                    </div>

                    <div class="line">
                        <span class="label">الاسم الشخصي</span>
                        <span class="underline">${data.firstName}</span>
                    </div>

                    <div class="line">
                        <span class="label">اسم العائلة</span>
                        <span class="underline">${data.familyName}</span>
                    </div>

                    <div class="line">
                        <span class="label">ولد في</span>
                        <span class="underline">${formatArabicDate(data.birthDate)}</span>
                    </div>

                    <div class="line">
                        <span class="label">بـ</span>
                        <span class="underline">${data.birthPlace}</span>
                    </div>

                    <div class="line">
                        <span class="label">من</span>
                        <span class="underline">${data.fatherName}</span>
                    </div>

                    <div class="line">
                        <span class="label">الجنسية</span>
                        <span class="underline">${data.fatherNationality}</span>
                    </div>

                    <div class="line">
                        <span class="label">و</span>
                        <span class="underline">${data.motherName}</span>
                    </div>

                    <div class="line">
                        <span class="label">الجنسية</span>
                        <span class="underline">${data.motherNationality}</span>
                    </div>

                    <div class="line">
                        <span class="label">زواج شرعي</span>
                        <span class="underline">${data.parentsMaritalStatus === 'متزوجان' ? 'نعم' : 'لا'}</span>
                    </div>

                    <div class="line">
                        <span class="label">مستخرج تحت رقم</span>
                        <span class="underline">${data.actNumber}</span>
                    </div>

                    <div class="line">
                        <span class="label">مكتب الحالة المدنية</span>
                        <span class="underline">${data.declarationPlace}</span>
                    </div>

                    <div class="line">
                        <span class="label">صرح به</span>
                        <span class="underline">${data.declarantName || data.fatherName}</span>
                    </div>

                    <div class="line">
                        <span class="label">في</span>
                        <span class="underline">${formatArabicDate(data.declarationDate)}</span>
                    </div>

                    <div class="line">
                        <span class="label">المطابق لـ</span>
                        <span class="underline">${formatArabicDate(data.declarationDate)}</span>
                    </div>

                    <div class="signature">
                        <div>ضابط الحالة المدنية</div>
                        <div class="stamp">ختم</div>
                        <div>التاريخ: ${formatArabicDate(data.declarationDate)}</div>
                        <div>عام ${data.hijriYear}</div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Helper function to format date in Arabic
function formatArabicDate(dateString) {
    const date = new Date(dateString);
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
}

// Helper function to format date in French
function formatFrenchDate(dateString) {
    const date = new Date(dateString);
    const months = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
}

// Helper function to translate nationality
function translateNationality(nationality) {
    const translations = {
        'مغربية': 'Marocaine',
        'مغربي': 'Marocain',
        'فرنسية': 'Française',
        'فرنسي': 'Français',
        'إسبانية': 'Espagnole',
        'إسباني': 'Espagnol',
        'أمريكية': 'Américaine',
        'أمريكي': 'Américain'
    };

    return translations[nationality] || nationality;
}
