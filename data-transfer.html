<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقل البيانات بين الأجهزة - مكتب الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', '<PERSON><PERSON>', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 20px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 50%;
            font-size: 2.5em;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
        }

        .header-text {
            text-align: right;
        }

        .header-text h1 {
            font-size: 2.2em;
            margin: 0 0 5px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        /* ===== FOOTER ===== */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a, #006233);
        }

        .footer p {
            margin: 0;
            font-weight: 500;
            opacity: 0.9;
        }

        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        .content {
            flex: 1;
            background: white;
            padding: 30px;
            overflow-y: auto;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
        }

        .transfer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .transfer-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .section-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            box-shadow: 0 4px 15px rgba(44, 62, 80, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .file-input {
            width: 100%;
            padding: 12px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .file-input:hover {
            border-color: #2c3e50;
            background: #f8f9fa;
        }

        .status-panel {
            background: white;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
            animation: slideIn 0.3s ease;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9em;
        }

        @media (max-width: 1200px) {
            .transfer-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .content {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .transfer-grid {
                grid-template-columns: 1fr;
            }

            .content {
                padding: 15px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.8em;
            }

            .header-nav-links {
                gap: 6px;
                justify-content: center;
            }

            .header-nav-link {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">🔄 نقل البيانات والنسخ الاحتياطية</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Header Navigation -->
            <div class="header-navigation">
                <div class="header-nav-content">
                    <div class="header-nav-links">
                        <a href="main-dashboard.html" class="header-nav-link">🏠 الصفحة الرئيسية</a>
                        <a href="citizens-database-indexeddb.html" class="header-nav-link">📝 إدارة البيانات</a>
                        <a href="search-citizens.html" class="header-nav-link">🔍 البحث في السجلات</a>
                        <a href="personal-id-form.html" class="header-nav-link">🆔 البطاقة الشخصية</a>
                        <a href="death-data-entry.html" class="header-nav-link">⚱️ تسجيل الوفاة</a>
                        <a href="employee-management.html" class="header-nav-link">👥 إدارة الموظفين</a>
                        <a href="data-transfer.html" class="header-nav-link active">🔄 و 🛡️ النسخ الاحتياطية</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="main-content">
            <!-- System Status -->
            <div class="status-panel">
                <h2 class="section-title">📊 حالة النظام</h2>
                <div id="systemInfo" class="system-info">
                    جاري فحص النظام...
                </div>
                <div id="alertContainer"></div>
            </div>

            <!-- Transfer Grid -->
            <div class="transfer-grid">
                <!-- Export Section -->
                <div class="transfer-section">
                    <h2 class="section-title">📤 تصدير البيانات</h2>
                    
                    <p style="margin-bottom: 20px; color: #6c757d;">
                        تصدير جميع بيانات المواطنين لنقلها إلى جهاز آخر
                    </p>

                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="includeImages" checked>
                            تضمين صور الشهادات (يزيد حجم الملف)
                        </label>
                    </div>

                    <button class="btn btn-primary" onclick="exportData()">
                        📦 تصدير البيانات
                    </button>

                    <div id="exportProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="exportProgressFill"></div>
                        </div>
                        <div id="exportStatus">جاري التصدير...</div>
                    </div>
                </div>

                <!-- Import Section -->
                <div class="transfer-section">
                    <h2 class="section-title">📥 استيراد البيانات</h2>
                    
                    <p style="margin-bottom: 20px; color: #6c757d;">
                        استيراد بيانات من جهاز آخر أو نسخة احتياطية
                    </p>

                    <input type="file" id="importFile" accept=".json" class="file-input" 
                           onchange="handleFileSelect(this)">

                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="clearExisting">
                            مسح البيانات الموجودة قبل الاستيراد
                        </label>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="createBackup" checked>
                            إنشاء نسخة احتياطية قبل الاستيراد
                        </label>
                    </div>

                    <button class="btn btn-success" onclick="importData()" id="importBtn" disabled>
                        📥 استيراد البيانات
                    </button>

                    <div id="importProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="importProgressFill"></div>
                        </div>
                        <div id="importStatus">جاري الاستيراد...</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="transfer-section">
                <h2 class="section-title">⚡ إجراءات سريعة</h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="btn btn-warning" onclick="createEmergencyBackup()">
                        🆘 نسخة احتياطية طارئة
                    </button>
                    
                    <button class="btn btn-primary" onclick="validateDatabase()">
                        🔍 فحص سلامة البيانات
                    </button>
                    
                    <button class="btn btn-success" onclick="showSyncHistory()">
                        📋 تاريخ المزامنة
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 نظام الحالة المدنية - جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script>
        // Global variables
        let selectedFile = null;
        let isProcessing = false;
        let dbManager = null;
        let scriptLoaded = false;
        let scriptError = false;

        // معالجات تحميل الملف - يجب تعريفها قبل استدعاءها
        function handleScriptLoad() {
            scriptLoaded = true;
            console.log('✅ تم تحميل indexeddb-manager.js بنجاح');
        }

        function handleScriptError() {
            scriptError = true;
            console.error('❌ فشل في تحميل indexeddb-manager.js');
        }
    </script>

    <!-- استخدام نفس مدير قاعدة البيانات المستخدم في الصفحة الرئيسية -->
    <script src="indexeddb-manager.js" onerror="handleScriptError()" onload="handleScriptLoad()"></script>
    <script>

        // Simple fallback database manager
        class FallbackDBManager {
            constructor() {
                this.fallbackMode = true;
                this.isInitialized = false;
            }

            async init() {
                try {
                    const testKey = 'test_' + Date.now();
                    localStorage.setItem(testKey, 'test');
                    localStorage.removeItem(testKey);
                    this.isInitialized = true;
                    console.log('✅ تم تهيئة FallbackDBManager');
                    return true;
                } catch (error) {
                    throw new Error('التخزين المحلي غير متاح');
                }
            }

            async getAllCitizens() {
                try {
                    const data = localStorage.getItem('citizens');
                    if (!data) {
                        console.log('📊 لا توجد بيانات في localStorage');
                        return [];
                    }
                    const citizens = JSON.parse(data);
                    console.log(`📊 تم العثور على ${citizens.length} مواطن في localStorage`);
                    return Array.isArray(citizens) ? citizens : [];
                } catch (error) {
                    console.error('خطأ في قراءة البيانات من localStorage:', error);
                    return [];
                }
            }

            async getCount() {
                try {
                    const data = localStorage.getItem('citizens');
                    if (!data) return 0;
                    const citizens = JSON.parse(data);
                    return Array.isArray(citizens) ? citizens.length : 0;
                } catch (error) {
                    console.error('خطأ في حساب عدد المواطنين:', error);
                    return 0;
                }
            }

            async addCitizen(citizen) {
                const citizens = JSON.parse(localStorage.getItem('citizens') || '[]');

                const exists = citizens.find(c =>
                    c.id === citizen.id ||
                    (c.actNumber && citizen.actNumber && c.actNumber === citizen.actNumber)
                );

                if (exists) {
                    throw new Error('المواطن موجود بالفعل');
                }

                citizens.push(citizen);
                localStorage.setItem('citizens', JSON.stringify(citizens));
                return citizen.id;
            }

            async getCitizenByActNumber(actNumber) {
                const citizens = JSON.parse(localStorage.getItem('citizens') || '[]');
                return citizens.find(c => c.actNumber === actNumber) || null;
            }

            async getCitizen(id) {
                const citizens = JSON.parse(localStorage.getItem('citizens') || '[]');
                return citizens.find(c =>
                    c.id === id ||
                    c.id === id.toString() ||
                    c.id === parseInt(id) ||
                    c.id?.toString() === id?.toString()
                ) || null;
            }

            async clearAllData() {
                localStorage.removeItem('citizens');
                return true;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            // انتظار قصير للتأكد من تحميل الملفات
            await new Promise(resolve => setTimeout(resolve, 500));

            try {
                let useIndexedDBManager = false;
                let managerType = 'fallback';

                // فحص ما إذا كان CitizensDB متاح (الاسم الصحيح في indexeddb-manager.js)
                if (!scriptError && typeof CitizensDB !== 'undefined') {
                    try {
                        // اختبار إنشاء instance من CitizensDB
                        const testManager = new CitizensDB();
                        useIndexedDBManager = true;
                        managerType = 'CitizensDB';
                        console.log('✅ CitizensDB متاح ويعمل');
                    } catch (error) {
                        console.warn('⚠️ CitizensDB متاح لكن لا يعمل:', error);
                    }
                }

                // فحص ما إذا كان citizensDB (المتغير العام) متاح
                if (!useIndexedDBManager && !scriptError && typeof citizensDB !== 'undefined') {
                    try {
                        // استخدام المتغير العام citizensDB
                        useIndexedDBManager = true;
                        managerType = 'citizensDB';
                        console.log('✅ citizensDB (المتغير العام) متاح ويعمل');
                    } catch (error) {
                        console.warn('⚠️ citizensDB متاح لكن لا يعمل:', error);
                    }
                }

                // تهيئة مدير قاعدة البيانات
                if (useIndexedDBManager) {
                    if (managerType === 'citizensDB' && typeof citizensDB !== 'undefined') {
                        // استخدام المتغير العام citizensDB
                        dbManager = citizensDB;
                        await dbManager.init();
                        showAlert('✅ تم تهيئة النظام بنجاح - متوافق مع الصفحة الرئيسية', 'success');
                    } else if (managerType === 'CitizensDB') {
                        // إنشاء instance جديد من CitizensDB
                        dbManager = new CitizensDB();
                        await dbManager.init();
                        showAlert('✅ تم تهيئة النظام بنجاح - متوافق مع الصفحة الرئيسية', 'success');
                    } else {
                        throw new Error('لم يتم العثور على مدير قاعدة البيانات المناسب');
                    }
                } else {
                    console.warn('⚠️ استخدام FallbackDBManager');
                    dbManager = new FallbackDBManager();
                    await dbManager.init();
                    showAlert('⚠️ تم تهيئة النظام بالوضع الأساسي - قد تكون بعض المزايا محدودة', 'warning');
                }

                // عرض معلومات النظام
                await displaySystemInfo(managerType);

            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showAlert('❌ خطأ في تهيئة النظام: ' + error.message, 'error');

                // محاولة عرض معلومات أساسية
                try {
                    document.getElementById('systemInfo').innerHTML = `
                        <div style="color: #dc3545; text-align: center; padding: 20px;">
                            ❌ خطأ في تهيئة قاعدة البيانات<br>
                            <small style="display: block; margin-top: 10px;">${error.message}</small>
                            <small style="display: block; margin-top: 5px;">
                                ${scriptError ? 'فشل في تحميل indexeddb-manager.js' : 'خطأ في التهيئة'}
                            </small>
                            <div style="margin-top: 15px; padding: 10px; background: rgba(255,193,7,0.1); border-radius: 5px;">
                                💡 تأكد من وجود ملف indexeddb-manager.js في نفس المجلد<br>
                                <small style="margin-top: 5px; display: block;">
                                    أو تحقق من وجود بيانات في localStorage
                                </small>
                            </div>
                            <div style="margin-top: 10px;">
                                <button onclick="location.reload()" class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9em;">
                                    🔄 إعادة تحميل الصفحة
                                </button>
                            </div>
                        </div>
                    `;
                } catch (e) {
                    console.error('خطأ في عرض رسالة الخطأ:', e);
                }
            }
        });

        // Display system information
        async function displaySystemInfo(managerType = 'unknown') {
            try {
                console.log('📊 عرض معلومات النظام...');
                const citizensCount = await dbManager.getCount();
                const storageType = dbManager.fallbackMode ? 'localStorage' : 'indexedDB';
                const supportsFiles = window.File && window.FileReader && window.FileList && window.Blob;
                const lastBackup = localStorage.getItem('lastBackupTime');

                // فحص إضافي للبيانات في localStorage
                let localStorageInfo = '';
                try {
                    const localData = localStorage.getItem('citizens');
                    if (localData) {
                        const localCitizens = JSON.parse(localData);
                        localStorageInfo = ` (localStorage: ${Array.isArray(localCitizens) ? localCitizens.length : 0} سجل)`;
                    }
                } catch (e) {
                    localStorageInfo = ' (localStorage: خطأ في القراءة)';
                }

                console.log('📊 معلومات النظام:', {
                    managerType,
                    citizensCount,
                    storageType,
                    localStorageInfo,
                    supportsFiles
                });

                // فحص وجود مخزن الصور
                let hasImagesStore = false;
                if (!dbManager.fallbackMode && dbManager.db && dbManager.db.objectStoreNames) {
                    hasImagesStore = Array.from(dbManager.db.objectStoreNames).includes('images');
                }

                // تحديد حالة التوافق
                const isFullyCompatible = (managerType === 'CitizensDB' || managerType === 'citizensDB');
                const compatibilityStatus = isFullyCompatible ?
                    '✅ متوافق مع الصفحة الرئيسية' :
                    '⚠️ وضع أساسي (توافق محدود)';

                const systemInfoDiv = document.getElementById('systemInfo');
                systemInfoDiv.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div>
                            <strong>مدير قاعدة البيانات:</strong><br>
                            ${isFullyCompatible ? `🔧 ${managerType}` : '🔧 FallbackDBManager'}
                        </div>
                        <div>
                            <strong>نوع التخزين:</strong><br>
                            ${storageType === 'indexedDB' ? '🗄️ IndexedDB (متقدم)' : '💾 التخزين المحلي (أساسي)'}
                        </div>
                        <div>
                            <strong>عدد السجلات:</strong><br>
                            📊 ${citizensCount} مواطن مسجل${localStorageInfo}
                        </div>
                        <div>
                            <strong>دعم الصور:</strong><br>
                            ${hasImagesStore ? '🖼️ مدعوم (مخزن منفصل)' : '📄 أساسي (مع البيانات)'}
                        </div>
                        <div>
                            <strong>آخر نسخة احتياطية:</strong><br>
                            ${lastBackup ? new Date(lastBackup).toLocaleString('ar-MA') : 'لم يتم إنشاء نسخة بعد'}
                        </div>
                        <div>
                            <strong>دعم الملفات:</strong><br>
                            ${supportsFiles ? '✅ مدعوم' : '❌ غير مدعوم'}
                        </div>
                        <div>
                            <strong>التوافق:</strong><br>
                            ${compatibilityStatus}
                        </div>
                        <div>
                            <strong>حالة الملف:</strong><br>
                            ${scriptError ? '❌ فشل التحميل' : scriptLoaded ? '✅ تم التحميل' : '⏳ جاري التحميل'}
                        </div>
                    </div>

                    ${citizensCount === 0 ? `
                    <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404;">
                        <h5 style="margin: 0 0 10px 0;">💡 لا توجد بيانات للتصدير</h5>
                        <p style="margin: 0; font-size: 0.9em;">
                            يمكنك إضافة بيانات من خلال:
                            <br>• <a href="citizens-database-indexeddb.html" style="color: #856404;">صفحة إدخال البيانات</a>
                            <br>• <a href="data-generator.html" style="color: #856404;">مولد البيانات التجريبية</a>
                            <br>• استيراد ملف JSON من جهاز آخر
                        </p>
                    </div>
                    ` : ''}
                `;
            } catch (error) {
                console.error('خطأ في عرض معلومات النظام:', error);
                document.getElementById('systemInfo').innerHTML = `
                    <div style="color: #dc3545; text-align: center;">
                        ❌ خطأ في تحميل معلومات النظام<br>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // Export data
        async function exportData() {
            if (isProcessing) {
                showAlert('⚠️ عملية أخرى قيد التنفيذ', 'error');
                return;
            }

            try {
                isProcessing = true;
                const includeImages = document.getElementById('includeImages').checked;

                showProgress('export', 0, 'بدء التصدير...');

                // Get all citizens data with detailed logging
                console.log('🔍 محاولة جلب البيانات للتصدير...');
                const citizens = await dbManager.getAllCitizens();
                console.log('📊 البيانات المسترجعة:', {
                    count: citizens.length,
                    isArray: Array.isArray(citizens),
                    sample: citizens.length > 0 ? citizens[0] : null
                });

                if (!citizens || citizens.length === 0) {
                    const storageType = dbManager.fallbackMode ? 'localStorage' : 'IndexedDB';
                    showAlert(`⚠️ لا توجد بيانات للتصدير في ${storageType}\n\nتأكد من وجود بيانات مسجلة في النظام`, 'error');
                    isProcessing = false;
                    hideProgress('export');
                    return;
                }

                showProgress('export', 25, `جاري تصدير ${citizens.length} سجل...`);

                // إذا كان مطلوب تضمين الصور، جلب الصور من مخزن منفصل
                let citizensWithImages = [...citizens];
                if (includeImages && !dbManager.fallbackMode) {
                    showProgress('export', 40, 'جاري تحميل الصور...');

                    for (let i = 0; i < citizensWithImages.length; i++) {
                        const citizen = citizensWithImages[i];
                        if (citizen.certificateImage && citizen.certificateImage.hasImage) {
                            try {
                                // جلب الصورة من مخزن الصور المنفصل
                                const citizenWithImage = await dbManager.getCitizen(citizen.id, true);
                                if (citizenWithImage && citizenWithImage.certificateImage) {
                                    citizensWithImages[i] = citizenWithImage;
                                }
                            } catch (error) {
                                console.warn(`فشل في تحميل صورة المواطن ${citizen.id}:`, error);
                            }
                        }

                        // تحديث شريط التقدم
                        const progress = 40 + (i / citizensWithImages.length) * 25;
                        showProgress('export', progress, `تحميل الصور: ${i + 1}/${citizensWithImages.length}`);
                    }
                }

                showProgress('export', 70, 'إعداد البيانات للتصدير...');

                // Prepare export data with enhanced metadata
                const exportData = {
                    version: '2.0', // إصدار محسن
                    timestamp: new Date().toISOString(),
                    totalRecords: citizensWithImages.length,
                    includeImages: includeImages,
                    storageType: dbManager.fallbackMode ? 'localStorage' : 'indexedDB',
                    hasImagesStore: !dbManager.fallbackMode && dbManager.db &&
                                   Array.from(dbManager.db.objectStoreNames).includes('images'),
                    exportedBy: 'IndexedDBManager', // تحديد مصدر التصدير
                    data: citizensWithImages
                };

                showProgress('export', 85, 'إنشاء ملف التصدير...');

                // Create and download file
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `citizens_backup_v2_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                // Save backup timestamp and history
                const backupTime = new Date().toISOString();
                localStorage.setItem('lastBackupTime', backupTime);

                // حفظ تاريخ العمليات
                const syncHistory = JSON.parse(localStorage.getItem('syncHistory') || '[]');
                syncHistory.push({
                    type: 'export',
                    timestamp: backupTime,
                    records: citizensWithImages.length,
                    includeImages: includeImages
                });
                localStorage.setItem('syncHistory', JSON.stringify(syncHistory.slice(-10))); // الاحتفاظ بآخر 10 عمليات

                showProgress('export', 100, 'تم التصدير بنجاح!');
                showAlert(`✅ تم تصدير ${citizensWithImages.length} سجل بنجاح${includeImages ? ' (مع الصور)' : ''}`, 'success');

                setTimeout(() => hideProgress('export'), 2000);

            } catch (error) {
                console.error('خطأ في التصدير:', error);
                showAlert('❌ خطأ في التصدير: ' + error.message, 'error');
                hideProgress('export');
            } finally {
                isProcessing = false;
            }
        }

        // Handle file selection
        function handleFileSelect(input) {
            selectedFile = input.files[0];
            const importBtn = document.getElementById('importBtn');

            if (selectedFile) {
                if (selectedFile.type === 'application/json' || selectedFile.name.endsWith('.json')) {
                    importBtn.disabled = false;
                    showAlert(`✅ تم اختيار الملف: ${selectedFile.name}`, 'info');
                } else {
                    importBtn.disabled = true;
                    showAlert('❌ يرجى اختيار ملف JSON صحيح', 'error');
                    selectedFile = null;
                }
            } else {
                importBtn.disabled = true;
            }
        }

        // Import data
        async function importData() {
            if (isProcessing) {
                showAlert('⚠️ عملية أخرى قيد التنفيذ', 'error');
                return;
            }

            if (!selectedFile) {
                showAlert('❌ يرجى اختيار ملف للاستيراد', 'error');
                return;
            }

            try {
                isProcessing = true;
                const clearExisting = document.getElementById('clearExisting').checked;
                const createBackup = document.getElementById('createBackup').checked;

                showProgress('import', 0, 'بدء الاستيراد...');

                // Confirm if clearing existing data
                if (clearExisting) {
                    const confirmed = confirm('⚠️ تحذير: سيتم مسح جميع البيانات الموجودة!\nهل أنت متأكد من المتابعة؟');
                    if (!confirmed) {
                        isProcessing = false;
                        hideProgress('import');
                        return;
                    }
                }

                // Create backup if requested
                if (createBackup) {
                    showProgress('import', 10, 'إنشاء نسخة احتياطية...');
                    await createEmergencyBackup();
                }

                showProgress('import', 30, 'قراءة الملف...');

                // Read file content
                const fileContent = await readFileAsText(selectedFile);
                const importData = JSON.parse(fileContent);

                if (!importData.data || !Array.isArray(importData.data)) {
                    throw new Error('تنسيق الملف غير صحيح');
                }

                showProgress('import', 50, 'معالجة البيانات...');

                // Clear existing data if requested
                if (clearExisting) {
                    await dbManager.clearAllData();
                }

                // Import data with enhanced compatibility
                let imported = 0;
                let skipped = 0;
                let errors = 0;

                for (let i = 0; i < importData.data.length; i++) {
                    try {
                        const citizen = importData.data[i];

                        // Check if citizen already exists (by actNumber or ID)
                        if (!clearExisting) {
                            let existing = null;

                            // البحث برقم العقد أولاً
                            if (citizen.actNumber) {
                                existing = await dbManager.getCitizenByActNumber(citizen.actNumber);
                            }

                            // إذا لم يوجد، البحث بالمعرف
                            if (!existing && citizen.id) {
                                try {
                                    existing = await dbManager.getCitizen(citizen.id);
                                } catch (e) {
                                    // تجاهل أخطاء البحث
                                }
                            }

                            if (existing) {
                                skipped++;
                                continue;
                            }
                        }

                        // إضافة المواطن باستخدام نفس الطريقة المستخدمة في الصفحة الرئيسية
                        await dbManager.addCitizen(citizen);
                        imported++;

                        // Update progress
                        const progress = 50 + (i / importData.data.length) * 40;
                        showProgress('import', progress, `استيراد السجل ${i + 1} من ${importData.data.length}...`);

                    } catch (error) {
                        console.error('خطأ في استيراد السجل:', error);
                        errors++;

                        // محاولة تسجيل تفاصيل الخطأ
                        if (importData.data[i]) {
                            console.error('بيانات السجل الذي فشل:', {
                                id: importData.data[i].id,
                                actNumber: importData.data[i].actNumber,
                                name: importData.data[i].firstNameAr || importData.data[i].personalName
                            });
                        }
                    }
                }

                showProgress('import', 100, 'تم الاستيراد بنجاح!');

                const message = `✅ تم الاستيراد بنجاح!\n` +
                              `📥 تم استيراد: ${imported} سجل\n` +
                              `⏭️ تم تخطي: ${skipped} سجل\n` +
                              `❌ أخطاء: ${errors} سجل`;

                showAlert(message, 'success');

                // Reset file input
                document.getElementById('importFile').value = '';
                document.getElementById('importBtn').disabled = true;
                selectedFile = null;

                // Refresh system info
                await displaySystemInfo();

                setTimeout(() => hideProgress('import'), 2000);

            } catch (error) {
                console.error('خطأ في الاستيراد:', error);
                showAlert('❌ خطأ في الاستيراد: ' + error.message, 'error');
                hideProgress('import');
            } finally {
                isProcessing = false;
            }
        }

        // Helper function to read file as text
        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = (e) => reject(new Error('خطأ في قراءة الملف'));
                reader.readAsText(file);
            });
        }

        // Create emergency backup
        async function createEmergencyBackup() {
            try {
                const citizens = await dbManager.getAllCitizens();

                if (citizens.length === 0) {
                    showAlert('⚠️ لا توجد بيانات لإنشاء نسخة احتياطية', 'error');
                    return false;
                }

                // نسخة احتياطية شاملة مع الصور
                let citizensWithImages = [...citizens];
                if (!dbManager.fallbackMode) {
                    // محاولة جلب الصور للنسخة الاحتياطية الطارئة
                    for (let i = 0; i < citizensWithImages.length; i++) {
                        const citizen = citizensWithImages[i];
                        if (citizen.certificateImage && citizen.certificateImage.hasImage) {
                            try {
                                const citizenWithImage = await dbManager.getCitizen(citizen.id, true);
                                if (citizenWithImage && citizenWithImage.certificateImage) {
                                    citizensWithImages[i] = citizenWithImage;
                                }
                            } catch (error) {
                                // تجاهل أخطاء تحميل الصور في النسخة الطارئة
                                console.warn(`تعذر تحميل صورة المواطن ${citizen.id} في النسخة الطارئة`);
                            }
                        }
                    }
                }

                const backupData = {
                    version: '2.0',
                    timestamp: new Date().toISOString(),
                    totalRecords: citizensWithImages.length,
                    type: 'emergency_backup',
                    storageType: dbManager.fallbackMode ? 'localStorage' : 'indexedDB',
                    exportedBy: 'IndexedDBManager',
                    includeImages: true,
                    data: citizensWithImages
                };

                const dataStr = JSON.stringify(backupData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `emergency_backup_v2_${new Date().toISOString().split('T')[0]}_${Date.now()}.json`;
                link.click();

                const backupTime = new Date().toISOString();
                localStorage.setItem('lastBackupTime', backupTime);

                // حفظ في تاريخ العمليات
                const syncHistory = JSON.parse(localStorage.getItem('syncHistory') || '[]');
                syncHistory.push({
                    type: 'emergency_backup',
                    timestamp: backupTime,
                    records: citizensWithImages.length
                });
                localStorage.setItem('syncHistory', JSON.stringify(syncHistory.slice(-10)));

                showAlert(`✅ تم إنشاء نسخة احتياطية طارئة (${citizensWithImages.length} سجل)`, 'success');
                return true;

            } catch (error) {
                console.error('خطأ في النسخة الاحتياطية:', error);
                showAlert('❌ خطأ في إنشاء النسخة الاحتياطية: ' + error.message, 'error');
                return false;
            }
        }

        // Validate database
        async function validateDatabase() {
            try {
                showAlert('🔍 جاري فحص سلامة البيانات...', 'info');

                const citizens = await dbManager.getAllCitizens();
                const issues = [];
                let validRecords = 0;

                for (let i = 0; i < citizens.length; i++) {
                    const citizen = citizens[i];

                    // Check required fields
                    if (!citizen.firstNameAr || !citizen.familyNameAr) {
                        issues.push(`السجل ${i + 1}: اسم غير مكتمل`);
                    }

                    if (!citizen.actNumber) {
                        issues.push(`السجل ${i + 1}: رقم العقد مفقود`);
                    }

                    if (!citizen.birthDate) {
                        issues.push(`السجل ${i + 1}: تاريخ الولادة مفقود`);
                    }

                    if (issues.length === 0) {
                        validRecords++;
                    }
                }

                if (issues.length === 0) {
                    showAlert(`✅ البيانات سليمة! تم فحص ${citizens.length} سجل`, 'success');
                } else {
                    const issuesText = issues.slice(0, 5).join('\n');
                    const moreText = issues.length > 5 ? `\n... و ${issues.length - 5} مشكلة أخرى` : '';
                    showAlert(`⚠️ تم العثور على ${issues.length} مشكلة:\n${issuesText}${moreText}`, 'error');
                }

            } catch (error) {
                console.error('خطأ في فحص البيانات:', error);
                showAlert('❌ خطأ في فحص البيانات: ' + error.message, 'error');
            }
        }

        // Show sync history
        function showSyncHistory() {
            const history = JSON.parse(localStorage.getItem('syncHistory') || '[]');

            if (history.length === 0) {
                showAlert('📋 لا يوجد تاريخ مزامنة', 'info');
                return;
            }

            const historyText = history.slice(-5).map(op =>
                `${op.type === 'export' ? '📤' : '📥'} ${op.type === 'export' ? 'تصدير' : 'استيراد'} - ${new Date(op.timestamp).toLocaleString('ar-MA')}`
            ).join('\n');

            showAlert(`📋 آخر 5 عمليات:\n${historyText}`, 'info');
        }

        // Show progress
        function showProgress(type, percentage, message) {
            const progressDiv = document.getElementById(`${type}Progress`);
            const progressFill = document.getElementById(`${type}ProgressFill`);
            const statusDiv = document.getElementById(`${type}Status`);

            progressDiv.style.display = 'block';
            progressFill.style.width = percentage + '%';
            statusDiv.textContent = message;
        }

        // Hide progress
        function hideProgress(type) {
            const progressDiv = document.getElementById(`${type}Progress`);
            progressDiv.style.display = 'none';
        }

        // Show alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = `alert-${type === 'warning' ? 'warning' : type}`;

            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, type === 'warning' ? 10000 : 8000); // تحذيرات تبقى أطول
        }
    </script>
</body>
</html>
