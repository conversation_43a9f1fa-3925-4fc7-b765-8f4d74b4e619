<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج إدخال بيانات البطاقة الشخصية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', '<PERSON><PERSON>', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* ===== Header ===== */
        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 20px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, #ffd700 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
        }

        .header-text {
            text-align: right;
        }

        .header-text h1 {
            font-size: 2.2em;
            margin: 0 0 5px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        /* ===== Navigation ===== */
        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        /* ===== المحتوى الرئيسي ===== */
        .content {
            flex: 1;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
        }

        .form-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }

        .section-title {
            font-size: 1.4em;
            color: #c41e3a;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #c41e3a;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-group.full-width {
            flex: 100%;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        input[type="text"],
        input[type="date"],
        textarea,
        select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        input[type="text"]:focus,
        input[type="date"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 10px rgba(196, 30, 58, 0.2);
            transform: translateY(-2px);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        .date-group {
            display: flex;
            gap: 15px;
            align-items: end;
        }

        .date-input {
            flex: 1;
        }

        .hijri-note {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        /* تحسينات بصرية لتحويل التاريخ */
        .date-conversion-indicator {
            position: relative;
        }

        .date-conversion-indicator::after {
            content: "🔄";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .date-conversion-indicator:hover::after {
            opacity: 1;
            transform: translateY(-50%) scale(1.2);
        }

        /* تأثير التحويل الناجح */
        .conversion-success {
            background-color: #e8f5e8 !important;
            border-color: #28a745 !important;
            transition: all 0.5s ease;
        }

        .buttons-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 150px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #c41e3a 0%, #a01729 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(196, 30, 58, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #228b22 0%, #1e7e1e 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(34, 139, 34, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #666;
            border: 2px solid #ddd;
        }

        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #c41e3a;
            color: #c41e3a;
        }

        .required {
            color: #c41e3a;
        }

        .form-note {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }

        .form-note strong {
            color: #0a4c57;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .form-group {
                min-width: 100%;
            }

            .date-group {
                flex-direction: column;
                gap: 10px;
            }

            .buttons-container {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* تحسينات للطباعة */
        @media print {
            body {
                background: white;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            .buttons-container {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نموذج البطاقة الشخصية</h1>
                            <div class="subtitle">مكتب الحالة المدنية - أيير</div>
                            <div class="department">قسم التوثيق والشهادات</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="header-navigation">
                <div class="header-nav-content">
                    <div class="header-nav-links">
                        <a href="main-dashboard.html" class="header-nav-link">🏠 الصفحة الرئيسية</a>
                        <a href="citizens-database-indexeddb.html" class="header-nav-link">📝 إدارة البيانات</a>
                        <a href="search-citizens.html" class="header-nav-link">🔍 البحث في السجلات</a>
                        <a href="personal-id-form.html" class="header-nav-link active">🆔 البطاقة الشخصية</a>
                        <a href="death-data-entry.html" class="header-nav-link">⚱️ تسجيل الوفاة</a>
                        <a href="employee-management.html" class="header-nav-link">👥 إدارة الموظفين</a>
                        <a href="data-transfer.html" class="header-nav-link">🔄 و 🛡️ النسخ الاحتياطية</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content">
            <div class="form-container">
                <h1 style="text-align: center; color: #c41e3a; font-size: 2.5em; margin-bottom: 20px;">📋 نموذج البطاقة الشخصية</h1>
                <p style="text-align: center; color: #7f8c8d; font-size: 1.2em; margin-bottom: 30px;">يرجى ملء جميع البيانات المطلوبة بدقة</p>

                <div class="form-note">
                    <strong>ملاحظة:</strong> جميع الحقول المطلوبة مميزة بعلامة <span class="required">*</span>.
                    يرجى التأكد من صحة البيانات قبل إنشاء البطاقة.
                </div>

            <form id="personalIdForm">
                <!-- قسم المعلومات الشخصية -->
                <div class="form-section">
                    <h2 class="section-title">
                        👤 المعلومات الشخصية
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstNameAr">الاسم الشخصي (عربي) <span class="required">*</span></label>
                            <input type="text" id="firstNameAr" name="firstNameAr" required placeholder="مثال: محمد">
                        </div>
                        <div class="form-group">
                            <label for="firstNameFr">الاسم الشخصي (فرنسي) <span class="required">*</span></label>
                            <input type="text" id="firstNameFr" name="firstNameFr" required placeholder="Exemple: Mohamed">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="familyNameAr">الاسم العائلي (عربي) <span class="required">*</span></label>
                            <input type="text" id="familyNameAr" name="familyNameAr" required placeholder="مثال: العلوي">
                        </div>
                        <div class="form-group">
                            <label for="familyNameFr">الاسم العائلي (فرنسي) <span class="required">*</span></label>
                            <input type="text" id="familyNameFr" name="familyNameFr" required placeholder="Exemple: Alaoui">
                        </div>
                    </div>
                </div>

                <!-- قسم تاريخ ومكان الولادة -->
                <div class="form-section">
                    <h2 class="section-title">
                        📅 تاريخ ومكان الولادة
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="birthDateHijri">تاريخ الولادة <span class="required">*</span></label>
                            <input type="text" id="birthDateHijri" name="birthDateHijri" class="date-conversion-indicator" required placeholder="مثال: 25/06/1411">
                            <div class="hijri-note">التاريخ الهجري بالصيغة: يوم/شهر/سنة • يتم التحويل تلقائياً</div>
                        </div>
                        <div class="form-group">
                            <label for="birthDateGregorian">موافق <span class="required">*</span></label>
                            <input type="date" id="birthDateGregorian" name="birthDateGregorian" class="date-conversion-indicator" required>
                            <div class="hijri-note">التاريخ الميلادي الموافق • يتم التحويل تلقائياً</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="birthPlaceAr">مكان الولادة (عربي) <span class="required">*</span></label>
                            <input type="text" id="birthPlaceAr" name="birthPlaceAr" required placeholder="مثال: الرباط">
                        </div>
                        <div class="form-group">
                            <label for="birthPlaceFr">مكان الولادة (فرنسي) <span class="required">*</span></label>
                            <input type="text" id="birthPlaceFr" name="birthPlaceFr" required placeholder="Exemple: Rabat">
                        </div>
                    </div>
                </div>

                <!-- قسم معلومات الوالدين -->
                <div class="form-section">
                    <h2 class="section-title">
                        👨‍👩‍👧‍👦 معلومات الوالدين
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="fatherName">اسم الوالد <span class="required">*</span></label>
                            <input type="text" id="fatherName" name="fatherName" required placeholder="مثال: عبد الله العلوي">
                        </div>
                        <div class="form-group">
                            <label for="motherName">اسم الوالدة <span class="required">*</span></label>
                            <input type="text" id="motherName" name="motherName" required placeholder="مثال: فاطمة الزهراني">
                        </div>
                    </div>
                </div>

                <!-- قسم العنوان الحالي -->
                <div class="form-section">
                    <h2 class="section-title">
                        🏠 العنوان الحالي
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="currentAddressAr">العنوان الحالي (عربي) <span class="required">*</span></label>
                            <input type="text" id="currentAddressAr" name="currentAddressAr" required placeholder="مثال: حي السلام">
                        </div>
                        <div class="form-group">
                            <label for="currentAddressFr">العنوان الحالي (فرنسي)</label>
                            <input type="text" id="currentAddressFr" name="currentAddressFr" placeholder="Exemple: Hay Salam">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="commune">الجماعة</label>
                            <input type="text" id="commune" name="commune" value="جماعة أيير" placeholder="جماعة أيير">
                        </div>
                        <div class="form-group">
                            <label for="province">الإقليم</label>
                            <input type="text" id="province" name="province" value="إقليم آسفي" placeholder="إقليم آسفي">
                        </div>
                    </div>
                </div>

                <!-- قسم البيانات الهامشية -->
                <div class="form-section">
                    <h2 class="section-title">
                        📝 البيانات الهامشية
                    </h2>

                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="marginalData">البيانات الهامشية</label>
                            <textarea id="marginalData" name="marginalData" placeholder="أدخل أي بيانات إضافية أو اتركه فارغ"></textarea>
                        </div>
                    </div>
                </div>

                <!-- قسم بيانات العقد والإصدار -->
                <div class="form-section">
                    <h2 class="section-title">
                        📄 بيانات العقد والإصدار
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="actNumber">رقم العقد</label>
                            <input type="text" id="actNumber" name="actNumber" placeholder="مثال: 15/2024">
                        </div>
                        <div class="form-group">
                            <label for="actYear">سنة العقد</label>
                            <input type="text" id="actYear" name="actYear" placeholder="مثال: 2024">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="issuingCommune">جماعة الإصدار</label>
                            <input type="text" id="issuingCommune" name="issuingCommune" value="أيير" placeholder="أيير">
                        </div>
                        <div class="form-group">
                            <label for="issueDate">تاريخ الإصدار</label>
                            <input type="date" id="issueDate" name="issueDate">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="certificationDate">تاريخ التصديق</label>
                            <input type="date" id="certificationDate" name="certificationDate">
                        </div>
                    </div>
                </div>

                <!-- قسم بيانات الشاهد -->
                <div class="form-section">
                    <h2 class="section-title">
                        👤 بيانات الشاهد
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="witnessName">أنا الموقع أسفله</label>
                            <input type="text" id="witnessName" name="witnessName" placeholder="مثال: عبد الرحمن العلوي">
                        </div>
                        <div class="form-group">
                            <label for="witnessAddress">الساكن حاليا ب</label>
                            <input type="text" id="witnessAddress" name="witnessAddress" placeholder="مثال: حي النهضة أيير">
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="buttons-container">
                    <button type="submit" class="btn btn-primary">
                        🆔 إنشاء البطاقة الشخصية
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="fillSampleData()" style="display: none;">
                        📝 ملء بيانات تجريبية
                    </button>
                    <button type="reset" class="btn btn-outline">
                        🔄 مسح البيانات
                    </button>
                    <a href="main-dashboard.html" class="btn btn-outline">
                        🏠 العودة للرئيسية
                    </a>
                </div>
            </form>
            </div>
        </div>
    </div>

    <script>
        // ملء بيانات تجريبية
        function fillSampleData() {
            document.getElementById('firstNameAr').value = 'محمد';
            document.getElementById('firstNameFr').value = 'Mohamed';
            document.getElementById('familyNameAr').value = 'العلوي';
            document.getElementById('familyNameFr').value = 'Alaoui';
            document.getElementById('birthDateHijri').value = '25/06/1411';
            document.getElementById('birthDateGregorian').value = '1990-03-15';
            document.getElementById('birthPlaceAr').value = 'أيير';
            document.getElementById('birthPlaceFr').value = 'Ayir';
            document.getElementById('fatherName').value = 'عبد الله العلوي';
            document.getElementById('motherName').value = 'فاطمة الزهراني';
            document.getElementById('currentAddressAr').value = 'حي السلام';
            document.getElementById('currentAddressFr').value = 'Hay Salam';
            document.getElementById('marginalData').value = '';

            // بيانات العقد والإصدار
            document.getElementById('actNumber').value = '15/2024';
            document.getElementById('actYear').value = '2024';
            document.getElementById('issuingCommune').value = 'أيير';
            document.getElementById('issueDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('certificationDate').value = new Date().toISOString().split('T')[0];

            // بيانات الشاهد
            document.getElementById('witnessName').value = 'عبد الرحمن العلوي';
            document.getElementById('witnessAddress').value = 'حي النهضة أيير';

            // إظهار رسالة تأكيد
            alert('✅ تم ملء البيانات التجريبية بنجاح!');
        }

        // معالجة إرسال النموذج
        document.getElementById('personalIdForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // جمع البيانات
            const formData = {
                firstNameAr: document.getElementById('firstNameAr').value,
                firstNameFr: document.getElementById('firstNameFr').value,
                familyNameAr: document.getElementById('familyNameAr').value,
                familyNameFr: document.getElementById('familyNameFr').value,
                birthDateHijri: document.getElementById('birthDateHijri').value,
                birthDateGregorian: document.getElementById('birthDateGregorian').value,
                birthPlaceAr: document.getElementById('birthPlaceAr').value,
                birthPlaceFr: document.getElementById('birthPlaceFr').value,
                fatherName: document.getElementById('fatherName').value,
                motherName: document.getElementById('motherName').value,
                currentAddressAr: document.getElementById('currentAddressAr').value,
                currentAddressFr: document.getElementById('currentAddressFr').value,
                commune: document.getElementById('commune').value,
                province: document.getElementById('province').value,
                marginalData: document.getElementById('marginalData').value,

                // بيانات العقد والإصدار
                actNumber: document.getElementById('actNumber').value,
                actYear: document.getElementById('actYear').value,
                issuingCommune: document.getElementById('issuingCommune').value,
                issueDate: document.getElementById('issueDate').value,
                certificationDate: document.getElementById('certificationDate').value,

                // بيانات الشاهد
                witnessName: document.getElementById('witnessName').value,
                witnessAddress: document.getElementById('witnessAddress').value
            };

            // التحقق من البيانات المطلوبة
            const requiredFields = ['firstNameAr', 'firstNameFr', 'familyNameAr', 'familyNameFr',
                                  'birthDateHijri', 'birthDateGregorian', 'birthPlaceAr', 'birthPlaceFr',
                                  'fatherName', 'motherName', 'currentAddressAr'];

            let isValid = true;
            for (let field of requiredFields) {
                if (!formData[field] || formData[field].trim() === '') {
                    isValid = false;
                    document.getElementById(field).style.borderColor = '#dc3545';
                } else {
                    document.getElementById(field).style.borderColor = '#ddd';
                }
            }

            if (!isValid) {
                alert('⚠️ يرجى ملء جميع الحقول المطلوبة!');
                return;
            }

            // تحويل البيانات إلى URL parameters
            const params = new URLSearchParams(formData);

            // فتح صفحة البطاقة الشخصية مع البيانات
            window.open(`personal-id-card.html?${params.toString()}`, '_blank');
        });

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للحقول
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // تحويل التاريخ بين الهجري والميلادي
            setupDateConversion();

            // اختبار دقة التحويل (للتطوير)
            testConversionAccuracy();
        });

        // ===== دوال تحويل التاريخ بين الهجري والميلادي =====

        function setupDateConversion() {
            const hijriInput = document.getElementById('birthDateHijri');
            const gregorianInput = document.getElementById('birthDateGregorian');

            // تحويل من هجري إلى ميلادي
            hijriInput.addEventListener('input', function() {
                const hijriDate = this.value.trim();
                if (hijriDate && isValidHijriDate(hijriDate)) {
                    const gregorianDate = convertHijriToGregorian(hijriDate);
                    if (gregorianDate) {
                        gregorianInput.value = gregorianDate;
                        // إضافة تأثير بصري للتأكيد على التحويل
                        gregorianInput.classList.add('conversion-success');
                        setTimeout(() => {
                            gregorianInput.classList.remove('conversion-success');
                        }, 1500);

                        // إظهار رسالة نجاح صغيرة
                        showConversionMessage('✅ تم التحويل إلى الميلادي', gregorianInput);
                    }
                }
            });

            // تحويل من ميلادي إلى هجري
            gregorianInput.addEventListener('input', function() {
                const gregorianDate = this.value.trim();
                if (gregorianDate && isValidGregorianDate(gregorianDate)) {
                    const hijriDate = convertGregorianToHijri(gregorianDate);
                    if (hijriDate) {
                        hijriInput.value = hijriDate;
                        // إضافة تأثير بصري للتأكيد على التحويل
                        hijriInput.classList.add('conversion-success');
                        setTimeout(() => {
                            hijriInput.classList.remove('conversion-success');
                        }, 1500);

                        // إظهار رسالة نجاح صغيرة
                        showConversionMessage('✅ تم التحويل إلى الهجري', hijriInput);
                    }
                }
            });
        }

        // التحقق من صحة التاريخ الهجري
        function isValidHijriDate(dateStr) {
            const pattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
            const match = dateStr.match(pattern);
            if (!match) return false;

            const day = parseInt(match[1]);
            const month = parseInt(match[2]);
            const year = parseInt(match[3]);

            // التحقق من النطاقات الأساسية
            if (year < 1 || year > 1500 || month < 1 || month > 12 || day < 1) {
                return false;
            }

            // التحقق من عدد أيام الشهر
            const hijriMonthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
            let maxDays = hijriMonthDays[month - 1];

            // ذو الحجة في السنة الكبيسة له 30 يوم
            if (month === 12 && isHijriLeapYear(year)) {
                maxDays = 30;
            }

            return day <= maxDays;
        }

        // التحقق من صحة التاريخ الميلادي
        function isValidGregorianDate(dateStr) {
            const date = new Date(dateStr);
            return date instanceof Date && !isNaN(date) && dateStr === date.toISOString().split('T')[0];
        }

        // تحويل من هجري إلى ميلادي (دقيق)
        function convertHijriToGregorian(hijriDateStr) {
            try {
                const parts = hijriDateStr.split('/');
                if (parts.length !== 3) return null;

                const hijriDay = parseInt(parts[0]);
                const hijriMonth = parseInt(parts[1]);
                const hijriYear = parseInt(parts[2]);

                // استخدام خوارزمية تحويل محسنة
                // نقطة البداية: 1 محرم 1 هـ = 16 يوليو 622 م

                // حساب إجمالي الأيام من 1/1/1 هـ
                let totalDays = 0;

                // إضافة أيام السنوات الكاملة
                for (let year = 1; year < hijriYear; year++) {
                    totalDays += isHijriLeapYear(year) ? 355 : 354;
                }

                // إضافة أيام الأشهر الكاملة في السنة الحالية
                const hijriMonthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
                for (let month = 1; month < hijriMonth; month++) {
                    let daysInMonth = hijriMonthDays[month - 1];
                    // ذو الحجة في السنة الكبيسة له 30 يوم
                    if (month === 12 && isHijriLeapYear(hijriYear)) {
                        daysInMonth = 30;
                    }
                    totalDays += daysInMonth;
                }

                // إضافة الأيام في الشهر الحالي (ناقص 1 لأن اليوم الأول = 0)
                totalDays += hijriDay - 1;

                // نقطة البداية المصححة: 19 يوليو 622 م (بناء على المراجع الفلكية الدقيقة)
                const hijriEpoch = new Date(622, 6, 19); // 19 يوليو 622

                // حساب التاريخ الميلادي
                const gregorianDate = new Date(hijriEpoch.getTime() + totalDays * 24 * 60 * 60 * 1000);

                // تنسيق التاريخ
                const year = gregorianDate.getFullYear();
                const month = (gregorianDate.getMonth() + 1).toString().padStart(2, '0');
                const day = gregorianDate.getDate().toString().padStart(2, '0');

                return `${year}-${month}-${day}`;
            } catch (error) {
                console.error('خطأ في تحويل التاريخ الهجري:', error);
                return null;
            }
        }

        // تحديد ما إذا كانت السنة الهجرية كبيسة
        function isHijriLeapYear(year) {
            // دورة 30 سنة: السنوات الكبيسة هي 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
            // هذا النظام المعتمد في التقويم الهجري المدني
            const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
            const yearInCycle = year % 30;
            return leapYears.includes(yearInCycle);
        }

        // دالة مساعدة لتحسين دقة التحويل
        function getHijriEpochCorrection() {
            // تصحيح بناء على المراجع الفلكية الحديثة
            // 1 محرم 1 هـ = 16 يوليو 622 م (التقويم اليولياني)
            // = 19 يوليو 622 م (التقويم الغريغوري)
            return new Date(622, 6, 19); // 19 يوليو 622
        }

        // تحويل من ميلادي إلى هجري (دقيق)
        function convertGregorianToHijri(gregorianDateStr) {
            try {
                const date = new Date(gregorianDateStr);
                if (isNaN(date)) return null;

                // نقطة البداية المصححة: 19 يوليو 622 م
                const hijriEpoch = new Date(622, 6, 19);

                // حساب الفرق بالأيام
                const diffTime = date.getTime() - hijriEpoch.getTime();
                const diffDays = Math.floor(diffTime / (24 * 60 * 60 * 1000));

                if (diffDays < 0) return null; // تاريخ قبل بداية التقويم الهجري

                // حساب السنة الهجرية
                let hijriYear = 1;
                let remainingDays = diffDays;

                // تحسين: استخدام حلقة أكثر دقة
                while (true) {
                    const daysInYear = isHijriLeapYear(hijriYear) ? 355 : 354;
                    if (remainingDays >= daysInYear) {
                        remainingDays -= daysInYear;
                        hijriYear++;
                    } else {
                        break;
                    }
                }

                // حساب الشهر واليوم الهجري
                const hijriMonthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
                let hijriMonth = 1;

                while (hijriMonth <= 12) {
                    let daysInMonth = hijriMonthDays[hijriMonth - 1];

                    // ذو الحجة في السنة الكبيسة له 30 يوم
                    if (hijriMonth === 12 && isHijriLeapYear(hijriYear)) {
                        daysInMonth = 30;
                    }

                    if (remainingDays >= daysInMonth) {
                        remainingDays -= daysInMonth;
                        hijriMonth++;
                    } else {
                        break;
                    }
                }

                const hijriDay = remainingDays + 1;

                // التأكد من صحة النتائج
                if (hijriMonth > 12) {
                    hijriMonth = 12;
                    hijriDay = isHijriLeapYear(hijriYear) ? 30 : 29;
                }

                // تنسيق التاريخ الهجري
                const formattedDay = hijriDay.toString().padStart(2, '0');
                const formattedMonth = hijriMonth.toString().padStart(2, '0');

                return `${formattedDay}/${formattedMonth}/${hijriYear}`;
            } catch (error) {
                console.error('خطأ في تحويل التاريخ الميلادي:', error);
                return null;
            }
        }

        // دالة اختبار دقة التحويل (للتطوير)
        function testConversionAccuracy() {
            console.log('🧪 اختبار دقة التحويل:');

            // اختبار التاريخ المعطى: 17/06/2025 = 20/12/1446
            const testDate = '2025-06-17';
            const expectedHijri = '20/12/1446';
            const calculatedHijri = convertGregorianToHijri(testDate);

            console.log(`📅 التاريخ الميلادي: ${testDate}`);
            console.log(`🎯 الهجري المتوقع: ${expectedHijri}`);
            console.log(`🔢 الهجري المحسوب: ${calculatedHijri}`);
            console.log(`✅ دقيق: ${calculatedHijri === expectedHijri ? 'نعم' : 'لا'}`);

            // اختبار العكس
            const backToGregorian = convertHijriToGregorian(expectedHijri);
            console.log(`🔄 العودة للميلادي: ${backToGregorian}`);
            console.log(`✅ دقيق: ${backToGregorian === testDate ? 'نعم' : 'لا'}`);
        }

        // إظهار رسالة تأكيد التحويل
        function showConversionMessage(message, targetElement) {
            // إزالة أي رسالة سابقة
            const existingMessage = targetElement.parentNode.querySelector('.conversion-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // إنشاء رسالة جديدة
            const messageDiv = document.createElement('div');
            messageDiv.className = 'conversion-message';
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: absolute;
                top: -25px;
                right: 0;
                background: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                z-index: 1000;
                opacity: 0;
                transform: translateY(10px);
                transition: all 0.3s ease;
                pointer-events: none;
            `;

            // إضافة الرسالة
            targetElement.parentNode.style.position = 'relative';
            targetElement.parentNode.appendChild(messageDiv);

            // إظهار الرسالة
            setTimeout(() => {
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0)';
            }, 100);

            // إخفاء الرسالة بعد 2 ثانية
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 2000);
        }
    </script>
</body>
</html>
