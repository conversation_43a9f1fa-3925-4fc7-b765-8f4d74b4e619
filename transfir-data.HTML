<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <title>تحويل التاريخ الميلادي إلى الهجري</title>
  <style>
    body {
      font-family: '<PERSON><PERSON><PERSON>', sans-serif;
      background: #f0f0f0;
      text-align: center;
      padding: 40px;
      direction: rtl;
    }
    .container {
      background: #fff;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 0 10px #ccc;
      max-width: 400px;
      margin: auto;
    }
    input, button {
      padding: 10px;
      font-size: 16px;
      margin-top: 10px;
      width: 100%;
      box-sizing: border-box;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0056b3;
    }
    #result {
      margin-top: 20px;
      font-size: 18px;
      color: #333;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>تحويل التاريخ الميلادي إلى الهجري</h2>
    <input type="date" id="gregorianDate">
    <button onclick="convertToHijri()">تحويل</button>
    <div id="result"></div>
  </div>

  <script>
    function convertToHijri() {
      const input = document.getElementById("gregorianDate").value;
      if (!input) {
        document.getElementById("result").innerText = "الرجاء اختيار التاريخ أولاً.";
        return;
      }

      const date = new Date(input);
      const hijriDate = new Intl.DateTimeFormat('ar-SA-u-ca-islamic', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }).format(date);

      document.getElementById("result").innerText = `التاريخ الهجري: ${hijriDate}`;
    }
  </script>
</body>
</html>
