<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخة موجزة من رسم الوفاة</title>
    <style>
        body {
            font-family: 'Amiri', 'Traditional Arabic', 'Times New Roman', serif;
            margin: 0;
            padding: 3mm;
            font-size: 14pt;
            font-weight: 700;
            line-height: 1.4;
            background: #f5f5f5;
            color: #333;
        }
        .document {
            width: auto;
            max-width: 200mm;    /* زيادة عرض النموذج */
            min-height: 287mm;   /* زيادة ارتفاع النموذج */
            margin: 5mm auto;    /* تقليل الهوامش الخارجية */
            padding: 8mm;        /* زيادة الهوامش الداخلية */
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* توزيع المساحة */
            border: 1px solid #ccc;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
            overflow: visible;   /* السماح بالتمدد */
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10mm;
            padding-bottom: 8mm;
            font-size: 14pt;
            font-weight: 900;
        }
        .header-right {
            text-align: left;
            direction: ltr;
            flex: 1;
        }
        .header-center {
            text-align: center;
            flex: 1;
            margin: 0 5mm;
        }
        .header-left {
            text-align: right;
            direction: rtl;
            flex: 1;
            line-height: 1.3;
        }
        .header-left div {
            margin-bottom: 1mm;
            line-height: 1.3;
        }

        /* Unified content layout */
        .content {
            height: calc(100% - 20mm);
            padding: 2mm;
            display: flex;
            flex-direction: column;
        }

        /* Content lines - Merged */
        .merged-line {
            margin: 0.4mm 0;
            font-size: 11px;
            font-weight: 500;
            display: flex;
            align-items: baseline;
            justify-content: space-between;
        }
        .french-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: ltr;
            text-align: left;
        }
        .arabic-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: rtl;
            text-align: right;
        }
        .label {
            font-weight: 500;
            margin: 0 2mm;
            white-space: nowrap;
            min-width: 15mm;
            color: #333;
        }
        .underline {
            border-bottom: none;
            min-width: 15mm;
            padding: 0 1mm;
            flex: 1;
            min-height: 2mm;
            font-weight: 700;
            color: #000;
        }

        /* Test controls */
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .controls button {
            background: white;
            color: #007bff;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
        }

        /* تحسينات الأزرار */
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .load-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(17, 153, 142, 0.4);
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);
        }

        .preview-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(253, 121, 168, 0.4);
        }

        /* تحسينات إضافية للنموذج */
        .form-field:empty::before {
            content: ".....................";
            color: #ccc;
            font-weight: normal;
        }

        .form-field:not(:empty) {
            color: #2c3e50;
            font-weight: 600;
        }

        /* تحسينات إضافية للنموذج */
        .title-section {
            text-align: center;
            margin: 8mm 0;
            padding: 6mm 0;
        }

        .title-main {
            font-size: 14pt;
            font-weight: 900;
            color: #2c3e50;
            margin-bottom: 8mm;
        }

        .form-grid {
            direction: rtl;
            margin: 8mm 0;
            flex: 1;
        }

        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 6mm;
            direction: rtl;
        }

        .form-label {
            padding: 4mm 0;
            font-weight: 900;
            color: #444;
            font-size: 14pt;
            margin: 0;
            flex: none;                        /* لا تتمدد */
            white-space: nowrap;               /* لا تكسر السطر */
            margin-left: 2mm;                  /* مسافة صغيرة بعد التسمية */
        }

        .form-field {
            padding: 5mm 0;
            border-bottom: 2px solid #333;
            min-height: 8mm;
            font-weight: 900;
            font-size: 14pt;
            margin: 0;
            flex: 1;                           /* تأخذ باقي المساحة */
        }

        .certification-section {
            margin: 10mm 0 8mm 0;
            direction: rtl;
            line-height: 1.8;
            font-size: 14pt;
            font-weight: 700;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            min-width: 50mm;
            display: inline-block;
            margin-right: 25mm;
            height: 1.5mm;
        }

        .footer-section {
            display: flex;
            justify-content: space-between;
            margin-top: 10mm;
            direction: rtl;
            font-size: 14pt;
        }

        .signature-area {
            display: flex;
            justify-content: space-between;
            margin-top: 8mm;
            direction: rtl;
        }

        @media print {
            /* إعداد الصفحة للطباعة في ورقة A4 عمودي */
            @page {
                size: A4 portrait;
                margin: 0;
            }

            body {
                margin: 0;
                padding: 0;
                background: white !important;
                font-size: 14pt;
                line-height: 1.3;
                display: block;
                position: relative;
                font-family: 'Amiri', 'Traditional Arabic', 'Times New Roman', serif;
            }

            .document {
                width: 200mm;        /* زيادة عرض النموذج */
                height: 287mm;       /* زيادة ارتفاع النموذج */
                max-width: none;
                margin: 0;           /* إزالة margin auto */
                padding: 8mm;        /* زيادة الهوامش الداخلية */
                box-shadow: none;
                border: none;
                page-break-inside: avoid;
                position: absolute;  /* موضع مطلق للطباعة */
                left: -2mm;          /* إزاحة 1 سم نحو اليسار (8mm - 10mm) */
                right: 12mm;         /* زيادة الهامش من اليمين */
                top: 5mm;            /* تقليل الهامش من الأعلى */
                bottom: 5mm;         /* تقليل الهامش من الأسفل */
                overflow: visible;   /* السماح بالتمدد */
                display: flex;
                flex-direction: column;
                justify-content: space-between; /* توزيع المساحة */
            }

            .controls { display: none; }
            .print-controls { display: none !important; }

            /* تحسين الخطوط للطباعة */
            .title-main {
                font-size: 14pt;
                margin-bottom: 2mm;
            }

            .form-label {
                font-size: 14pt;
                font-weight: 900;
                padding: 1.3mm 0;
                margin: 0;
            }

            .form-field {
                font-size: 14pt;
                font-weight: 900;
                min-height: 3.5mm;
                padding: 1.5mm 0;
                margin: 0;
            }

            .header {
                font-size: 14pt;
                font-weight: 900;
                margin-bottom: 3mm;
                padding-bottom: 2.5mm;
            }

            .certification-section {
                font-size: 14pt;
                margin: 2.5mm 0 2mm 0;
                line-height: 1.3;
            }

            .footer-section {
                font-size: 14pt;
                margin-top: 2.5mm;
            }

            /* تحسينات إضافية للطباعة */
            .signature-area div[style*="dashed"] {
                border: none !important;
                height: 10mm !important;
                margin-top: 1.3mm !important;
            }

            .form-field {
                border-bottom: 2px solid #000 !important;
                min-height: 2.5mm !important;
            }

            .header {
                border-bottom: none !important;
            }

            .signature-line {
                min-width: 55mm !important;
                margin-left: 25mm !important;
                height: 1.3mm !important;
            }

            /* ضمان عدم تجاوز المحتوى للحدود */
            .content-wrapper {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                overflow: visible;
            }

            .form-row {
                display: flex !important;
                align-items: center !important;
                margin-bottom: 3mm !important;
                direction: rtl !important;
            }

            .form-label {
                flex: none !important;
                white-space: nowrap !important;
                margin-left: 2mm !important;
            }

            .form-field {
                flex: 1 !important;
            }

            .title-section {
                margin: 4mm 0 !important;
                padding: 3mm 0 !important;
            }

            /* تحديد موضع الطباعة */
            @page {
                margin: 5mm;
                size: A4 portrait;
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            /* ضمان الطباعة في ورقة A4 عمودي */
            html, body {
                width: 210mm;  /* عرض A4 عمودي */
                height: 297mm; /* ارتفاع A4 عمودي */
                margin: 0;
                padding: 0;
                overflow: hidden;
                direction: ltr;  /* اتجاه من اليسار لليمين */
            }

            /* إزالة منطقة النصف - الآن نستخدم الصفحة كاملة */
        }

        /* تحسينات إضافية للواجهة */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>
</head>
<body>


    <div class="document">
        <!-- Header -->
        <div class="header">
            <div class="header-right">
            </div>
            <div class="header-center">
            </div>
            <div class="header-left">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
                <div>عقد رقم : ......../...........</div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-wrapper" style="font-size: 14pt; width: 100%; direction: rtl;">

            <!-- Title Section -->
            <div class="title-section">
                <div class="title-main">
                    نسخة موجزة من رسم الوفاة
                </div>
            </div>

            <!-- Form Grid -->
            <div class="form-grid">
                <div class="form-row">
                    <div class="form-label">توفي(ت) بـ:</div>
                    <div class="form-field" id="deathPlaceAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">في:</div>
                    <div class="form-field" id="deathDateAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">الاسم الشخصي:</div>
                    <div class="form-field" id="firstNameAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">الاسم العائلي:</div>
                    <div class="form-field" id="familyNameAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">تاريخ الولادة:</div>
                    <div class="form-field" id="birthDateAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">مكان الولادة:</div>
                    <div class="form-field" id="birthPlaceAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">مهنته(ها):</div>
                    <div class="form-field" id="professionAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">الساكن(ة):</div>
                    <div class="form-field" id="residenceAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">والده(ا):</div>
                    <div class="form-field" id="fatherNameAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">والدته(ا):</div>
                    <div class="form-field" id="motherNameAr"></div>
                </div>

                <div class="form-row">
                    <div class="form-label">نشهد بصفتنا ضابطا الحالة المدنية نحن:</div>
                    <div class="form-field" id="certificationAr">ضابط الحالة المدنية - أيير</div>
                </div>
            </div>

            <!-- Certification Section -->
            <div class="certification-section">
                <div style="margin-top: 2mm;">
                    <span>بمطابقة هذه النسخة لما هو مضمن في سجلات الحالة المدنية بالمكتب المذكور</span>
                </div>
            </div>

            <!-- Footer Section -->
            <div class="footer-section">
                <div style="text-align: left; flex: 1;">
                    <!-- مساحة فارغة للطابع -->
                </div>
                <div style="text-align: right; flex: 1;">
                    <span style="font-weight: 600;">أيير في: </span>
                    <span id="currentDate" style="font-weight: 500;"></span>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="signature-area">
                <div style="text-align: left; flex: 1;">
                    <div style="height: 25mm; border: 1px dashed #ccc; margin-top: 5mm; display: flex; align-items: center; justify-content: center; color: #999; font-size: 14pt;">
                        طابع مكتب الحالة المدنية
                    </div>
                </div>
                <div style="text-align: right; flex: 1;">
                    <div style="height: 25mm; border: 1px dashed #ccc; margin-top: 5mm; display: flex; align-items: center; justify-content: center; color: #999; font-size: 14pt;">
                        ضابط الحالة المدنية
                    </div>
                </div>
            </div>

        </div>

        <!-- Print Button Section -->
        <div class="print-controls" style="text-align: center; margin-top: 15mm; padding: 8mm; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;">
            <div style="margin-bottom: 10px; color: #6c757d; font-size: 14px; font-weight: 500;">
                🎯 أدوات التحكم في النموذج
            </div>

            <button onclick="printCertificate()" class="print-btn" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 14px 35px;
                border-radius: 30px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
                transition: all 0.3s ease;
                margin: 5px 8px;
                min-width: 180px;
            ">
                🖨️ طباعة النموذج
            </button>

            <button onclick="loadFromDatabase()" class="load-btn" style="
                background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
                color: white;
                border: none;
                padding: 14px 35px;
                border-radius: 30px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 6px 20px rgba(17, 153, 142, 0.3);
                transition: all 0.3s ease;
                margin: 5px 8px;
                min-width: 180px;
            ">
                📊 تحميل من قاعدة البيانات
            </button>

            <button onclick="fillTestData()" class="test-btn" style="
                background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
                color: #8b4513;
                border: none;
                padding: 14px 35px;
                border-radius: 30px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 6px 20px rgba(252, 182, 159, 0.3);
                transition: all 0.3s ease;
                margin: 5px 8px;
                min-width: 180px;
            ">
                📋 مسح النموذج
            </button>

            <button onclick="showPrintPreview()" class="preview-btn" style="
                background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
                color: white;
                border: none;
                padding: 14px 35px;
                border-radius: 30px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 6px 20px rgba(253, 121, 168, 0.3);
                transition: all 0.3s ease;
                margin: 5px 8px;
                min-width: 180px;
            ">
                👁️ معاينة الطباعة
            </button>

            <div style="margin-top: 10px; font-size: 12px; color: #6c757d;">
                💡 النموذج محسّن للطباعة في نصف ورقة A4 أفقي
            </div>
        </div>

    </div>

    <script>
        // متغير لتتبع حالة تحميل قاعدة البيانات
        let dbReady = false;
        let pendingDataLoad = null;

        // استخدام نفس مدير قاعدة البيانات المستخدم في صفحة البحث
        // تحميل مدير قاعدة البيانات
        const script = document.createElement('script');
        script.src = 'indexeddb-manager.js';
        document.head.appendChild(script);

        // انتظار تحميل المدير ثم تهيئة قاعدة البيانات
        script.onload = async function() {
            try {
                await citizensDB.init();
                console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
                dbReady = true;

                // إذا كان هناك طلب معلق لتحميل البيانات، قم بتنفيذه الآن
                if (pendingDataLoad) {
                    console.log('🔄 تنفيذ طلب تحميل البيانات المعلق...');
                    await pendingDataLoad();
                    pendingDataLoad = null;
                }
            } catch (error) {
                console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            }
        };

        // ملء بيانات تجريبية (مسح النموذج)
        function fillTestData() {
            // مسح جميع الحقول - النموذج يبقى فارغ
            clearAllFields();
            console.log('🗑️ تم مسح النموذج - جاهز للملء اليدوي');

            // إظهار رسالة تأكيد
            showTemporaryMessage('✅ تم مسح النموذج بنجاح', 'success');
        }

        // وظيفة مسح جميع الحقول (جعل النموذج ورقة بيضاء)
        function clearAllFields() {
            // مسح الحقول الجديدة في النموذج المحدث
            const fieldsToClean = [
                'deathPlaceAr', 'deathDateAr', 'firstNameAr', 'familyNameAr',
                'birthDateAr', 'birthPlaceAr', 'professionAr', 'residenceAr',
                'fatherNameAr', 'motherNameAr', 'certificationAr'
            ];

            let clearedCount = 0;
            fieldsToClean.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.textContent = '';
                    element.style.backgroundColor = '#f8f9fa';
                    clearedCount++;

                    // إزالة التأثير البصري بعد ثانية
                    setTimeout(() => {
                        element.style.backgroundColor = '';
                    }, 1000);
                }
            });

            console.log(`🗑️ تم مسح ${clearedCount} حقل - النموذج ورقة بيضاء`);
        }

        // وظيفة معاينة الطباعة
        function showPrintPreview() {
            console.log('👁️ عرض معاينة الطباعة...');

            const dataCheck = checkIfFormHasData();

            if (!dataCheck.hasData) {
                showTemporaryMessage('⚠️ النموذج فارغ - لا توجد بيانات للمعاينة', 'error');
                return;
            }

            // إنشاء نافذة معاينة
            const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

            // نسخ محتوى الصفحة الحالية
            const documentClone = document.cloneNode(true);

            // إزالة أزرار التحكم من النسخة
            const printControls = documentClone.querySelector('.print-controls');
            if (printControls) {
                printControls.remove();
            }

            // إضافة عنوان للمعاينة
            const title = documentClone.querySelector('title');
            if (title) {
                title.textContent = 'معاينة الطباعة - نسخة موجزة من رسم الوفاة';
            }

            // كتابة المحتوى في النافذة الجديدة
            previewWindow.document.write(documentClone.documentElement.outerHTML);
            previewWindow.document.close();

            // إضافة زر طباعة في النافذة الجديدة
            previewWindow.document.body.insertAdjacentHTML('beforeend', `
                <div style="position: fixed; top: 10px; right: 10px; z-index: 10000;">
                    <button onclick="window.print()" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-weight: bold;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                    ">🖨️ طباعة</button>
                    <button onclick="window.close()" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-weight: bold;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                        margin-left: 5px;
                    ">✖️ إغلاق</button>
                </div>
            `);

            showTemporaryMessage('👁️ تم فتح معاينة الطباعة في نافذة جديدة', 'success');
        }

        // وظيفة إظهار رسالة مؤقتة
        function showTemporaryMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 600;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease-out;
            `;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // تحميل البيانات من قاعدة البيانات
        async function loadFromDatabase() {
            try {
                // التحقق من جاهزية قاعدة البيانات
                if (!dbReady) {
                    alert('⏳ قاعدة البيانات لم تكتمل تهيئتها بعد، يرجى المحاولة مرة أخرى');
                    return;
                }

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await citizensDB.init();
                }

                // البحث عن أول متوفى في قاعدة البيانات
                const allCitizens = await citizensDB.getAllCitizens();
                const deceasedRecords = allCitizens.filter(record => record.isDeceased === true);

                if (deceasedRecords.length > 0) {
                    const record = deceasedRecords[0]; // أول سجل متوفى
                    fillDataFromRecord(record);
                    alert(`✅ تم تحميل بيانات: ${record.personalName || record.firstNameAr} ${record.familyName || record.familyNameAr}`);
                } else {
                    alert('❌ لا توجد سجلات متوفين في قاعدة البيانات');
                }

            } catch (error) {
                console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
                alert('❌ خطأ في الاتصال بقاعدة البيانات: ' + error.message);
            }
        }

        // ملء البيانات من سجل قاعدة البيانات
        function fillDataFromRecord(record) {
            console.log('🚀 [الخطوة 1] بدء نقل البيانات من قاعدة البيانات إلى النموذج...');
            console.log('📊 البيانات المستلمة:', record);

            let successCount = 0;
            let failureCount = 0;
            const results = [];

            try {
                // بيانات الوفاة
                const deathInfo = record.deathInfo || {};

                // ملء الحقول في النموذج مع تتبع النجاح/الفشل

                // 1. مكان الوفاة
                try {
                    const deathPlace = deathInfo.deathPlace || '';
                    document.getElementById('deathPlaceAr').textContent = deathPlace;
                    results.push(`✅ مكان الوفاة: "${deathPlace}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ مكان الوفاة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 2. تاريخ الوفاة
                try {
                    const deathDate = formatArabicDate(deathInfo.deathDate) || '';
                    document.getElementById('deathDateAr').textContent = deathDate;
                    results.push(`✅ تاريخ الوفاة: "${deathDate}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ تاريخ الوفاة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 3. الاسم الشخصي
                try {
                    const firstName = record.personalName || record.firstNameAr || '';
                    document.getElementById('firstNameAr').textContent = firstName;
                    results.push(`✅ الاسم الشخصي: "${firstName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ الاسم الشخصي: فشل - ${e.message}`);
                    failureCount++;
                }

                // 4. الاسم العائلي
                try {
                    const familyName = record.familyName || record.familyNameAr || '';
                    document.getElementById('familyNameAr').textContent = familyName;
                    results.push(`✅ الاسم العائلي: "${familyName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ الاسم العائلي: فشل - ${e.message}`);
                    failureCount++;
                }

                // 5. تاريخ الولادة
                try {
                    const birthDate = formatArabicDate(record.birthDate) || '';
                    document.getElementById('birthDateAr').textContent = birthDate;
                    results.push(`✅ تاريخ الولادة: "${birthDate}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ تاريخ الولادة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 6. مكان الولادة
                try {
                    const birthPlace = record.birthPlace || record.birthPlaceAr || '';
                    document.getElementById('birthPlaceAr').textContent = birthPlace;
                    results.push(`✅ مكان الولادة: "${birthPlace}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ مكان الولادة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 7. المهنة
                try {
                    const profession = record.profession || '';
                    document.getElementById('professionAr').textContent = profession;
                    results.push(`✅ المهنة: "${profession}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ المهنة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 8. الساكن
                try {
                    const residenceElement = document.getElementById('residenceAr');
                    if (residenceElement) {
                        const residence = record.residence || record.birthPlace || record.birthPlaceAr || '';
                        residenceElement.textContent = residence;
                        results.push(`✅ الساكن: "${residence}"`);
                        successCount++;
                    } else {
                        results.push(`❌ الساكن: عنصر غير موجود`);
                        failureCount++;
                    }
                } catch (e) {
                    results.push(`❌ الساكن: فشل - ${e.message}`);
                    failureCount++;
                }

                // 9. اسم الوالد
                try {
                    const fatherName = record.fatherName || record.fatherNameAr || '';
                    document.getElementById('fatherNameAr').textContent = fatherName;
                    results.push(`✅ اسم الوالد: "${fatherName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ اسم الوالد: فشل - ${e.message}`);
                    failureCount++;
                }

                // 10. اسم الوالدة
                try {
                    const motherName = record.motherName || record.motherNameAr || '';
                    document.getElementById('motherNameAr').textContent = motherName;
                    results.push(`✅ اسم الوالدة: "${motherName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ اسم الوالدة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 11. التصديق
                try {
                    const certificationElement = document.getElementById('certificationAr');
                    if (certificationElement) {
                        certificationElement.textContent = 'ضابط الحالة المدنية - أيير';
                        results.push(`✅ التصديق: "ضابط الحالة المدنية - أيير"`);
                        successCount++;
                    } else {
                        results.push(`❌ التصديق: عنصر غير موجود`);
                        failureCount++;
                    }
                } catch (e) {
                    results.push(`❌ التصديق: فشل - ${e.message}`);
                    failureCount++;
                }

                // عرض النتائج
                console.log('📊 [الخطوة 1] تفاصيل نقل البيانات:');
                results.forEach(result => console.log(result));

                const totalFields = successCount + failureCount;
                const successRate = ((successCount / totalFields) * 100).toFixed(1);

                if (failureCount === 0) {
                    console.log(`🎉 [الخطوة 1] نجح بالكامل! تم نقل جميع البيانات (${successCount}/${totalFields}) بنسبة ${successRate}%`);
                    alert(`🎉 نجح نقل البيانات بالكامل!\n\n✅ تم نقل ${successCount} حقل بنجاح\n📊 نسبة النجاح: ${successRate}%\n\n🎯 جميع البيانات منقولة بسلاسة إلى النموذج!`);
                } else {
                    console.log(`⚠️ [الخطوة 1] نجح جزئياً: ${successCount} نجح، ${failureCount} فشل من أصل ${totalFields}`);
                    alert(`⚠️ نقل البيانات نجح جزئياً\n\n✅ نجح: ${successCount} حقل\n❌ فشل: ${failureCount} حقل\n📊 نسبة النجاح: ${successRate}%\n\nتحقق من وحدة التحكم للتفاصيل`);
                }

            } catch (error) {
                console.error('❌ [الخطوة 1] فشل كامل في نقل البيانات:', error);
                alert(`❌ فشل كامل في نقل البيانات!\n\nخطأ: ${error.message}\n\nتحقق من وحدة التحكم للتفاصيل`);
            }
        }

        // وظائف مساعدة للتنسيق والترجمة

        // حساب العمر
        function calculateAge(birthDate, deathDate) {
            if (!birthDate || !deathDate) return null;

            try {
                const birth = new Date(birthDate);
                const death = new Date(deathDate);

                if (isNaN(birth.getTime()) || isNaN(death.getTime())) return null;

                let age = death.getFullYear() - birth.getFullYear();
                const monthDiff = death.getMonth() - birth.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && death.getDate() < birth.getDate())) {
                    age--;
                }

                return age >= 0 ? age : null;
            } catch (error) {
                console.error('خطأ في حساب العمر:', error);
                return null;
            }
        }

        // تنسيق التاريخ بالعربية
        function formatArabicDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };

                return date.toLocaleDateString('ar-MA', options);
            } catch (error) {
                console.error('خطأ في تنسيق التاريخ العربي:', error);
                return dateString;
            }
        }

        // تنسيق التاريخ بالفرنسية
        function formatFrenchDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };

                return date.toLocaleDateString('fr-FR', options);
            } catch (error) {
                console.error('خطأ في تنسيق التاريخ الفرنسي:', error);
                return dateString;
            }
        }

        // ترجمة الجنس
        function translateGender(gender) {
            const translations = {
                'ذكر': 'Masculin',
                'أنثى': 'Féminin',
                'male': 'Masculin',
                'female': 'Féminin'
            };
            return translations[gender] || gender;
        }

        // ترجمة الحالة العائلية
        function translateMaritalStatus(status) {
            const translations = {
                'متزوج': 'Marié',
                'متزوجة': 'Mariée',
                'أعزب': 'Célibataire',
                'عزباء': 'Célibataire',
                'مطلق': 'Divorcé',
                'مطلقة': 'Divorcée',
                'أرمل': 'Veuf',
                'أرملة': 'Veuve'
            };
            return translations[status] || status;
        }

        // ترجمة عامة للفرنسية
        function translateToFrench(text) {
            if (!text) return '';

            const translations = {
                // الأماكن
                'الرباط': 'Rabat',
                'الدار البيضاء': 'Casablanca',
                'فاس': 'Fès',
                'مراكش': 'Marrakech',
                'أيير': 'Ayir',
                'أسفي': 'Safi',

                // المهن
                'مزارع': 'Agriculteur',
                'معلم': 'Enseignant',
                'طبيب': 'Médecin',
                'مهندس': 'Ingénieur',
                'تاجر': 'Commerçant',
                'عامل': 'Ouvrier',
                'موظف': 'Employé',
                'متقاعد': 'Retraité',

                // أسباب الوفاة
                'مرض طبيعي': 'Maladie naturelle',
                'حادث': 'Accident',
                'مرض مزمن': 'Maladie chronique',
                'شيخوخة': 'Vieillesse',
                'قلب': 'Cardiaque',
                'سرطان': 'Cancer'
            };

            return translations[text] || text;
        }

        // تحميل البيانات من URL عند تحميل الصفحة
        async function loadDataFromURL() {
            try {
                const urlParams = new URLSearchParams(window.location.search);

                // التحقق من وجود معرف المواطن
                if (urlParams.has('id')) {
                    const citizenId = decodeURIComponent(urlParams.get('id'));

                    // التحقق من جاهزية قاعدة البيانات
                    if (dbReady) {
                        console.log('📊 قاعدة البيانات جاهزة، تحميل البيانات مباشرة...');
                        await loadCitizenDataById(citizenId);
                    } else {
                        console.log('⏳ قاعدة البيانات غير جاهزة، إضافة الطلب للقائمة المعلقة...');
                        pendingDataLoad = async () => {
                            await loadCitizenDataById(citizenId);
                        };
                    }
                    return;
                }

                // تحميل البيانات من المعاملات المباشرة
                if (urlParams.has('firstNameAr') || urlParams.has('personalName')) {
                    fillDataFromURLParams(urlParams);
                }

            } catch (error) {
                console.error('خطأ في تحميل البيانات من URL:', error);
            }
        }

        // ملء البيانات من معاملات URL
        function fillDataFromURLParams(urlParams) {
            // البيانات العربية
            document.getElementById('firstNameAr').textContent = urlParams.get('firstNameAr') || urlParams.get('personalName') || '';
            document.getElementById('familyNameAr').textContent = urlParams.get('familyNameAr') || urlParams.get('familyName') || '';
            document.getElementById('birthDateAr').textContent = formatArabicDate(urlParams.get('birthDate')) || '';
            document.getElementById('birthPlaceAr').textContent = urlParams.get('birthPlaceAr') || urlParams.get('birthPlace') || '';
            document.getElementById('genderAr').textContent = urlParams.get('gender') || '';
            document.getElementById('fatherNameAr').textContent = urlParams.get('fatherNameAr') || urlParams.get('fatherName') || '';
            document.getElementById('motherNameAr').textContent = urlParams.get('motherNameAr') || urlParams.get('motherName') || '';
            document.getElementById('actNumberAr').textContent = urlParams.get('actNumber') || '';

            // بيانات الوفاة
            document.getElementById('deathDateAr').textContent = formatArabicDate(urlParams.get('deathDate')) || '';
            document.getElementById('deathPlaceAr').textContent = urlParams.get('deathPlace') || '';
            document.getElementById('ageAr').textContent = urlParams.get('age') || '';
            document.getElementById('professionAr').textContent = urlParams.get('profession') || '';
            document.getElementById('maritalStatusAr').textContent = urlParams.get('maritalStatus') || '';
            document.getElementById('deathCauseAr').textContent = urlParams.get('deathCause') || '';

            // البيانات الفرنسية
            document.getElementById('firstNameFr').textContent = urlParams.get('firstNameFr') || urlParams.get('personalName') || '';
            document.getElementById('familyNameFr').textContent = urlParams.get('familyNameFr') || urlParams.get('familyName') || '';
            document.getElementById('birthDateFr').textContent = formatFrenchDate(urlParams.get('birthDate')) || '';
            document.getElementById('birthPlaceFr').textContent = urlParams.get('birthPlaceFr') || translateToFrench(urlParams.get('birthPlace')) || '';
            document.getElementById('genderFr').textContent = translateGender(urlParams.get('gender')) || '';
            document.getElementById('fatherNameFr').textContent = urlParams.get('fatherNameFr') || urlParams.get('fatherName') || '';
            document.getElementById('motherNameFr').textContent = urlParams.get('motherNameFr') || urlParams.get('motherName') || '';
            document.getElementById('actNumberFr').textContent = urlParams.get('actNumber') || '';

            // بيانات الوفاة بالفرنسية
            document.getElementById('deathDateFr').textContent = formatFrenchDate(urlParams.get('deathDate')) || '';
            document.getElementById('deathPlaceFr').textContent = translateToFrench(urlParams.get('deathPlace')) || '';
            document.getElementById('ageFr').textContent = urlParams.get('age') || '';
            document.getElementById('professionFr').textContent = translateToFrench(urlParams.get('profession')) || '';
            document.getElementById('maritalStatusFr').textContent = translateMaritalStatus(urlParams.get('maritalStatus')) || '';
            document.getElementById('deathCauseFr').textContent = translateToFrench(urlParams.get('deathCause')) || '';
        }

        // تحميل بيانات مواطن بالمعرف
        async function loadCitizenDataById(citizenId) {
            try {
                console.log('🔍 البحث عن المواطن بالمعرف:', citizenId);

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    console.log('⚙️ تهيئة قاعدة البيانات...');
                    await citizensDB.init();
                }

                // الحصول على بيانات المواطن
                const record = await citizensDB.getCitizen(citizenId, true);

                if (record && record.isDeceased) {
                    console.log('✅ تم العثور على المواطن المتوفى:', record);
                    fillDataFromRecord(record);
                } else if (record && !record.isDeceased) {
                    alert('⚠️ هذا المواطن ليس متوفى');
                } else {
                    alert('❌ السجل غير موجود');
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                alert('❌ خطأ في تحميل بيانات المواطن: ' + error.message);
            }
        }

        // وظيفة الطباعة المحسنة
        function printCertificate() {
            console.log('🖨️ بدء عملية الطباعة المحسنة...');

            // التحقق من وجود بيانات في النموذج
            const dataCheck = checkIfFormHasData();

            if (!dataCheck.hasData) {
                const confirmPrint = confirm('⚠️ النموذج فارغ!\n\nهل تريد طباعة النموذج الفارغ؟\n\n💡 يمكنك تحميل البيانات من قاعدة البيانات أولاً');
                if (!confirmPrint) {
                    console.log('❌ تم إلغاء الطباعة - النموذج فارغ');
                    showTemporaryMessage('❌ تم إلغاء الطباعة', 'error');
                    return;
                }
            } else if (!dataCheck.isComplete) {
                const missingRequired = dataCheck.totalRequired - dataCheck.requiredFieldsFilled;
                const confirmPrint = confirm(`⚠️ بعض الحقول المطلوبة فارغة!\n\nالحقول المملوءة: ${dataCheck.filledFields}/${dataCheck.totalFields}\nالحقول المطلوبة المفقودة: ${missingRequired}\n\nهل تريد المتابعة مع الطباعة؟`);
                if (!confirmPrint) {
                    console.log('❌ تم إلغاء الطباعة - حقول مطلوبة مفقودة');
                    showTemporaryMessage('❌ يرجى ملء الحقول المطلوبة', 'error');
                    return;
                }
            }

            // تحضير النموذج للطباعة بدون رسالة

            // تحديث التواريخ
            updatePrintDate();
            updateCurrentDate();

            // حفظ العنوان الأصلي
            const originalTitle = document.title;

            // تغيير العنوان لإخفائه من الطباعة
            document.title = '';

            // إخفاء عناصر التحكم
            const printControls = document.querySelector('.print-controls');
            const originalDisplay = printControls.style.display;
            printControls.style.display = 'none';

            // إخفاء الحدود المنقطة في الطباعة
            const dashedBorders = document.querySelectorAll('[style*="dashed"]');
            const originalBorderStyles = [];
            dashedBorders.forEach((element, index) => {
                originalBorderStyles[index] = element.style.border;
                element.style.border = 'none';
            });

            // طباعة النموذج
            try {
                setTimeout(() => {
                    window.print();
                    console.log('✅ تم إرسال النموذج للطباعة');
                    showTemporaryMessage('✅ تم إرسال النموذج للطباعة', 'success');
                }, 500);
            } catch (error) {
                console.error('❌ خطأ في الطباعة:', error);
                showTemporaryMessage('❌ حدث خطأ أثناء الطباعة', 'error');
            } finally {
                // إعادة الإعدادات الأصلية
                setTimeout(() => {
                    document.title = originalTitle;
                    printControls.style.display = originalDisplay;

                    // إعادة الحدود المنقطة
                    dashedBorders.forEach((element, index) => {
                        element.style.border = originalBorderStyles[index];
                    });
                }, 1500);
            }
        }

        // التحقق من وجود بيانات في النموذج مع تفاصيل أكثر
        function checkIfFormHasData() {
            const fieldsToCheck = [
                { id: 'deathPlaceAr', name: 'مكان الوفاة', required: true },
                { id: 'deathDateAr', name: 'تاريخ الوفاة', required: true },
                { id: 'firstNameAr', name: 'الاسم الشخصي', required: true },
                { id: 'familyNameAr', name: 'الاسم العائلي', required: true },
                { id: 'birthDateAr', name: 'تاريخ الولادة', required: false },
                { id: 'birthPlaceAr', name: 'مكان الولادة', required: false },
                { id: 'professionAr', name: 'المهنة', required: false },
                { id: 'residenceAr', name: 'السكن', required: false },
                { id: 'fatherNameAr', name: 'اسم الوالد', required: false },
                { id: 'motherNameAr', name: 'اسم الوالدة', required: false }
            ];

            let hasData = false;
            let filledFields = 0;
            let requiredFieldsFilled = 0;
            let totalRequired = fieldsToCheck.filter(field => field.required).length;

            fieldsToCheck.forEach(field => {
                const element = document.getElementById(field.id);
                if (element && element.textContent.trim() !== '') {
                    hasData = true;
                    filledFields++;
                    if (field.required) {
                        requiredFieldsFilled++;
                    }
                }
            });

            console.log(`📊 إحصائيات النموذج: ${filledFields}/${fieldsToCheck.length} حقل مملوء، ${requiredFieldsFilled}/${totalRequired} حقل مطلوب مملوء`);

            return {
                hasData: hasData,
                filledFields: filledFields,
                totalFields: fieldsToCheck.length,
                requiredFieldsFilled: requiredFieldsFilled,
                totalRequired: totalRequired,
                isComplete: requiredFieldsFilled === totalRequired
            };
        }

        // تحديث تاريخ الطباعة
        function updatePrintDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            };

            const arabicDate = now.toLocaleDateString('ar-MA', options);
            const printDateElement = document.getElementById('printDate');

            if (printDateElement) {
                printDateElement.textContent = arabicDate;
                console.log('📅 تم تحديث تاريخ الطباعة:', arabicDate);
            }
        }

        // تحديث التاريخ الحالي أمام "أيير في"
        function updateCurrentDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };

            const arabicDate = now.toLocaleDateString('ar-MA', options);
            const currentDateElement = document.getElementById('currentDate');

            if (currentDateElement) {
                currentDateElement.textContent = arabicDate;
                console.log('📅 تم تحديث التاريخ الحالي:', arabicDate);
            }
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل صفحة نسخة موجزة من رسم الوفاة...');

            // مسح النموذج ليصبح ورقة بيضاء
            clearAllFields();

            // تحديث التاريخ الحالي عند تحميل الصفحة
            updateCurrentDate();

            // تحميل البيانات من URL إذا كانت موجودة
            loadDataFromURL();
        });





        console.log('📋 تم تحميل نموذج نسخة موجزة من رسم الوفاة بنجاح');
    </script>
</body>
</html>
