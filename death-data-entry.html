<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخة موجزة من رسم الوفاة - مكتب الحالة المدنية أيير</title>
    <style>
        /* ===== GENERAL STYLES ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Amiri', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
        }

        /* ===== CONTAINER & LAYOUT ===== */
        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }

        /* ===== HEADER STYLES ===== */
        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 20px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 50%;
            font-size: 2.5em;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
        }

        .header-text {
            text-align: right;
        }

        .header-text h1 {
            font-size: 2.2em;
            margin: 0 0 5px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        /* ===== RESPONSIVE HEADER ===== */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.8em;
            }

            .header-nav-links {
                gap: 6px;
                justify-content: center;
            }

            .header-nav-link {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }



        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }



        /* ===== MAIN CONTENT LAYOUT ===== */
        .content {
            flex: 1;
            background: white;
            padding: 30px;
            overflow-y: auto;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
        }

        /* ===== FORM STYLES ===== */
        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 18px;
            font-size: 1.2em;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.9em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 0 3px rgba(196, 30, 58, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* ===== BUTTON STYLES ===== */
        .btn {
            padding: 8px 18px;
            border: none;
            border-radius: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 3px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 18px;
            gap: 8px;
        }

        /* ===== ALERT STYLES ===== */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* ===== CERTIFICATE PREVIEW STYLES ===== */
        .preview-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 18px;
            border: 1px solid #e9ecef;
            height: fit-content;
        }

        .certificate-preview {
            min-height: 500px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: white;
            margin-bottom: 15px;
            overflow: auto;
            position: relative;
            padding: 12px;
        }

        .preview-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 30px;
        }

        /* ===== CERTIFICATE CONTENT STYLES ===== */
        .certificate-content {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 20px; /* زيادة الحشو */
            font-family: 'Times New Roman', serif;
            line-height: 1.7; /* زيادة المسافة بين الأسطر */
            background: white;
            border: 2px solid #000;
            min-height: 450px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            font-size: 14px; /* تكبير الخط الأساسي */
        }

        .certificate-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
            padding-bottom: 5px;
        }

        .header-left {
            text-align: left;
            font-size: 13px; /* تكبير من 11px إلى 13px */
            line-height: 1.6;
            font-weight: 600; /* زيادة السمك */
        }

        .header-right {
            text-align: right;
            font-size: 13px; /* تكبير من 11px إلى 13px */
            line-height: 1.6;
            font-weight: 600; /* زيادة السمك */
        }

        .certificate-title {
            text-align: center;
            font-size: 20px; /* تكبير من 18px إلى 20px */
            font-weight: bold;
            margin: 8px 0; /* زيادة المسافة */
            color: #000;
        }

        .certificate-body {
            font-size: 15px; /* تكبير من 13px إلى 15px */
            line-height: 1.8; /* زيادة المسافة بين الأسطر */
            text-align: right;
            margin: 8px 0; /* زيادة المسافة */
        }

        .field-line {
            margin-bottom: 10px; /* زيادة المسافة بين الحقول */
            display: flex;
            align-items: baseline;
            width: 100%;
        }

        .field-label {
            font-weight: 600;
            margin-left: 10px; /* زيادة المسافة */
            white-space: nowrap;
            min-width: 120px; /* زيادة العرض */
            font-size: 15px; /* تكبير من 13px إلى 15px */
            color: #333; /* لون أفتح للتسميات */
        }

        .field-value {
            flex: 1;
            min-height: 24px; /* زيادة الارتفاع */
            padding: 2px 6px; /* زيادة الحشو */
            color: #000; /* لون أسود قوي للقيم */
            font-size: 15px; /* تكبير من 13px إلى 15px */
            font-weight: 700; /* تسميك القيم المهمة */
        }



        .certificate-footer {
            margin-top: 40px; /* زيادة المسافة العلوية */
            display: flex;
            justify-content: space-between;
            align-items: end;
            font-size: 12px; /* تكبير من 10px إلى 12px */
            padding-top: 20px; /* زيادة الحشو */
        }

        .footer-left {
            text-align: left;
            line-height: 1.5; /* زيادة المسافة بين الأسطر */
            font-weight: 600; /* زيادة السمك */
        }

        .footer-right {
            text-align: right;
            line-height: 1.5; /* زيادة المسافة بين الأسطر */
            font-weight: 600; /* زيادة السمك */
        }

        .certification-text {
            text-align: center;
            margin: 30px 0; /* زيادة المسافة */
            font-size: 14px; /* تكبير من 12px إلى 14px */
            line-height: 1.8; /* زيادة المسافة بين الأسطر */
            font-weight: 600; /* زيادة السمك */
            color: #333;
            padding: 15px; /* زيادة الحشو */
            background: #f9f9f9;
            border-radius: 6px; /* زيادة الانحناء */
            border: 1px solid #e0e0e0;
        }

        /* ===== CERTIFICATE IMAGE STYLES ===== */
        .certificate-image {
            max-width: 100%;
            max-height: 600px;
            width: auto;
            height: auto;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            cursor: zoom-in;
            transition: transform 0.3s ease;
        }

        .certificate-image:hover {
            transform: scale(1.02);
        }

        .image-container {
            text-align: center;
            position: relative;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .image-info {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            color: #6c757d;
        }

        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }

        .zoom-btn {
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .zoom-btn:hover {
            background: rgba(0,0,0,0.9);
        }

        /* ===== ENHANCED IMAGE CONTROLS ===== */
        #imageContainer {
            max-height: 600px !important;
            max-width: 100% !important;
        }

        /* When zoomed, keep image within container bounds */
        #certificateImg.zoomed {
            width: 100% !important;
            height: 100% !important;
            max-width: 100% !important;
            max-height: none !important;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
        }

        /* ===== FOOTER ===== */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a, #006233);
        }

        .footer p {
            margin: 0;
            font-weight: 500;
            opacity: 0.9;
        }



        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .content {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .content {
                padding: 15px;
            }

            .main-content {
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">⚱️ تسجيل بيانات الوفاة</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Header Navigation -->
            <div class="header-navigation">
                <div class="header-nav-content">
                    <div class="header-nav-links">
                        <a href="main-dashboard.html" class="header-nav-link">🏠 الصفحة الرئيسية</a>
                        <a href="citizens-database-indexeddb.html" class="header-nav-link">📝 إدارة البيانات</a>
                        <a href="search-citizens.html" class="header-nav-link">🔍 البحث في السجلات</a>
                        <a href="personal-id-form.html" class="header-nav-link">🆔 البطاقة الشخصية</a>
                        <a href="death-data-entry.html" class="header-nav-link active">⚱️ تسجيل الوفاة</a>
                        <a href="employee-management.html" class="header-nav-link">👥 إدارة الموظفين</a>
                        <a href="data-transfer.html" class="header-nav-link">🔄 و 🛡️ النسخ الاحتياطية</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="main-content">
            <!-- Left Panel - Forms -->
            <div class="left-panel">
                <div id="alertContainer"></div>



                <!-- معلومات الوفاة الأساسية -->
                <div class="form-section">
                    <h2>⚱️ معلومات الوفاة الأساسية</h2>

                    <!-- Hidden Fields for Edit Mode -->
                    <input type="hidden" id="isDeceased" value="true">
                    <input type="hidden" id="originalActNumber" value="">
                    <input type="hidden" id="editMode" value="false">
                    <input type="hidden" id="citizenId" value="">

                    <form id="deathForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="deathPlace">توفي(ت) بـ <span class="required">*</span>:</label>
                                <input type="text" id="deathPlace" name="deathPlace" required placeholder="مكان الوفاة">
                            </div>

                            <div class="form-group">
                                <label for="deathDate">في <span class="required">*</span>:</label>
                                <input type="date" id="deathDate" name="deathDate" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="deathTime">الساعة:</label>
                                <input type="time" id="deathTime" name="deathTime" placeholder="ساعة الوفاة">
                            </div>

                            <div class="form-group">
                                <label for="deathCause">سبب الوفاة:</label>
                                <input type="text" id="deathCause" name="deathCause" placeholder="سبب الوفاة">
                            </div>
                        </div>
                    </form>
                </div>

            <!-- معلومات الوفاة -->
            <div class="form-section">
                <h2>📝 معلومات الوفاة</h2>

                <div class="form-row">
                    <div class="form-group">
                        <label for="personalName">الاسم الشخصي <span class="required">*</span>:</label>
                        <input type="text" id="personalName" name="personalName" required placeholder="الاسم الشخصي">
                    </div>

                    <div class="form-group">
                        <label for="familyName">الاسم العائلي <span class="required">*</span>:</label>
                        <input type="text" id="familyName" name="familyName" required placeholder="الاسم العائلي">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="deathPlace">توفي(ت) بـ <span class="required">*</span>:</label>
                        <input type="text" id="deathPlace" name="deathPlace" required placeholder="مكان الوفاة">
                    </div>

                    <div class="form-group">
                        <label for="deathDate">في <span class="required">*</span>:</label>
                        <input type="date" id="deathDate" name="deathDate" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="birthDate">تاريخ الولادة:</label>
                        <input type="date" id="birthDate" name="birthDate">
                    </div>

                    <div class="form-group">
                        <label for="birthPlace">مكان الولادة:</label>
                        <input type="text" id="birthPlace" name="birthPlace" placeholder="مكان الولادة">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="profession">مهنته(ها):</label>
                        <input type="text" id="profession" name="profession" placeholder="المهنة">
                    </div>

                    <div class="form-group">
                        <label for="residence">الساكن(ة):</label>
                        <input type="text" id="residence" name="residence" placeholder="مكان السكن">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="fatherName">والده(ا):</label>
                        <input type="text" id="fatherName" name="fatherName" placeholder="اسم الوالد">
                    </div>

                    <div class="form-group">
                        <label for="motherName">والدته:</label>
                        <input type="text" id="motherName" name="motherName" placeholder="اسم الوالدة">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="certificationOfficer">نشهد بصفتنا ضابطا الحالة المدنية نحن:</label>
                        <input type="text" id="certificationOfficer" name="certificationOfficer" placeholder="اسم ضابط الحالة المدنية" value="ضابط الحالة المدنية - أيير">
                    </div>

                    <div class="form-group">
                        <label for="registrationDate">تاريخ التسجيل:</label>
                        <input type="date" id="registrationDate" name="registrationDate">
                    </div>
                </div>

                <!-- حقول مخفية للتوافق مع النظام -->
                <div style="display: none;">
                    <input type="text" id="personalNameFr" name="personalNameFr">
                    <input type="text" id="familyNameFr" name="familyNameFr">
                    <input type="text" id="birthPlaceFr" name="birthPlaceFr">
                    <input type="text" id="hijriDate" name="hijriDate">
                    <input type="text" id="actNumber" name="actNumber" readonly>
                    <input type="text" id="gender" name="gender">
                    <input type="text" id="maritalStatus" name="maritalStatus">
                    <input type="text" id="nationality" name="nationality" value="مغربية">
                    <input type="number" id="age" name="age">
                    <input type="time" id="deathTime" name="deathTime">
                    <input type="text" id="deathCause" name="deathCause">
                </div>
            </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="extractDataFromImage()" id="extractBtn" disabled style="display: none;">📤 نقل المعلومات من الصورة</button>
                    <button type="button" class="btn btn-success" onclick="saveDeathRecord()">💾 حفظ البيانات</button>
                    <button type="button" class="btn btn-warning" onclick="printCertificate()" disabled id="printBtn">🖨️ طباعة الشهادة</button>
                    <button type="button" class="btn btn-info" onclick="openDeathCertificate()" disabled id="deathCertBtn">📋 نسخة موجزة من رسم الوفاة</button>
                    <button type="button" class="btn btn-primary" onclick="clearForm()">🗑️ مسح النموذج</button>
                </div>

                <!-- حقول مخفية لوضع التعديل -->
                <input type="hidden" id="editMode" value="false">
                <input type="hidden" id="citizenId" value="">
                <input type="hidden" id="originalActNumber" value="">
                <input type="hidden" id="isDeceased" value="true">
            </div>

            <!-- Right Panel - Certificate Image Upload -->
            <div class="right-panel">
                <div class="preview-section">
                    <h2 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #c41e3a; padding-bottom: 8px; text-align: center; font-size: 1.2em;">📄 تحميل شهادة الوفاة</h2>

                    <!-- Upload Section -->
                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
                        <div style="text-align: center; margin-bottom: 15px;">
                            <h5 style="color: #495057; margin-bottom: 10px;">📤 رفع صورة شهادة الوفاة</h5>
                            <p style="color: #6c757d; font-size: 14px; margin-bottom: 15px;">اختر صورة شهادة الوفاة لعرضها ونقل المعلومات منها</p>

                            <input type="file" id="certificateImageInput" accept="image/*,.pdf" style="display: none;" onchange="handleCertificateUpload(event)">
                            <button type="button" onclick="document.getElementById('certificateImageInput').click()"
                                    style="background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; margin: 5px;">
                                📁 اختيار ملف
                            </button>
                            <button type="button" onclick="clearCertificateImage()"
                                    style="background: #dc3545; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; margin: 5px;">
                                🗑️ مسح الصورة
                            </button>
                        </div>

                        <div style="text-align: center; font-size: 12px; color: #6c757d;">
                            <strong>الصيغ المدعومة:</strong> JPG, PNG, PDF | <strong>الحد الأقصى:</strong> 10MB
                        </div>
                    </div>

                    <!-- Certificate Display Area -->
                    <div class="certificate-preview" id="certificatePreview">
                        <!-- Default placeholder -->
                        <div id="certificateImageDisplay" style="text-align: center; padding: 40px; color: #6c757d; border: 2px dashed #dee2e6; border-radius: 8px; background: #f8f9fa;">
                            <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                            <h4 style="color: #495057; margin-bottom: 10px;">لم يتم تحميل شهادة وفاة بعد</h4>
                            <p style="margin-bottom: 0;">اضغط على "اختيار ملف" لتحميل صورة شهادة الوفاة</p>
                        </div>

                        <!-- Hidden certificate content for reference -->
                        <div class="certificate-content" style="display: none;">
                            <!-- Header -->
                            <div class="certificate-header">
                                <div class="header-left">
                                    المملكة المغربية<br>
                                    وزارة الداخلية<br>
                                    (إقليم أسفي)<br>
                                    جماعة أيير<br>
                                    مكتب الحالة المدنية :<br>
                                    ............./............. : عقد رقم
                                </div>
                                <div class="header-right">
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                </div>
                            </div>

                            <!-- Title -->
                            <div class="certificate-title">
                                نسخة موجزة من رسم الوفاة
                            </div>

                            <!-- Body -->
                            <div class="certificate-body">
                                <div class="field-line">
                                    <span class="field-label">توفي(ت) بـ :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">في :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الاسم الشخصي :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الاسم العائلي :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">تاريخ الازدياد :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">مكان الازدياد :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">مهنته (ها) :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الساكن (ة) بـ :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">والده :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">والدته :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">نشهد بصفتنا ضابطا للحالة المدنية نحن :</span>
                                    <span class="field-value">.............................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">بمطابقة هذه النسخة لما هو مضمن في سجلات الحالة المدنية بالمكتب المذكور</span>
                                    <span class="field-value"></span>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="certificate-footer">
                                <div class="footer-right">
                                    أيير : في ................................................................<br>
                                    ضابط الحالة المدنية
                                </div>
                                <div class="footer-left">
                                    طابع مكتب الحالة المدنية<br>
                                    ضابط الحالة المدنية
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Actions -->
                    <div style="text-align: center; margin-top: 12px;">
                        <button type="button" class="btn btn-success" disabled style="font-size: 0.85em; padding: 6px 14px;">🖨️ طباعة الشهادة</button>
                        <button type="button" class="btn btn-primary" disabled style="font-size: 0.85em; padding: 6px 14px;">📄 تصدير PDF</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 نظام الحالة المدنية - جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- تضمين مدير قاعدة البيانات المشترك -->
    <script src="indexeddb-manager.js"></script>

    <script>
        /* ===== JAVASCRIPT FUNCTIONS ===== */

        // متغيرات وضع التعديل
        let editingId = null;
        let isEditMode = false;

        // تعيين تاريخ اليوم كافتراضي
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تهيئة صفحة شهادة الوفاة...');

            const today = new Date().toISOString().split('T')[0];
            document.getElementById('deathDate').value = today;
            document.getElementById('registrationDate').value = today;

            // تهيئة متغيرات الصورة
            currentCertificateImage = null;
            currentZoom = 1;

            // تهيئة عرض الصورة
            displayCertificateImage();
            updateButtonStates();

            // التحقق من توفر citizensDB والتهيئة
            if (typeof citizensDB === 'undefined') {
                console.error('❌ citizensDB غير متاح - تأكد من تحميل indexeddb-manager.js');
                showAlert('❌ خطأ في تحميل نظام قاعدة البيانات', 'error');
                return;
            }

            console.log('✅ citizensDB متاح، بدء التهيئة...');

            // تهيئة قاعدة البيانات المشتركة
            citizensDB.init().then(() => {
                console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
                // فحص وضع التعديل
                checkEditMode();
                console.log('✅ تم تهيئة الصفحة بنجاح');
            }).catch(error => {
                console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
                showAlert('❌ خطأ في تهيئة قاعدة البيانات: ' + error.message, 'error');
            });

            // إضافة مستمعي الأحداث لتحديث حالة الأزرار
            const personalNameField = document.getElementById('personalName');
            const familyNameField = document.getElementById('familyName');

            if (personalNameField && familyNameField) {
                personalNameField.addEventListener('input', updateButtonStates);
                familyNameField.addEventListener('input', updateButtonStates);
            }
        });

        // فحص وضع التعديل وتحميل البيانات
        async function checkEditMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const editParam = urlParams.get('edit');
            const citizenId = urlParams.get('id');

            console.log('🔍 فحص وضع التعديل:', {
                editParam: editParam,
                citizenId: citizenId,
                allParams: Object.fromEntries(urlParams.entries())
            });

            if (editParam === 'true' && citizenId) {
                console.log('🔄 وضع التعديل مفعل للمواطن:', citizenId);
                editingId = citizenId;
                isEditMode = true;

                try {
                    // محاولة تحميل البيانات من قاعدة البيانات أولاً (مع الصورة)
                    console.log('📊 محاولة تحميل البيانات من قاعدة البيانات مع الصورة...');
                    await loadCitizenFromDatabase(citizenId, true); // true لتحميل الصورة
                } catch (error) {
                    console.warn('⚠️ فشل في تحميل البيانات من قاعدة البيانات، استخدام URL parameters:', error);
                    // في حالة الفشل، استخدم البيانات من URL
                    console.log('🔄 التبديل إلى تحميل البيانات من URL parameters...');
                    loadCitizenData(urlParams);
                    updateUIForEditMode();
                }
            } else {
                console.log('ℹ️ وضع الإضافة الجديدة (ليس تعديل)');
            }
        }

        // تحميل بيانات المواطن من قاعدة البيانات
        async function loadCitizenFromDatabase(citizenId, includeImage = true) {
            try {
                console.log('📋 تحميل بيانات المواطن من قاعدة البيانات:', citizenId, 'مع الصورة:', includeImage);

                // جلب بيانات المواطن من قاعدة البيانات مع الصورة
                const citizen = await citizensDB.getCitizen(citizenId, includeImage);

                if (!citizen) {
                    throw new Error(`لم يتم العثور على المواطن بالمعرف: ${citizenId}`);
                }

                console.log('✅ تم العثور على المواطن:', citizen.firstNameAr || citizen.personalName);
                console.log('🖼️ حالة الصورة:', {
                    hasDeathCertificateImage: !!citizen.deathCertificateImage,
                    hasCertificateImage: !!citizen.certificateImage,
                    imageType: citizen.deathCertificateImage ? 'deathCertificate' : citizen.certificateImage ? 'certificate' : 'none'
                });

                // التحقق من حالة الوفاة
                if (!citizen.isDeceased && !citizen.deathInfo) {
                    throw new Error('هذا المواطن ليس متوفى');
                }

                // تحميل البيانات في النموذج
                loadCitizenDataToForm(citizen);

                // تحميل صورة شهادة الوفاة إذا كانت موجودة
                if (includeImage) {
                    loadDeathCertificateImage(citizen);
                }

                // تحديث واجهة المستخدم لوضع التعديل
                updateUIForEditMode();

                console.log('✅ تم تحميل بيانات المواطن بنجاح من قاعدة البيانات');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن من قاعدة البيانات:', error);
                throw error;
            }
        }

        // تحميل صورة شهادة الوفاة
        function loadDeathCertificateImage(citizen) {
            try {
                console.log('🖼️ محاولة تحميل صورة شهادة الوفاة...');

                // البحث عن صورة شهادة الوفاة في أماكن مختلفة
                let imageData = null;
                let imageName = 'شهادة الوفاة';

                // الأولوية الأولى: deathCertificateImage
                if (citizen.deathCertificateImage) {
                    if (typeof citizen.deathCertificateImage === 'string') {
                        imageData = citizen.deathCertificateImage;
                    } else if (citizen.deathCertificateImage.data) {
                        imageData = citizen.deathCertificateImage.data;
                        imageName = citizen.deathCertificateImage.name || imageName;
                    }
                    console.log('✅ تم العثور على صورة في deathCertificateImage');
                }

                // الأولوية الثانية: certificateImage (للتوافق مع النظام القديم)
                else if (citizen.certificateImage) {
                    if (typeof citizen.certificateImage === 'string') {
                        imageData = citizen.certificateImage;
                    } else if (citizen.certificateImage.data) {
                        imageData = citizen.certificateImage.data;
                        imageName = citizen.certificateImage.name || imageName;
                    }
                    console.log('✅ تم العثور على صورة في certificateImage');
                }

                // إذا تم العثور على صورة، قم بتحميلها
                if (imageData) {
                    console.log('📤 تحميل الصورة في واجهة المستخدم...');

                    // تحديد نوع الملف من البيانات
                    let fileType = 'image/jpeg'; // افتراضي
                    if (imageData.startsWith('data:')) {
                        const mimeMatch = imageData.match(/data:([^;]+)/);
                        if (mimeMatch) {
                            fileType = mimeMatch[1];
                        }
                    }

                    // إنشاء كائن الصورة
                    currentCertificateImage = {
                        data: imageData,
                        name: imageName,
                        type: fileType,
                        size: Math.round(imageData.length * 0.75) // تقدير تقريبي للحجم
                    };

                    console.log('💾 تم تحميل بيانات الصورة:', {
                        name: currentCertificateImage.name,
                        type: currentCertificateImage.type,
                        estimatedSize: (currentCertificateImage.size / 1024 / 1024).toFixed(2) + ' MB'
                    });

                    // عرض الصورة
                    displayCertificateImage();
                    updateButtonStates();

                    console.log('✅ تم تحميل وعرض صورة شهادة الوفاة بنجاح');
                    showAlert('✅ تم تحميل صورة شهادة الوفاة المحفوظة', 'success');
                } else {
                    console.log('ℹ️ لا توجد صورة شهادة وفاة محفوظة');
                    // عرض الحالة الافتراضية
                    displayCertificateImage();
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل صورة شهادة الوفاة:', error);
                showAlert('⚠️ خطأ في تحميل صورة شهادة الوفاة: ' + error.message, 'error');
                // عرض الحالة الافتراضية في حالة الخطأ
                displayCertificateImage();
            }
        }

        // تحميل بيانات المواطن في النموذج
        function loadCitizenDataToForm(citizen) {
            try {
                console.log('📋 تحميل بيانات المواطن في النموذج...');
                console.log('👤 بيانات المواطن:', {
                    id: citizen.id,
                    firstNameAr: citizen.firstNameAr,
                    familyNameAr: citizen.familyNameAr,
                    deathInfo: citizen.deathInfo
                });

                // تحميل البيانات الأساسية
                const personalName = citizen.firstNameAr || citizen.personalName || '';
                const familyName = citizen.familyNameAr || citizen.familyName || '';
                const birthPlace = citizen.birthPlaceAr || citizen.birthPlace || '';
                const fatherName = citizen.fatherNameAr || citizen.fatherName || '';
                const motherName = citizen.motherNameAr || citizen.motherName || '';

                document.getElementById('personalName').value = personalName;
                document.getElementById('familyName').value = familyName;
                document.getElementById('birthDate').value = citizen.birthDate || '';
                document.getElementById('birthPlace').value = birthPlace;
                document.getElementById('profession').value = citizen.profession || '';
                document.getElementById('residence').value = citizen.residence || '';
                document.getElementById('fatherName').value = fatherName;
                document.getElementById('motherName').value = motherName;
                document.getElementById('registrationDate').value = citizen.registrationDate || '';

                // تحميل بيانات الوفاة
                if (citizen.deathInfo) {
                    document.getElementById('deathPlace').value = citizen.deathInfo.deathPlace || '';
                    document.getElementById('deathDate').value = citizen.deathInfo.deathDate || '';
                    document.getElementById('certificationOfficer').value = citizen.deathInfo.certificationOfficer || 'ضابط الحالة المدنية - أيير';
                } else {
                    document.getElementById('certificationOfficer').value = 'ضابط الحالة المدنية - أيير';
                }

                // تحميل البيانات المخفية للتوافق
                document.getElementById('personalNameFr').value = citizen.firstNameFr || '';
                document.getElementById('familyNameFr').value = citizen.familyNameFr || '';
                document.getElementById('birthPlaceFr').value = citizen.birthPlaceFr || '';
                document.getElementById('hijriDate').value = citizen.hijriDate || '';
                document.getElementById('actNumber').value = citizen.actNumber || '';
                document.getElementById('gender').value = citizen.gender || '';
                document.getElementById('maritalStatus').value = citizen.maritalStatus || '';
                document.getElementById('nationality').value = citizen.nationality || 'مغربية';
                document.getElementById('age').value = citizen.age || '';

                if (citizen.deathInfo) {
                    document.getElementById('deathTime').value = citizen.deathInfo.deathTime || '';
                    document.getElementById('deathCause').value = citizen.deathInfo.deathCause || '';
                }

                console.log('✅ تم تحميل البيانات في النموذج:', {
                    personalName: personalName,
                    familyName: familyName,
                    deathPlace: citizen.deathInfo ? citizen.deathInfo.deathPlace : '',
                    deathDate: citizen.deathInfo ? citizen.deathInfo.deathDate : ''
                });

                // ملء الحقول المخفية لوضع التعديل
                document.getElementById('editMode').value = 'true';
                document.getElementById('citizenId').value = citizen.id || '';
                document.getElementById('originalActNumber').value = citizen.actNumber || '';
                document.getElementById('isDeceased').value = 'true';

                console.log('🔧 تم تعيين الحقول المخفية:', {
                    editMode: 'true',
                    citizenId: citizen.id,
                    originalActNumber: citizen.actNumber,
                    isDeceased: 'true'
                });

                console.log('✅ تم تحميل بيانات المواطن في النموذج بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن في النموذج:', error);
                showAlert('خطأ في تحميل بيانات المواطن في النموذج', 'error');
            }
        }

        // تحميل بيانات المواطن للتعديل (من URL parameters - احتياطي)
        function loadCitizenData(urlParams) {
            try {
                console.log('📋 تحميل بيانات المواطن للتعديل من URL parameters...');
                console.log('🔍 البيانات المستلمة:', Object.fromEntries(urlParams.entries()));

                // تحميل البيانات الأساسية
                const personalName = urlParams.get('personalName') || urlParams.get('firstNameAr') || '';
                const familyName = urlParams.get('familyName') || urlParams.get('familyNameAr') || '';
                const birthPlace = urlParams.get('birthPlace') || urlParams.get('birthPlaceAr') || '';
                const fatherName = urlParams.get('fatherName') || urlParams.get('fatherNameAr') || '';
                const motherName = urlParams.get('motherName') || urlParams.get('motherNameAr') || '';

                document.getElementById('personalName').value = personalName;
                document.getElementById('familyName').value = familyName;
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('birthPlace').value = birthPlace;
                document.getElementById('profession').value = urlParams.get('profession') || '';
                document.getElementById('residence').value = urlParams.get('residence') || '';
                document.getElementById('fatherName').value = fatherName;
                document.getElementById('motherName').value = motherName;
                document.getElementById('registrationDate').value = urlParams.get('registrationDate') || '';
                document.getElementById('certificationOfficer').value = urlParams.get('certificationOfficer') || 'ضابط الحالة المدنية - أيير';

                // تحميل بيانات الوفاة
                document.getElementById('deathPlace').value = urlParams.get('deathPlace') || '';
                document.getElementById('deathDate').value = urlParams.get('deathDate') || '';

                // تحميل البيانات المخفية للتوافق
                document.getElementById('personalNameFr').value = urlParams.get('firstNameFr') || '';
                document.getElementById('familyNameFr').value = urlParams.get('familyNameFr') || '';
                document.getElementById('birthPlaceFr').value = urlParams.get('birthPlaceFr') || '';
                document.getElementById('hijriDate').value = urlParams.get('hijriDate') || '';
                document.getElementById('actNumber').value = urlParams.get('actNumber') || '';
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('maritalStatus').value = urlParams.get('maritalStatus') || '';
                document.getElementById('nationality').value = urlParams.get('nationality') || 'مغربية';
                document.getElementById('age').value = urlParams.get('age') || '';
                document.getElementById('deathTime').value = urlParams.get('deathTime') || '';
                document.getElementById('deathCause').value = urlParams.get('deathCause') || '';

                console.log('✅ تم تحميل البيانات في الحقول:', {
                    personalName: personalName,
                    familyName: familyName,
                    deathPlace: urlParams.get('deathPlace'),
                    deathDate: urlParams.get('deathDate'),
                    birthPlace: birthPlace
                });

                // ملء الحقول المخفية لوضع التعديل
                document.getElementById('editMode').value = 'true';
                document.getElementById('citizenId').value = urlParams.get('id') || '';
                document.getElementById('originalActNumber').value = urlParams.get('actNumber') || '';
                document.getElementById('isDeceased').value = 'true';

                console.log('🔧 تم تعيين الحقول المخفية:', {
                    editMode: 'true',
                    citizenId: urlParams.get('id'),
                    originalActNumber: urlParams.get('actNumber'),
                    isDeceased: 'true'
                });

                // تحديث واجهة المستخدم لوضع التعديل
                updateUIForEditMode();

                // ملء الحقول المخفية لوضع التعديل
                document.getElementById('editMode').value = 'true';
                document.getElementById('citizenId').value = urlParams.get('id') || '';
                document.getElementById('originalActNumber').value = urlParams.get('actNumber') || '';
                document.getElementById('isDeceased').value = 'true';

                console.log('✅ تم تحميل بيانات المواطن بنجاح من URL parameters');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                showAlert('خطأ في تحميل بيانات المواطن', 'error');
            }
        }

        // تحديث واجهة المستخدم لوضع التعديل
        function updateUIForEditMode() {
            // تحديث عنوان الصفحة
            const headerTitle = document.querySelector('.header-main h1');
            if (headerTitle) {
                headerTitle.innerHTML = '✏️ تعديل شهادة الوفاة';
                headerTitle.style.color = '#f39c12';
            }

            // تحديث عنوان القسم
            const sectionTitle = document.querySelector('.form-section h2');
            if (sectionTitle) {
                sectionTitle.innerHTML = '✏️ تعديل معلومات الوفاة';
                sectionTitle.style.color = '#f39c12';
            }

            // تحديث زر الحفظ
            const saveButton = document.querySelector('button[onclick="saveDeathRecord()"]');
            if (saveButton) {
                saveButton.innerHTML = '💾 تحديث البيانات';
                saveButton.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
            }

            // جعل رقم العقد للقراءة فقط في وضع التعديل
            const actNumberField = document.getElementById('actNumber');
            if (actNumberField) {
                actNumberField.readOnly = true;
                actNumberField.style.backgroundColor = '#f8f9fa';
                actNumberField.style.color = '#6c757d';
            }

            // إظهار رسالة التعديل
            showAlert('📝 وضع التعديل مفعل - يمكنك الآن تعديل بيانات الوفاة', 'success');
        }

        // وظيفة تحديث المعاينة
        function updatePreview() {
            // الحصول على القيم من النموذج
            const deathPlace = document.getElementById('deathPlace').value || '';
            const deathDate = document.getElementById('deathDate').value || '';
            const personalName = document.getElementById('personalName').value || '';
            const familyName = document.getElementById('familyName').value || '';
            const birthDate = document.getElementById('birthDate').value || '';
            const birthPlace = document.getElementById('birthPlace').value || '';
            const profession = document.getElementById('profession').value || '';
            const residence = document.getElementById('residence').value || '';
            const fatherName = document.getElementById('fatherName').value || '';
            const motherName = document.getElementById('motherName').value || '';

            // تحديث الحقول في المعاينة
            const fieldValues = document.querySelectorAll('.certificate-content .field-value');

            if (fieldValues[0]) fieldValues[0].textContent = deathPlace;
            if (fieldValues[1]) fieldValues[1].textContent = deathDate;
            if (fieldValues[2]) fieldValues[2].textContent = personalName;
            if (fieldValues[3]) fieldValues[3].textContent = familyName;
            if (fieldValues[4]) fieldValues[4].textContent = birthDate;
            if (fieldValues[5]) fieldValues[5].textContent = birthPlace;
            if (fieldValues[6]) fieldValues[6].textContent = profession;
            if (fieldValues[7]) fieldValues[7].textContent = residence;
            if (fieldValues[8]) fieldValues[8].textContent = fatherName;
            if (fieldValues[9]) fieldValues[9].textContent = motherName;

            // إظهار رسالة نجاح
            showAlert('تم تحديث المعاينة بنجاح!', 'success');
        }

        // وظيفة حفظ بيانات الوفاة
        async function saveDeathRecord() {
            try {
                // التحقق من صحة البيانات المطلوبة
                const deathPlace = document.getElementById('deathPlace').value.trim();
                const deathDate = document.getElementById('deathDate').value;
                const personalName = document.getElementById('personalName').value.trim();
                const familyName = document.getElementById('familyName').value.trim();

                if (!deathPlace || !deathDate || !personalName || !familyName) {
                    showAlert('يرجى ملء جميع الحقول المطلوبة (مكان الوفاة، تاريخ الوفاة، الاسم الشخصي، الاسم العائلي)', 'error');
                    return;
                }

                // قراءة الحقول المخفية
                const editMode = document.getElementById('editMode').value === 'true';
                const citizenId = document.getElementById('citizenId').value;
                const originalActNumber = document.getElementById('originalActNumber').value;
                const isDeceasedValue = document.getElementById('isDeceased').value === 'true';

                console.log('🔧 حالة الحفظ:', {
                    editMode: editMode,
                    citizenId: citizenId,
                    originalActNumber: originalActNumber,
                    isDeceased: isDeceasedValue
                });

                // جمع جميع البيانات بالتنسيق المتوافق مع النظام الأصلي
                const deathRecord = {
                    // البيانات الأساسية (متوافقة مع النظام الأصلي)
                    id: editMode ? citizenId : Date.now().toString(), // استخدام المعرف الموجود في وضع التعديل
                    firstNameAr: personalName,
                    familyNameAr: familyName,
                    personalName: personalName, // للتوافق مع النظام القديم
                    familyName: familyName, // للتوافق مع النظام القديم
                    birthDate: document.getElementById('birthDate').value || null,
                    birthPlaceAr: document.getElementById('birthPlace').value.trim() || null,
                    birthPlace: document.getElementById('birthPlace').value.trim() || null, // للتوافق
                    profession: document.getElementById('profession').value.trim() || null,
                    residence: document.getElementById('residence').value.trim() || null,
                    fatherNameAr: document.getElementById('fatherName').value.trim() || null,
                    motherNameAr: document.getElementById('motherName').value.trim() || null,
                    fatherName: document.getElementById('fatherName').value.trim() || null, // للتوافق
                    motherName: document.getElementById('motherName').value.trim() || null, // للتوافق
                    registrationDate: document.getElementById('registrationDate').value || new Date().toISOString().split('T')[0],

                    // البيانات المخفية للتوافق
                    firstNameFr: document.getElementById('personalNameFr').value.trim() || null,
                    familyNameFr: document.getElementById('familyNameFr').value.trim() || null,
                    hijriDate: document.getElementById('hijriDate').value.trim() || null,
                    birthPlaceFr: document.getElementById('birthPlaceFr').value.trim() || null,
                    gender: document.getElementById('gender').value || null,
                    maritalStatus: document.getElementById('maritalStatus').value || null,
                    nationality: document.getElementById('nationality').value.trim() || 'مغربية',
                    age: document.getElementById('age').value || null,

                    // الحفاظ على معرف العقد الأصلي في وضع التعديل
                    actNumber: editMode ? (document.getElementById('actNumber').value || originalActNumber || generateActNumber()) : generateActNumber(),

                    // بيانات الوفاة - الحفاظ على حالة الوفاة
                    isDeceased: true, // دائماً true لشهادات الوفاة
                    deathInfo: {
                        deathPlace: deathPlace,
                        deathDate: deathDate,
                        deathTime: document.getElementById('deathTime').value || null,
                        deathCause: document.getElementById('deathCause').value.trim() || null,
                        registrationDate: document.getElementById('registrationDate').value || new Date().toISOString().split('T')[0],
                        registrationTime: new Date().toLocaleTimeString('ar-MA'),
                        certificationOfficer: document.getElementById('certificationOfficer').value.trim() || 'ضابط الحالة المدنية - أيير'
                    },

                    // إضافة صورة الشهادة إذا كانت موجودة
                    deathCertificateImage: currentCertificateImage ? {
                        data: currentCertificateImage.data,
                        name: currentCertificateImage.name,
                        type: currentCertificateImage.type,
                        size: currentCertificateImage.size
                    } : null,

                    // للتوافق مع النظام القديم
                    certificateImage: currentCertificateImage ? {
                        data: currentCertificateImage.data,
                        name: currentCertificateImage.name,
                        type: currentCertificateImage.type,
                        size: currentCertificateImage.size
                    } : null,

                    // معلومات إضافية
                    recordType: 'death',
                    createdAt: editMode ? undefined : new Date().toISOString(), // لا نغير تاريخ الإنشاء في التعديل
                    lastModified: new Date().toISOString()
                };

                console.log('💾 سجل الوفاة المُعد للحفظ:', {
                    id: deathRecord.id,
                    actNumber: deathRecord.actNumber,
                    isDeceased: deathRecord.isDeceased,
                    editMode: editMode,
                    hasDeathCertificateImage: !!deathRecord.deathCertificateImage,
                    hasCertificateImage: !!deathRecord.certificateImage,
                    imageSize: currentCertificateImage ? (currentCertificateImage.size / 1024 / 1024).toFixed(2) + ' MB' : 'لا توجد'
                });

                // حفظ أو تحديث في قاعدة البيانات باستخدام النظام المشترك
                if (editMode) {
                    await citizensDB.updateCitizen(deathRecord);
                    showAlert('✅ تم تحديث بيانات الوفاة بنجاح!', 'success');
                    console.log('✅ تم تحديث السجل بنجاح في قاعدة البيانات');
                } else {
                    await citizensDB.addCitizen(deathRecord);
                    showAlert('✅ تم حفظ بيانات الوفاة بنجاح!', 'success');
                    console.log('✅ تم حفظ السجل بنجاح في قاعدة البيانات');
                }

            } catch (error) {
                console.error('❌ خطأ في حفظ البيانات:', error);
                showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'error');
            }
        }

        // وظيفة توليد رقم العقد
        function generateActNumber() {
            const currentYear = new Date().getFullYear();
            const randomNumber = Math.floor(Math.random() * 1000) + 1;
            return `${randomNumber}/${currentYear}`;
        }

        // تم استبدال وظائف قاعدة البيانات القديمة بالنظام المشترك CitizensDB

        // وظيفة مسح النموذج
        function clearForm() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المدخلة؟')) {
                document.getElementById('deathForm').reset();

                // إعادة تعيين التاريخ الافتراضي
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('deathDate').value = today;
                document.getElementById('registrationDate').value = today;
                document.getElementById('nationality').value = 'مغربية';

                // مسح الحقول المخفية
                document.getElementById('editMode').value = 'false';
                document.getElementById('citizenId').value = '';
                document.getElementById('originalActNumber').value = '';
                document.getElementById('actNumber').value = '';
                document.getElementById('certificationOfficer').value = 'ضابط الحالة المدنية - أيير';

                // مسح المعاينة
                const fieldValues = document.querySelectorAll('.certificate-content .field-value');
                fieldValues.forEach(field => {
                    field.textContent = '';
                });

                showAlert('تم مسح النموذج بنجاح', 'success');
            }
        }

        // وظيفة فتح النسخة الموجزة من رسم الوفاة
        function openDeathCertificate() {
            // التحقق من وجود بيانات أساسية
            const personalName = document.getElementById('personalName').value.trim();
            const familyName = document.getElementById('familyName').value.trim();
            const deathDate = document.getElementById('deathDate').value;
            const deathPlace = document.getElementById('deathPlace').value.trim();

            if (!personalName || !familyName) {
                showAlert('❌ يرجى إدخال الاسم الشخصي والعائلي على الأقل', 'error');
                return;
            }

            try {
                // جمع البيانات من النموذج
                const formData = {
                    personalName: personalName,
                    familyName: familyName,
                    birthDate: document.getElementById('birthDate').value,
                    birthPlace: document.getElementById('birthPlace').value.trim(),
                    gender: document.getElementById('gender').value,
                    fatherName: document.getElementById('fatherName').value.trim(),
                    motherName: document.getElementById('motherName').value.trim(),
                    profession: document.getElementById('profession').value.trim(),
                    maritalStatus: document.getElementById('maritalStatus').value,
                    deathDate: deathDate,
                    deathPlace: deathPlace,
                    deathTime: document.getElementById('deathTime').value,
                    deathCause: document.getElementById('deathCause').value.trim(),
                    age: document.getElementById('age').value
                };

                // إنشاء URL مع البيانات
                const params = new URLSearchParams();
                Object.keys(formData).forEach(key => {
                    if (formData[key]) {
                        params.append(key, formData[key]);
                    }
                });

                const url = `death-certificate.html?${params.toString()}`;
                console.log('📋 فتح النسخة الموجزة من رسم الوفاة:', url);

                // فتح النموذج في نافذة جديدة
                window.open(url, '_blank');

                showAlert('✅ تم فتح النسخة الموجزة من رسم الوفاة في نافذة جديدة', 'success');

            } catch (error) {
                console.error('❌ خطأ في فتح النسخة الموجزة من رسم الوفاة:', error);
                showAlert('حدث خطأ أثناء فتح النسخة الموجزة من رسم الوفاة', 'error');
            }
        }

        // تحديث حالة الأزرار
        function updateButtonStates() {
            const hasImage = currentCertificateImage !== null;

            // تحديث أزرار التحكم
            const extractBtn = document.getElementById('extractBtn');
            const printBtn = document.getElementById('printBtn');
            const deathCertBtn = document.getElementById('deathCertBtn');

            if (extractBtn) {
                extractBtn.disabled = !hasImage;
                extractBtn.style.opacity = hasImage ? '1' : '0.5';
            }

            if (printBtn) {
                printBtn.disabled = !hasImage;
                printBtn.style.opacity = hasImage ? '1' : '0.5';
            }

            if (deathCertBtn) {
                deathCertBtn.disabled = false; // هذا الزر يعمل دائماً
            }

            console.log('🔧 تم تحديث حالة الأزرار:', {
                hasImage: hasImage,
                extractEnabled: hasImage,
                printEnabled: hasImage
            });
        }

        // وظيفة إظهار الرسائل
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // متغيرات الصورة
        let currentCertificateImage = null;
        let currentZoom = 1;
        let currentRotation = 0;
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;
        let imageNaturalWidth = 0;
        let imageNaturalHeight = 0;
        let originalDisplayWidth = 0;
        let originalDisplayHeight = 0;

        // وظيفة تحميل صورة الشهادة
        function handleCertificateUpload(event) {
            console.log('📤 بدء تحميل الصورة...');

            const file = event.target.files[0];
            if (!file) {
                console.log('❌ لم يتم اختيار ملف');
                return;
            }

            console.log('📁 تفاصيل الملف:', {
                name: file.name,
                type: file.type,
                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
            });

            // التحقق من نوع الملف
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            if (!allowedTypes.includes(file.type)) {
                console.log('❌ نوع الملف غير مدعوم:', file.type);
                showAlert('❌ نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG) أو ملف PDF', 'error');
                return;
            }

            // التحقق من حجم الملف (10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                console.log('❌ حجم الملف كبير:', (file.size / 1024 / 1024).toFixed(2) + ' MB');
                showAlert('❌ حجم الملف كبير جداً. الحد الأقصى 10MB', 'error');
                return;
            }

            console.log('✅ الملف صالح، بدء القراءة...');

            // قراءة الملف
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('✅ تم قراءة الملف بنجاح');

                currentCertificateImage = {
                    data: e.target.result,
                    name: file.name,
                    size: file.size,
                    type: file.type
                };

                console.log('💾 تم حفظ بيانات الصورة:', {
                    name: currentCertificateImage.name,
                    type: currentCertificateImage.type,
                    dataLength: currentCertificateImage.data.length
                });

                displayCertificateImage();
                updateButtonStates();
                showAlert('✅ تم تحميل صورة الشهادة بنجاح', 'success');
            };

            reader.onerror = function(error) {
                console.error('❌ خطأ في قراءة الملف:', error);
                showAlert('❌ خطأ في قراءة الملف', 'error');
            };

            reader.readAsDataURL(file);
        }

        // وظيفة عرض صورة الشهادة
        function displayCertificateImage() {
            console.log('🖼️ بدء عرض الصورة...');

            const displayArea = document.getElementById('certificateImageDisplay');

            if (!displayArea) {
                console.error('❌ لم يتم العثور على منطقة العرض');
                return;
            }

            if (!currentCertificateImage) {
                console.log('📄 عرض الحالة الافتراضية (لا توجد صورة)');
                displayArea.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #6c757d; border: 2px dashed #dee2e6; border-radius: 8px; background: #f8f9fa;">
                        <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                        <h4 style="color: #495057; margin-bottom: 10px;">لم يتم تحميل شهادة وفاة بعد</h4>
                        <p style="margin-bottom: 0;">اضغط على "اختيار ملف" لتحميل صورة شهادة الوفاة</p>
                    </div>
                `;
                return;
            }

            console.log('🔍 تحليل نوع الملف:', currentCertificateImage.type);

            const isImage = currentCertificateImage.type.startsWith('image/');
            const isPDF = currentCertificateImage.type === 'application/pdf';

            if (isImage) {
                console.log('🖼️ عرض صورة...');
                displayArea.innerHTML = `
                    <div class="image-container">
                        <!-- Enhanced Image Controls -->
                        <div id="imageControls" style="margin-top: 10px; text-align: center;">
                            <!-- أزرار التكبير والتصغير -->
                            <div class="control-group" style="margin-bottom: 10px;">
                                <button class="btn btn-secondary" onclick="zoomIn(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍+ تكبير</button>
                                <button class="btn btn-secondary" onclick="zoomOut(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍- تصغير</button>
                                <button class="btn btn-secondary" onclick="resetZoom(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔄 إعادة تعيين</button>
                            </div>

                            <!-- أزرار التحكم في الاتجاهات -->
                            <div class="direction-controls" id="directionControls" style="display: none; margin: 10px 0;">
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 5px; max-width: 150px; margin: 0 auto;">
                                    <div></div>
                                    <button class="btn btn-info" onclick="moveImage(0, -20)" title="أعلى" style="font-size: 16px; padding: 8px;">⬆️</button>
                                    <div></div>
                                    <button class="btn btn-info" onclick="moveImage(-20, 0)" title="يسار" style="font-size: 16px; padding: 8px;">⬅️</button>
                                    <button class="btn btn-warning" onclick="resetImagePosition()" title="المنتصف" style="font-size: 16px; padding: 8px;">🎯</button>
                                    <button class="btn btn-info" onclick="moveImage(20, 0)" title="يمين" style="font-size: 16px; padding: 8px;">➡️</button>
                                    <div></div>
                                    <button class="btn btn-info" onclick="moveImage(0, 20)" title="أسفل" style="font-size: 16px; padding: 8px;">⬇️</button>
                                    <div></div>
                                </div>
                            </div>

                            <!-- أزرار الدوران والوظائف الأخرى -->
                            <div class="control-group" style="margin-bottom: 10px;">
                                <button class="btn btn-secondary" onclick="rotateLeft(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">↺ يسار</button>
                                <button class="btn btn-secondary" onclick="rotateRight(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">↻ يمين</button>
                                <button class="btn btn-secondary" onclick="resetRotation(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔄 إعادة دوران</button>
                                <button class="btn btn-secondary" onclick="fitToScreen(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">📐 ملء الشاشة</button>
                                <button class="btn btn-secondary" onclick="openImageInNewTab()" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔗 نافذة جديدة</button>
                            </div>

                            <div style="margin-bottom: 8px; color: #6c757d; font-size: 11px;">
                                💡 اضغط على الصورة أو عجلة الفأرة للتكبير • استخدم أزرار الاتجاهات للتحريك<br>
                                ⌨️ الكيبورد: <strong>+/-</strong> تكبير • <strong>P/O</strong> دوران • <strong>I</strong> إعادة تعيين • <strong>U</strong> ملء الشاشة
                            </div>
                        </div>
                        <div id="imageContainer" style="margin: 10px auto; position: relative; overflow: hidden; border-radius: 6px; border: 1px solid #ddd; max-width: 100%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; cursor: zoom-in;">
                            <img src="${currentCertificateImage.data}"
                                 alt="شهادة الوفاة"
                                 class="certificate-image"
                                 id="certificateImg"
                                 style="user-select: none; -webkit-user-drag: none; transform-origin: center; transform: scale(${currentZoom});"
                                 onload="console.log('✅ تم تحميل الصورة في DOM'); initializeImageControls(this, document.getElementById('imageContainer'));"
                                 onerror="console.error('❌ خطأ في تحميل الصورة في DOM')">
                        </div>
                        <div class="image-info">
                            <strong>📁 ${currentCertificateImage.name}</strong> |
                            📏 ${(currentCertificateImage.size / 1024 / 1024).toFixed(2)} MB |
                            🔍 ${Math.round(currentZoom * 100)}%
                        </div>
                    </div>
                `;
                console.log('✅ تم إنشاء HTML للصورة');
            } else if (isPDF) {
                console.log('📄 عرض PDF...');
                displayArea.innerHTML = `
                    <div class="image-container">
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 48px; margin-bottom: 15px; color: #dc3545;">📄</div>
                            <h4 style="color: #495057; margin-bottom: 10px;">ملف PDF</h4>
                            <p style="margin-bottom: 15px; color: #6c757d;">تم تحميل ملف PDF بنجاح</p>
                            <button class="btn btn-primary" onclick="openPDFInNewTab()">📖 فتح PDF</button>
                        </div>
                        <div class="image-info">
                            <strong>📁 ${currentCertificateImage.name}</strong> |
                            📏 ${(currentCertificateImage.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                    </div>
                `;
                console.log('✅ تم إنشاء HTML للـ PDF');
            } else {
                console.error('❌ نوع ملف غير معروف:', currentCertificateImage.type);
            }
        }

        // وظيفة مسح صورة الشهادة
        function clearCertificateImage() {
            if (currentCertificateImage) {
                if (confirm('هل أنت متأكد من حذف صورة الشهادة؟')) {
                    currentCertificateImage = null;
                    currentZoom = 1;
                    document.getElementById('certificateImageInput').value = '';
                    displayCertificateImage();
                    updateButtonStates();
                    showAlert('تم حذف صورة الشهادة', 'success');
                }
            } else {
                showAlert('لا توجد صورة لحذفها', 'error');
            }
        }

        // وظائف التكبير والتصغير
        function zoomImage(factor) {
            currentZoom *= factor;
            currentZoom = Math.max(0.5, Math.min(currentZoom, 3)); // حد أدنى 50% وحد أقصى 300%

            const img = document.getElementById('certificateImg');
            if (img) {
                img.style.transform = `scale(${currentZoom})`;
                updateImageInfo();
            }
        }

        function resetZoom() {
            currentZoom = 1;
            const img = document.getElementById('certificateImg');
            if (img) {
                img.style.transform = `scale(${currentZoom})`;
                updateImageInfo();
            }
        }

        function toggleZoom() {
            if (currentZoom === 1) {
                zoomImage(1.5);
            } else {
                resetZoom();
            }
        }

        function updateImageInfo() {
            const infoDiv = document.querySelector('.image-info');
            if (infoDiv && currentCertificateImage) {
                infoDiv.innerHTML = `
                    <strong>📁 ${currentCertificateImage.name}</strong> |
                    📏 ${(currentCertificateImage.size / 1024 / 1024).toFixed(2)} MB |
                    🔍 ${Math.round(currentZoom * 100)}%
                `;
            }
        }

        function openImageInNewTab() {
            if (currentCertificateImage) {
                const newWindow = window.open();
                newWindow.document.write(`
                    <html>
                        <head><title>شهادة الوفاة - ${currentCertificateImage.name}</title></head>
                        <body style="margin: 0; padding: 20px; background: #f5f5f5; text-align: center;">
                            <img src="${currentCertificateImage.data}" style="max-width: 100%; height: auto; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                        </body>
                    </html>
                `);
            }
        }

        function openPDFInNewTab() {
            if (currentCertificateImage && currentCertificateImage.type === 'application/pdf') {
                const newWindow = window.open();
                newWindow.location.href = currentCertificateImage.data;
            }
        }

        // تحديث حالة الأزرار
        function updateButtonStates() {
            const extractBtn = document.getElementById('extractBtn');
            const printBtn = document.getElementById('printBtn');
            const deathCertBtn = document.getElementById('deathCertBtn');

            // أزرار الصورة
            if (currentCertificateImage) {
                extractBtn.disabled = false;
                printBtn.disabled = false;
                extractBtn.style.opacity = '1';
                printBtn.style.opacity = '1';
            } else {
                extractBtn.disabled = true;
                printBtn.disabled = true;
                extractBtn.style.opacity = '0.6';
                printBtn.style.opacity = '0.6';
            }

            // زر النسخة الموجزة من رسم الوفاة - يتم تفعيله عند وجود بيانات أساسية
            const personalName = document.getElementById('personalName').value.trim();
            const familyName = document.getElementById('familyName').value.trim();

            if (personalName && familyName) {
                deathCertBtn.disabled = false;
                deathCertBtn.style.opacity = '1';
            } else {
                deathCertBtn.disabled = true;
                deathCertBtn.style.opacity = '0.6';
            }
        }

        // وظيفة نقل المعلومات من الصورة (محاكاة)
        function extractDataFromImage() {
            if (!currentCertificateImage) {
                showAlert('❌ يرجى تحميل صورة الشهادة أولاً', 'error');
                return;
            }

            // محاكاة عملية استخراج البيانات
            showAlert('🔄 جاري استخراج المعلومات من الصورة...', 'success');

            // محاكاة تأخير المعالجة
            setTimeout(() => {
                // بيانات تجريبية للمحاكاة
                const extractedData = {
                    personalName: 'محمد',
                    familyName: 'الأحمدي',
                    birthDate: '1950-05-15',
                    birthPlace: 'أيير',
                    deathPlace: 'أيير',
                    deathDate: '2024-01-15',
                    deathTime: '14:30',
                    deathCause: 'أسباب طبيعية',
                    gender: 'ذكر',
                    profession: 'فلاح',
                    residence: 'أيير',
                    fatherName: 'أحمد الأحمدي',
                    motherName: 'فاطمة بنت علي',
                    age: '74'
                };

                // ملء النموذج بالبيانات المستخرجة
                fillFormWithExtractedData(extractedData);

                showAlert('✅ تم استخراج المعلومات ونقلها إلى النموذج بنجاح!', 'success');
            }, 2000);
        }

        // ملء النموذج بالبيانات المستخرجة
        function fillFormWithExtractedData(data) {
            // ملء البيانات الشخصية
            if (data.personalName) document.getElementById('personalName').value = data.personalName;
            if (data.familyName) document.getElementById('familyName').value = data.familyName;
            if (data.birthDate) document.getElementById('birthDate').value = data.birthDate;
            if (data.birthPlace) document.getElementById('birthPlace').value = data.birthPlace;
            if (data.gender) document.getElementById('gender').value = data.gender;
            if (data.profession) document.getElementById('profession').value = data.profession;
            if (data.residence) document.getElementById('residence').value = data.residence;
            if (data.fatherName) document.getElementById('fatherName').value = data.fatherName;
            if (data.motherName) document.getElementById('motherName').value = data.motherName;
            if (data.age) document.getElementById('age').value = data.age;

            // ملء بيانات الوفاة
            if (data.deathPlace) document.getElementById('deathPlace').value = data.deathPlace;
            if (data.deathDate) document.getElementById('deathDate').value = data.deathDate;
            if (data.deathTime) document.getElementById('deathTime').value = data.deathTime;
            if (data.deathCause) document.getElementById('deathCause').value = data.deathCause;

            // تأثير بصري لإظهار الحقول المملوءة
            const filledFields = document.querySelectorAll('input[type="text"], input[type="date"], input[type="time"], select');
            filledFields.forEach(field => {
                if (field.value) {
                    field.style.background = '#e8f5e8';
                    field.style.borderColor = '#28a745';

                    // إزالة التأثير بعد 3 ثوان
                    setTimeout(() => {
                        field.style.background = '';
                        field.style.borderColor = '';
                    }, 3000);
                }
            });
        }

        // وظيفة طباعة الشهادة
        function printCertificate() {
            if (!currentCertificateImage) {
                showAlert('❌ يرجى تحميل صورة الشهادة أولاً', 'error');
                return;
            }

            // فتح نافذة طباعة للصورة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>طباعة شهادة الوفاة</title>
                    <style>
                        @media print {
                            @page {
                                size: A4;
                                margin: 15mm;
                            }
                            body {
                                margin: 0;
                                padding: 0;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                            }
                            img {
                                max-width: 100%;
                                max-height: 100%;
                                object-fit: contain;
                            }
                        }
                        body {
                            margin: 0;
                            padding: 20px;
                            background: white;
                            text-align: center;
                        }
                        img {
                            max-width: 100%;
                            height: auto;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        }
                    </style>
                </head>
                <body>
                    <img src="${currentCertificateImage.data}" alt="شهادة الوفاة">
                    <script>
                        window.onload = function() {
                            window.print();
                            window.onafterprint = function() {
                                window.close();
                            }
                        }
                    <\/script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }

        // ===== ENHANCED IMAGE CONTROL FUNCTIONS =====

        function initializeImageControls(image, container) {
            // Remove existing event listeners
            const newImage = image.cloneNode(true);
            image.parentNode.replaceChild(newImage, image);

            // Add enhanced click to zoom
            newImage.addEventListener('click', function(e) {
                e.preventDefault();

                if (currentZoom === 1) {
                    console.log('🖱️ نقر للتكبير');
                    zoomIn();
                } else {
                    console.log('🖱️ نقر للتصغير');
                    resetZoom();
                }
            });

            // Enhanced wheel zoom with smooth scaling
            newImage.addEventListener('wheel', function(e) {
                e.preventDefault();

                if (e.deltaY < 0) {
                    zoomIn();
                } else {
                    zoomOut();
                }
            });

            // Add keyboard controls
            document.addEventListener('keydown', handleKeyboardControls);
        }

        function zoomIn(event) {
            if (event) event.preventDefault();

            // التكبير باستخدام transform scale فقط
            const previousZoom = currentZoom;
            currentZoom = Math.min(currentZoom * 1.2, 5);

            // إذا وصلنا للحد الأقصى، أعلم المستخدم
            if (currentZoom === 5 && previousZoom < 5) {
                console.log('⚠️ وصلت للحد الأقصى للتكبير (500%)');
            }

            updateImageTransform();
            console.log('🔍 تكبير إلى:', Math.round(currentZoom * 100) + '% (محصور داخل الحدود)');
        }

        function zoomOut(event) {
            if (event) event.preventDefault();

            const previousZoom = currentZoom;
            currentZoom = Math.max(currentZoom / 1.2, 0.2);

            // إذا وصلنا للحد الأدنى، أعلم المستخدم
            if (currentZoom === 0.2 && previousZoom > 0.2) {
                console.log('⚠️ وصلت للحد الأدنى للتصغير (20%)');
            }

            updateImageTransform();
            console.log('🔍 تصغير إلى:', Math.round(currentZoom * 100) + '%');
        }

        function resetZoom(event) {
            if (event) event.preventDefault();
            currentZoom = 1;
            const image = document.getElementById('certificateImg');
            const container = document.getElementById('imageContainer');

            if (image && container) {
                image.style.transform = `scale(1) rotate(${currentRotation}deg)`;
                image.style.transformOrigin = 'center center';

                // إزالة أي أبعاد ثابتة وترك CSS يتحكم
                console.log('🔄 إعادة تعيين التكبير باستخدام CSS');

                image.style.width = '';
                image.style.height = '';
                image.style.maxWidth = '';
                image.style.maxHeight = '';

                container.style.width = '';
                container.style.height = '';

                console.log('🔄 تم إعادة تعيين التكبير للمنتصف');
            }

            updateImageTransform();
        }

        function rotateLeft(event) {
            if (event) event.preventDefault();
            currentRotation -= 90;
            if (currentRotation < 0) currentRotation = 270;
            updateImageTransform();
            console.log('↺ دوران يسار إلى:', currentRotation + '°');
        }

        function rotateRight(event) {
            if (event) event.preventDefault();
            currentRotation += 90;
            if (currentRotation >= 360) currentRotation = 0;
            updateImageTransform();
            console.log('↻ دوران يمين إلى:', currentRotation + '°');
        }

        function resetRotation(event) {
            if (event) event.preventDefault();
            currentRotation = 0;
            updateImageTransform();
            console.log('🔄 إعادة تعيين الدوران');
        }

        function updateImageTransform() {
            const image = document.getElementById('certificateImg');
            const container = document.getElementById('imageContainer');

            if (image) {
                // تطبيق التحويل مع نقطة أصل ثابتة في المنتصف
                image.style.transform = `scale(${currentZoom}) rotate(${currentRotation}deg)`;
                image.style.transformOrigin = 'center center';

                // إضافة أو إزالة فئة التكبير
                if (currentZoom > 1) {
                    image.classList.add('zoomed');
                    // عند التكبير، نبقي الصورة داخل حدود الحاوية
                    if (container) {
                        container.style.overflow = 'hidden'; // منع التمرير
                        container.style.cursor = 'zoom-out';

                        // التأكد من أن الصورة تبقى داخل الحدود
                        image.style.width = '100%';
                        image.style.height = '100%';

                        // إظهار أزرار التحكم في الاتجاهات
                        const directionControls = document.getElementById('directionControls');
                        if (directionControls) {
                            directionControls.style.display = 'block';
                        }

                        console.log('🔒 الصورة محصورة داخل الحدود - التكبير:', Math.round(currentZoom * 100) + '%');
                    }
                } else {
                    image.classList.remove('zoomed');
                    // عند الحجم الطبيعي، نخفي التمرير
                    if (container) {
                        container.style.overflow = 'hidden';
                        container.style.cursor = 'zoom-in';
                    }

                    // إخفاء أزرار التحكم في الاتجاهات
                    const directionControls = document.getElementById('directionControls');
                    if (directionControls) {
                        directionControls.style.display = 'none';
                    }

                    // التأكد من أن CSS يتحكم في الأبعاد
                    if (currentZoom === 1) {
                        image.style.width = '';
                        image.style.height = '';
                        image.style.maxWidth = '';
                        image.style.maxHeight = '';
                    }
                }

                console.log('🔄 تحديث التحويل: تكبير=' + Math.round(currentZoom * 100) + '% دوران=' + currentRotation + '°');
            }
        }

        function handleKeyboardControls(e) {
            // تجاهل إذا كان المستخدم يكتب في حقل نص
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                return;
            }

            const key = e.key;

            switch(key) {
                case '+':
                case '=':
                    zoomIn();
                    console.log('🔍 تكبير بالكيبورد');
                    break;
                case '-':
                    zoomOut();
                    console.log('🔍 تصغير بالكيبورد');
                    break;
                case '0':
                    resetZoom();
                    console.log('🔄 إعادة تعيين التكبير بالكيبورد');
                    break;
                case 'p': // بدلاً من Q - دوران يسار
                    rotateLeft();
                    console.log('↺ دوران يسار بالكيبورد');
                    break;
                case 'o': // بدلاً من E - دوران يمين
                    rotateRight();
                    console.log('↻ دوران يمين بالكيبورد');
                    break;
                case 'i': // بدلاً من R - إعادة تعيين
                    resetZoom();
                    resetRotation();
                    console.log('🔄 إعادة تعيين شاملة بالكيبورد');
                    break;
                case 'u': // بدلاً من F - ملء الشاشة
                    fitToScreen();
                    console.log('📐 ملء الشاشة بالكيبورد');
                    break;
                case 'y': // بدلاً من D - تحميل
                    downloadImage();
                    console.log('💾 تحميل بالكيبورد');
                    break;
            }
        }

        function moveImage(deltaX, deltaY) {
            const image = document.getElementById('certificateImg');
            const container = document.getElementById('imageContainer');

            if (image && container && currentZoom > 1) {
                // حساب الموقع الحالي للصورة
                const currentTransform = image.style.transform || '';
                const scaleMatch = currentTransform.match(/scale\(([^)]+)\)/);
                const rotateMatch = currentTransform.match(/rotate\(([^)]+)\)/);
                const translateMatch = currentTransform.match(/translate\(([^,]+),\s*([^)]+)\)/);

                let currentX = 0, currentY = 0;
                if (translateMatch) {
                    currentX = parseFloat(translateMatch[1]) || 0;
                    currentY = parseFloat(translateMatch[2]) || 0;
                }

                // حساب الموقع الجديد
                const newX = currentX + deltaX;
                const newY = currentY + deltaY;

                // حساب الحدود المسموحة للحركة
                const imageRect = image.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();

                // حساب المساحة الإضافية بسبب التكبير
                const scaledWidth = imageRect.width;
                const scaledHeight = imageRect.height;
                const maxMoveX = Math.max(0, (scaledWidth - containerRect.width) / 2);
                const maxMoveY = Math.max(0, (scaledHeight - containerRect.height) / 2);

                // تقييد الحركة داخل الحدود
                const constrainedX = Math.max(-maxMoveX, Math.min(maxMoveX, newX));
                const constrainedY = Math.max(-maxMoveY, Math.min(maxMoveY, newY));

                // تطبيق التحويل الجديد
                const scaleValue = scaleMatch ? scaleMatch[1] : currentZoom;
                const rotateValue = rotateMatch ? rotateMatch[1] : currentRotation + 'deg';

                image.style.transform = `scale(${scaleValue}) rotate(${rotateValue}) translate(${constrainedX}px, ${constrainedY}px)`;

                console.log('🎮 تحريك الصورة:', {
                    deltaX: deltaX,
                    deltaY: deltaY,
                    newX: constrainedX,
                    newY: constrainedY,
                    maxMoveX: maxMoveX,
                    maxMoveY: maxMoveY,
                    zoom: currentZoom
                });
            } else if (currentZoom <= 1) {
                console.log('⚠️ التحريك متاح فقط عند التكبير');
            }
        }

        function resetImagePosition() {
            const image = document.getElementById('certificateImg');
            if (image && currentZoom > 1) {
                // إعادة تعيين موقع الصورة للمنتصف مع الحفاظ على التكبير والدوران
                const scaleValue = currentZoom;
                const rotateValue = currentRotation;

                image.style.transform = `scale(${scaleValue}) rotate(${rotateValue}deg)`;
                image.style.transformOrigin = 'center center';

                console.log('🎯 تم إعادة تعيين موقع الصورة للمنتصف');
                console.log('🔍 التكبير الحالي:', Math.round(currentZoom * 100) + '%');
            } else {
                console.log('⚠️ إعادة تعيين الموقع متاحة فقط عند التكبير');
            }
        }

        function fitToScreen(event) {
            if (event) event.preventDefault();

            const image = document.getElementById('certificateImg');
            const container = document.getElementById('imageContainer');

            if (image && container) {
                // إعادة تعيين التحويلات
                image.style.width = '';
                image.style.height = '';
                image.style.maxWidth = '';
                image.style.maxHeight = '';

                container.style.width = '';
                container.style.height = '';

                // إعادة تعيين التكبير
                currentZoom = 1;
                image.style.transformOrigin = 'center center';
                updateImageTransform();

                console.log('📐 تم ملء الشاشة وإعادة تعيين الصورة');
            }
        }

        function downloadImage(event) {
            if (event) event.preventDefault();

            if (currentCertificateImage) {
                try {
                    const link = document.createElement('a');
                    link.href = currentCertificateImage.data;
                    link.download = currentCertificateImage.name || 'شهادة_الوفاة.jpg';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    console.log('💾 تم تحميل الصورة:', currentCertificateImage.name);
                } catch (error) {
                    console.error('❌ خطأ في تحميل الصورة:', error);
                    alert('❌ حدث خطأ أثناء تحميل الصورة');
                }
            } else {
                console.log('⚠️ لا توجد صورة للتحميل');
            }
        }

        // تحديث الدوال القديمة لتتوافق مع النظام الجديد
        function zoomImage(factor) {
            if (factor > 1) {
                zoomIn();
            } else {
                zoomOut();
            }
        }

        function toggleZoom() {
            if (currentZoom === 1) {
                zoomIn();
            } else {
                resetZoom();
            }
        }
    </script>
</body>
</html>
