<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قاعدة بيانات المواطنين - IndexedDB - مكتب الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            direction: rtl;
            display: flex;
            flex-direction: column;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 20px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 50%;
            font-size: 2.5em;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
        }

        .header-text {
            text-align: right;
        }

        .header-text h1 {
            font-size: 2.2em;
            margin: 0 0 5px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        /* ===== FOOTER ===== */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a, #006233);
        }

        .footer p {
            margin: 0;
            font-weight: 500;
            opacity: 0.9;
        }



        .content {
            flex: 1;
            background: white;
            padding: 30px;
            overflow-y: auto;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .form-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        /* تخصيص حجم منطقة تحميل الشهادة */
        .upload-area {
            width: 70% !important;
            height: calc(100% - 60px) !important;
            min-height: 400px !important;
            max-height: 600px !important;
            margin: 0 auto !important;
            position: relative !important;
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 10px rgba(196, 30, 58, 0.3);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 3px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #c41e3a, #8b0000);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            padding: 15px 20px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 3px solid #c41e3a;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 1.6em;
            font-weight: bold;
            color: #c41e3a;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(196, 30, 58, 0.2);
        }

        .stat-label {
            color: #6c757d;
            font-weight: 600;
            font-size: 12px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        /* Virtual Keyboard Styles */
        .key-btn {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 2px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 35px;
            text-align: center;
        }

        .key-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }

        .key-btn:active {
            background: #dee2e6;
            transform: translateY(0);
        }

        .space-btn {
            min-width: 120px;
            background: #f8f9fa;
        }

        .delete-btn {
            background: #ffeaa7;
            color: #856404;
        }

        .delete-btn:hover {
            background: #fdcb6e;
        }

        .clear-btn {
            background: #fab1a0;
            color: #d63031;
        }

        .clear-btn:hover {
            background: #e17055;
            color: white;
        }

        /* Draggable keyboard styles */
        #virtualKeyboard.dragging {
            opacity: 0.8;
            transform: rotate(2deg);
            transition: none;
        }

        #keyboardHeader:hover {
            background: #dee2e6 !important;
        }

        #keyboardHeader:active {
            background: #ced4da !important;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .content {
                padding: 20px;
            }

            /* تكييف منطقة التحميل للشاشات المتوسطة */
            .upload-area {
                width: 85% !important;
                min-height: 350px !important;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .content {
                padding: 15px;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .key-btn {
                padding: 6px 8px;
                font-size: 12px;
                min-width: 30px;
            }

            /* تكييف منطقة التحميل للهواتف */
            .upload-area {
                width: 95% !important;
                min-height: 300px !important;
                max-height: 450px !important;
            }
        }

        /* Enhanced Image Display Styles */
        #imageContainer {
            position: relative;
            box-sizing: border-box;
            width: 100% !important;
            height: 85% !important;
            max-width: 100% !important;
            max-height: 100% !important;
            overflow: hidden;
            transition: all 0.3s ease;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border-radius: 8px;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            /* تحسين التمرير */
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        #imageContainer:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        #uploadedImage {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain !important;
            border-radius: 6px;
            transition: transform 0.3s ease;
            background: #f8f9fa;
            display: block;
            max-width: none !important;
            max-height: none !important;
        }

        /* When zoomed, keep image within container bounds */
        #uploadedImage.zoomed {
            width: 100% !important;
            height: 100% !important;
            max-width: 100% !important;
            max-height: 100% !important;
            object-fit: contain !important;
            transform-origin: center center !important;
        }

        /* تحسين عرض الصورة في منطقة التحميل */
        .upload-area.has-image {
            border-color: #28a745 !important;
            background: #f8fff8 !important;
        }

        .upload-area.has-image:hover {
            border-color: #20c997 !important;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2) !important;
        }

        /* Responsive image sizing */
        @media (max-width: 768px) {
            #imageContainer {
                width: 100%;
                height: 80%;
            }
        }

        @media (max-width: 480px) {
            #imageContainer {
                width: 100%;
                height: 75%;
            }
        }

        #imageInfo {
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
            color: #212529;
        }

        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

        /* Keyboard Direction Styles */
        #arabicKeys {
            direction: ltr;
            text-align: left;
        }

        #frenchKeys {
            direction: rtl;
            text-align: right;
        }

        /* Ensure buttons maintain proper spacing in both directions */
        #arabicKeys .key-btn,
        #frenchKeys .key-btn {
            margin: 2px;
            display: inline-block;
        }

        /* Control buttons should be centered regardless of direction */
        #arabicKeys > div:last-child,
        #frenchKeys > div:last-child {
            text-align: center !important;
            direction: ltr !important;
        }

        /* تحسينات للتاريخ الهجري المحسن */
        .date-conversion-animation {
            animation: highlightConversion 0.8s ease-in-out;
        }

        @keyframes highlightConversion {
            0% { background-color: #fff3cd; transform: scale(1); }
            50% { background-color: #ffeaa7; transform: scale(1.02); }
            100% { background-color: #f8f9fa; transform: scale(1); }
        }

        /* تحسينات بصرية لتحويل التاريخ الدقيق */
        .date-conversion-indicator {
            position: relative;
        }

        .date-conversion-indicator::after {
            content: "🔄";
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .date-conversion-indicator:hover::after {
            opacity: 1;
            transform: translateY(-50%) scale(1.2);
        }

        /* تأثير التحويل الناجح */
        .conversion-success {
            background-color: #e8f5e8 !important;
            border-color: #28a745 !important;
            transition: all 0.5s ease;
        }

        /* تحسين مظهر الملاحظات */
        .hijri-note {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        .hijri-accuracy-indicator {
            font-size: 10px;
            color: #6c757d;
            margin-right: 5px;
        }

        .hijri-accurate {
            color: #28a745;
        }

        .hijri-approximate {
            color: #ffc107;
        }

        .hijri-error {
            color: #dc3545;
        }

        /* تحسين مظهر حقل التاريخ الهجري */
        #hijriDate {
            transition: all 0.3s ease;
        }

        /* تحسينات للطبقة المتراكبة للتاريخ بالحروف */
        .date-input-container {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .date-words-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(232, 245, 232, 0.95);
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            color: #155724;
            font-weight: 600;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        }

        .date-words-overlay:hover {
            background: rgba(232, 245, 232, 1);
            border-color: #28a745;
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }

        #hijriDate:focus {
            box-shadow: 0 0 0 3px rgba(0, 98, 51, 0.1);
            border-color: var(--morocco-green);
        }

        #hijriDate.error {
            border-color: var(--morocco-red);
            background-color: #fff5f5;
        }



        @keyframes slideInFromTop {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ===== Navigation ===== */
        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        @media (max-width: 768px) {
            .header-nav-links {
                flex-direction: column;
                gap: 5px;
            }

            .header-nav-link {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">🗃️ قاعدة بيانات المواطنين</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="header-navigation">
                <div class="header-nav-content">
                    <div class="header-nav-links">
                        <a href="main-dashboard.html" class="header-nav-link">🏠 الصفحة الرئيسية</a>
                        <a href="citizens-database-indexeddb.html" class="header-nav-link active">📝 إدارة البيانات</a>
                        <a href="search-citizens.html" class="header-nav-link">🔍 البحث في السجلات</a>
                        <a href="personal-id-form.html" class="header-nav-link">🆔 البطاقة الشخصية</a>
                        <a href="death-data-entry.html" class="header-nav-link">⚱️ تسجيل الوفاة</a>
                        <a href="employee-management.html" class="header-nav-link">👥 إدارة الموظفين</a>
                        <a href="data-transfer.html" class="header-nav-link">🔄 و 🛡️ النسخ الاحتياطية</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Statistics -->
            <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCitizens">0</div>
                <div class="stat-label">إجمالي المواطنين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="maleCount">0</div>
                <div class="stat-label">ذكور</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="femaleCount">0</div>
                <div class="stat-label">إناث</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRegistrations">0</div>
                <div class="stat-label">تسجيلات اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="withCertificates">0</div>
                <div class="stat-label">مع شهادات كاملة</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Form Section -->
            <div class="form-section">
                <h2>📝 تسجيل مواطن جديد</h2>

                <div id="alertContainer"></div>
                <div id="loadingIndicator" class="loading" style="display: none;">
                    ⏳ جاري التحميل...
                </div>

                <form id="citizenForm">
                    <div class="form-group">
                        <label for="firstNameAr">الاسم الشخصي (عربي):</label>
                        <input type="text" id="firstNameAr" name="firstNameAr" required placeholder="أحمد">
                    </div>

                    <div class="form-group">
                        <label for="firstNameFr">الاسم الشخصي (فرنسي):</label>
                        <input type="text" id="firstNameFr" name="firstNameFr" required placeholder="Ahmed">
                    </div>

                    <div class="form-group">
                        <label for="familyNameAr">الاسم العائلي (عربي):</label>
                        <input type="text" id="familyNameAr" name="familyNameAr" required placeholder="محمد علي">
                    </div>

                    <div class="form-group">
                        <label for="familyNameFr">الاسم العائلي (فرنسي):</label>
                        <input type="text" id="familyNameFr" name="familyNameFr" required placeholder="Mohamed Ali">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceAr">مكان الازدياد (عربي):</label>
                        <input type="text" id="birthPlaceAr" name="birthPlaceAr" required placeholder="الرباط">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceFr">مكان الازدياد (فرنسي):</label>
                        <input type="text" id="birthPlaceFr" name="birthPlaceFr" required placeholder="Rabat">
                    </div>

                    <div class="form-group">
                        <label for="birthDate">تاريخ الازدياد (ميلادي):</label>
                        <div class="date-input-container">
                            <input type="date" id="birthDate" name="birthDate" class="date-conversion-indicator" required onchange="convertToHijri(); showDateInWords();" onclick="toggleDateDisplay('birthDate', 'birthDateWords')" style="width: 100%; cursor: pointer;" title="اضغط للتبديل بين التاريخ الرقمي والتاريخ بالحروف">
                            <div id="birthDateWordsOverlay" class="date-words-overlay" onclick="toggleDateWordsOverlay('birthDateWordsOverlay')" title="اضغط لإخفاء/إظهار التاريخ بالحروف"></div>
                        </div>
                        <!-- حقل مخفي لحفظ التاريخ بالحروف -->
                        <input type="hidden" id="birthDateWords" name="birthDateWords">
                        <div class="hijri-note">يتم التحويل تلقائياً إلى التاريخ الهجري</div>
                    </div>

                    <div class="form-group">
                        <label for="hijriDate">التاريخ الهجري:</label>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="hijriMode" value="auto" checked onchange="toggleHijriMode()"> تلقائي
                            </label>
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="hijriMode" value="manual" onchange="toggleHijriMode()"> يدوي
                            </label>
                        </div>
                        <input type="text" id="hijriDate" name="hijriDate" class="date-conversion-indicator" placeholder="مثال: 5 رجب 1445" readonly style="background-color: #f8f9fa;">
                        <small id="hijriHelp" style="color: #6c757d; font-size: 12px;">سيتم تحويل التاريخ الميلادي تلقائياً • نظام دقيق محسن</small>

                    </div>

                    <div class="form-group">
                        <label for="gender">الجنس:</label>
                        <select id="gender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fatherNameAr">اسم الوالد (عربي):</label>
                        <input type="text" id="fatherNameAr" name="fatherNameAr" required placeholder="محمد علي حسن">
                    </div>

                    <div class="form-group">
                        <label for="fatherNameFr">اسم الوالد (فرنسي):</label>
                        <input type="text" id="fatherNameFr" name="fatherNameFr" required placeholder="Mohamed Ali Hassan">
                    </div>

                    <div class="form-group">
                        <label for="motherNameAr">اسم الوالدة (عربي):</label>
                        <input type="text" id="motherNameAr" name="motherNameAr" required placeholder="فاطمة أحمد محمد">
                    </div>

                    <div class="form-group">
                        <label for="motherNameFr">اسم الوالدة (فرنسي):</label>
                        <input type="text" id="motherNameFr" name="motherNameFr" required placeholder="Fatima Ahmed Mohamed">
                    </div>

                    <div class="form-group">
                        <label for="actNumber">رقم العقد:</label>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="auto" checked onchange="toggleActNumberMode()"> تلقائي
                            </label>
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="manual" onchange="toggleActNumberMode()"> يدوي
                            </label>
                        </div>
                        <input type="text" id="actNumber" name="actNumber" required readonly style="background-color: #f8f9fa;">
                        <small id="actNumberHelp" style="color: #6c757d; font-size: 12px;">سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة</small>
                    </div>

                    <div class="form-group">
                        <label for="registrationDate">تاريخ التسجيل:</label>
                        <div class="date-input-container">
                            <input type="date" id="registrationDate" name="registrationDate" required onchange="showRegistrationHijri(); showRegistrationDateInWords();" onclick="toggleDateDisplay('registrationDate', 'registrationDateWords')" style="width: 100%; cursor: pointer;" title="اضغط للتبديل بين التاريخ الرقمي والتاريخ بالحروف">
                            <div id="registrationDateWordsOverlay" class="date-words-overlay" onclick="toggleDateWordsOverlay('registrationDateWordsOverlay')" title="اضغط لإخفاء/إظهار التاريخ بالحروف"></div>
                        </div>
                        <!-- حقل مخفي لحفظ التاريخ بالحروف -->
                        <input type="hidden" id="registrationDateWords" name="registrationDateWords">
                        <div id="registrationHijriDisplay" style="margin-top: 5px; padding: 6px; background: #fff3cd; border-radius: 4px; font-size: 11px; color: #856404; display: none;">
                            <strong>الموافق بالهجري:</strong> <span id="registrationHijriDate"></span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 8px; flex-wrap: wrap; justify-content: center; margin-top: 15px;">
                        <button type="button" class="btn btn-success" onclick="saveAndNew()" style="font-size: 14px; padding: 10px 20px;" title="اختصار: Ctrl+S">💾➕ حفظ وإضافة جديد</button>
                        <button type="button" class="btn btn-warning" onclick="clearForm()" style="font-size: 14px; padding: 10px 20px;" title="اختصار: Ctrl+R">🗑️ مسح النموذج</button>
                        <button type="button" class="btn btn-info" onclick="toggleKeyboard()" style="font-size: 14px; padding: 10px 20px;">⌨️ لوحة المفاتيح</button>
                        <a href="search-citizens.html" class="btn btn-secondary" style="font-size: 14px; padding: 10px 20px;">🔍 البحث في السجلات</a>
                        <a href="death-certificate.html" class="btn btn-info" style="font-size: 14px; padding: 10px 20px; display: none;">📋 نسخة موجزة من رسم الوفاة</a>
                    </div>

                    <!-- Keyboard Shortcuts Info -->
                    <div style="text-align: center; margin-top: 8px; font-size: 11px; color: #6c757d;">
                        💡 اختصارات لوحة المفاتيح: Ctrl+S (حفظ وجديد) • Ctrl+R (مسح)
                    </div>
                </form>

                <!-- Virtual Keyboard -->
                <div id="virtualKeyboard" style="display: none; position: fixed; z-index: 1000; background: #f8f9fa; border: 2px solid #007bff; border-radius: 8px; padding: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); max-width: 90vw; cursor: move;">
                    <div id="keyboardHeader" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; cursor: move; padding: 5px; background: #e9ecef; border-radius: 4px; margin: -5px -5px 10px -5px;">
                        <h6 style="margin: 0; color: #495057; user-select: none;">🎹 لوحة المفاتيح الافتراضية <small style="color: #6c757d;">(اسحب لتحريك)</small></h6>
                        <div>
                            <button type="button" id="arabicKeyboard" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px; margin: 2px;">🇲🇦 عربي</button>
                            <button type="button" id="frenchKeyboard" class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px; margin: 2px;">🇫🇷 فرنسي</button>
                            <button type="button" id="closeKeyboard" class="btn btn-danger" style="font-size: 12px; padding: 4px 8px; margin: 2px;">✖</button>
                        </div>
                    </div>

                    <!-- Arabic Keyboard (Left to Right) -->
                    <div id="arabicKeys" style="display: block; direction: ltr;">
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="ض">ض</button>
                            <button type="button" class="key-btn" data-char="ص">ص</button>
                            <button type="button" class="key-btn" data-char="ث">ث</button>
                            <button type="button" class="key-btn" data-char="ق">ق</button>
                            <button type="button" class="key-btn" data-char="ف">ف</button>
                            <button type="button" class="key-btn" data-char="غ">غ</button>
                            <button type="button" class="key-btn" data-char="ع">ع</button>
                            <button type="button" class="key-btn" data-char="ه">ه</button>
                            <button type="button" class="key-btn" data-char="خ">خ</button>
                            <button type="button" class="key-btn" data-char="ح">ح</button>
                            <button type="button" class="key-btn" data-char="ج">ج</button>
                            <button type="button" class="key-btn" data-char="د">د</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="ش">ش</button>
                            <button type="button" class="key-btn" data-char="س">س</button>
                            <button type="button" class="key-btn" data-char="ي">ي</button>
                            <button type="button" class="key-btn" data-char="ب">ب</button>
                            <button type="button" class="key-btn" data-char="ل">ل</button>
                            <button type="button" class="key-btn" data-char="ا">ا</button>
                            <button type="button" class="key-btn" data-char="ت">ت</button>
                            <button type="button" class="key-btn" data-char="ن">ن</button>
                            <button type="button" class="key-btn" data-char="م">م</button>
                            <button type="button" class="key-btn" data-char="ك">ك</button>
                            <button type="button" class="key-btn" data-char="ط">ط</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="ئ">ئ</button>
                            <button type="button" class="key-btn" data-char="ء">ء</button>
                            <button type="button" class="key-btn" data-char="ؤ">ؤ</button>
                            <button type="button" class="key-btn" data-char="ر">ر</button>
                            <button type="button" class="key-btn" data-char="لا">لا</button>
                            <button type="button" class="key-btn" data-char="ى">ى</button>
                            <button type="button" class="key-btn" data-char="ة">ة</button>
                            <button type="button" class="key-btn" data-char="و">و</button>
                            <button type="button" class="key-btn" data-char="ز">ز</button>
                            <button type="button" class="key-btn" data-char="ظ">ظ</button>
                        </div>
                        <div style="text-align: center;">
                            <button type="button" class="key-btn space-btn" data-char=" ">مسافة</button>
                            <button type="button" class="key-btn delete-btn" data-action="backspace">حذف</button>
                            <button type="button" class="key-btn clear-btn" data-action="clear">مسح الكل</button>
                        </div>
                    </div>

                    <!-- French Keyboard (Right to Left - POIUY Layout) -->
                    <div id="frenchKeys" style="display: none; direction: rtl;">
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="P">P</button>
                            <button type="button" class="key-btn" data-char="O">O</button>
                            <button type="button" class="key-btn" data-char="I">I</button>
                            <button type="button" class="key-btn" data-char="U">U</button>
                            <button type="button" class="key-btn" data-char="Y">Y</button>
                            <button type="button" class="key-btn" data-char="T">T</button>
                            <button type="button" class="key-btn" data-char="R">R</button>
                            <button type="button" class="key-btn" data-char="E">E</button>
                            <button type="button" class="key-btn" data-char="Z">Z</button>
                            <button type="button" class="key-btn" data-char="A">A</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="M">M</button>
                            <button type="button" class="key-btn" data-char="L">L</button>
                            <button type="button" class="key-btn" data-char="K">K</button>
                            <button type="button" class="key-btn" data-char="J">J</button>
                            <button type="button" class="key-btn" data-char="H">H</button>
                            <button type="button" class="key-btn" data-char="G">G</button>
                            <button type="button" class="key-btn" data-char="F">F</button>
                            <button type="button" class="key-btn" data-char="D">D</button>
                            <button type="button" class="key-btn" data-char="S">S</button>
                            <button type="button" class="key-btn" data-char="Q">Q</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="À">À</button>
                            <button type="button" class="key-btn" data-char="Ç">Ç</button>
                            <button type="button" class="key-btn" data-char="È">È</button>
                            <button type="button" class="key-btn" data-char="É">É</button>
                            <button type="button" class="key-btn" data-char="N">N</button>
                            <button type="button" class="key-btn" data-char="B">B</button>
                            <button type="button" class="key-btn" data-char="V">V</button>
                            <button type="button" class="key-btn" data-char="C">C</button>
                            <button type="button" class="key-btn" data-char="X">X</button>
                            <button type="button" class="key-btn" data-char="W">W</button>
                        </div>
                        <div style="direction: ltr; text-align: center;">
                            <button type="button" class="key-btn space-btn" data-char=" ">Espace</button>
                            <button type="button" class="key-btn delete-btn" data-action="backspace">Effacer</button>
                            <button type="button" class="key-btn clear-btn" data-action="clear">Tout effacer</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Section -->
            <div class="form-section">
                <h2>📄 إدارة الشهادة الكاملة</h2>

                <!-- Upload Button -->
                <div style="text-align: center; margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()" style="font-size: 14px; padding: 10px 20px;">
                        📷 تحميل الشهادة الكاملة
                    </button>
                </div>

                <!-- Display Area -->
                <div class="upload-area" id="uploadArea" style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; padding: 20px; text-align: center; transition: all 0.3s ease;">
                    <div id="uploadContent">
                        <div style="color: #6c757d; font-size: 32px; margin-bottom: 12px;">📄</div>
                        <h4 style="color: #6c757d; margin-bottom: 8px; font-size: 16px;">منطقة عرض الشهادة</h4>
                        <p style="color: #6c757d; margin-bottom: 8px; font-size: 13px;">استخدم الزر أعلاه لتحميل الشهادة</p>
                        <p style="color: #95a5a6; font-size: 12px;">يدعم: JPG, PNG, PDF</p>
                    </div>

                    <!-- Uploaded Image Display -->
                    <div id="imageDisplay" style="display: none;">
                        <div id="imageContainer" style="margin: 10px auto; position: relative; overflow: hidden; border-radius: 6px; border: 1px solid #ddd; max-width: 100%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; cursor: zoom-in;">
                            <img id="uploadedImage" style="user-select: none; -webkit-user-drag: none; transform-origin: center;">
                        </div>


                    </div>
                </div>

                <!-- Enhanced Image Controls -->
                <div id="imageControls" style="display: none; margin-top: 10px; text-align: center;">
                    <!-- أزرار التكبير والتصغير -->
                    <div class="control-group" style="margin-bottom: 10px;">
                        <button class="btn btn-secondary" onclick="zoomIn(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍+ تكبير</button>
                        <button class="btn btn-secondary" onclick="zoomOut(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍- تصغير</button>
                        <button class="btn btn-secondary" onclick="resetZoom(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔄 إعادة تعيين</button>
                    </div>

                    <!-- أزرار التحكم في الاتجاهات -->
                    <div class="direction-controls" id="directionControls" style="display: none; margin: 10px 0;">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 5px; max-width: 150px; margin: 0 auto;">
                            <div></div>
                            <button class="btn btn-info" onclick="moveImage(0, -20)" title="أعلى" style="font-size: 16px; padding: 8px;">⬆️</button>
                            <div></div>
                            <button class="btn btn-info" onclick="moveImage(-20, 0)" title="يسار" style="font-size: 16px; padding: 8px;">⬅️</button>
                            <button class="btn btn-warning" onclick="resetImagePosition()" title="المنتصف" style="font-size: 16px; padding: 8px;">🎯</button>
                            <button class="btn btn-info" onclick="moveImage(20, 0)" title="يمين" style="font-size: 16px; padding: 8px;">➡️</button>
                            <div></div>
                            <button class="btn btn-info" onclick="moveImage(0, 20)" title="أسفل" style="font-size: 16px; padding: 8px;">⬇️</button>
                            <div></div>
                        </div>
                    </div>

                    <!-- أزرار الدوران والوظائف الأخرى -->
                    <div class="control-group" style="margin-bottom: 10px;">
                        <button class="btn btn-secondary" onclick="rotateLeft(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">↺ يسار</button>
                        <button class="btn btn-secondary" onclick="rotateRight(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">↻ يمين</button>
                        <button class="btn btn-secondary" onclick="resetRotation(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔄 إعادة دوران</button>
                        <button class="btn btn-secondary" onclick="fitToScreen(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">📐 ملء الشاشة</button>
                        <button class="btn btn-secondary" onclick="downloadImage(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">💾 تحميل</button>
                    </div>

                    <div style="margin-bottom: 8px; color: #6c757d; font-size: 11px;">
                        💡 اضغط على الصورة أو عجلة الفأرة للتكبير • استخدم أزرار الاتجاهات للتحريك<br>
                        ⌨️ الكيبورد (تخطيط POIUY): <strong>+/-</strong> تكبير • <strong>P/O</strong> دوران • <strong>I</strong> إعادة تعيين • <strong>U</strong> ملء الشاشة • <strong>Y</strong> تحميل
                    </div>
                    <button class="btn btn-secondary" onclick="removeImage(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🗑️ إزالة الصورة</button>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="fileInput" accept="image/*,.pdf" style="display: none;" onchange="handleFileUpload(event)">



                <!-- Instructions -->
                <div style="margin-top: 12px; padding: 10px; background: #e8f5e8; border: 1px solid #c3e6cb; border-radius: 6px;">
                    <h5 style="color: #155724; margin-bottom: 6px; font-size: 14px;">📋 تعليمات الاستخدام:</h5>
                    <ul style="color: #155724; text-align: right; margin: 0; padding-right: 15px; font-size: 12px;">
                        <li>قم بتحميل صورة الشهادة الكاملة للمواطن</li>
                        <li>اقرأ المعلومات من الشهادة المعروضة</li>
                        <li>أدخل البيانات يدوياً في النموذج على اليسار</li>
                        <li>تأكد من صحة جميع المعلومات قبل الحفظ</li>
                        <li>يمكنك تكبير الصورة لرؤية التفاصيل بوضوح</li>
                    </ul>
                </div>

                <!-- Backup Section -->
                <div style="margin-top: 12px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                    <h5 style="color: #856404; margin-bottom: 8px; font-size: 14px;">💾 النسخ الاحتياطي والأرشفة</h5>
                    <div style="display: flex; gap: 6px; flex-wrap: wrap; justify-content: center;">
                        <button type="button" class="btn btn-primary" onclick="exportAllData()" style="font-size: 12px; padding: 6px 12px;">📤 تصدير البيانات</button>
                        <button type="button" class="btn btn-success" onclick="importData()" style="font-size: 12px; padding: 6px 12px;">📥 استيراد البيانات</button>
                        <button type="button" class="btn btn-info" onclick="autoBackup()" style="font-size: 12px; padding: 6px 12px;">🔄 نسخ احتياطي</button>
                    </div>
                    <input type="file" id="importFileInput" accept=".json" style="display: none;" onchange="handleImportFile(event)">
                    <div style="margin-top: 6px; font-size: 11px; color: #856404; text-align: center;">
                        💡 استخدم التصدير/الاستيراد لنقل البيانات بين الأجهزة
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 نظام الحالة المدنية - جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- Include IndexedDB Manager -->
    <script src="indexeddb-manager.js"></script>
    <script>
        // مكتبة التحويل الدقيقة بين التقويم الميلادي والهجري
        class AccurateHijriConverter {
            constructor() {
                // أسماء الأشهر الهجرية
                this.hijriMonths = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ];

                // أسماء الأشهر الميلادية بالعربية
                this.gregorianMonths = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];
            }

            // تحويل دقيق من ميلادي إلى هجري باستخدام Intl.DateTimeFormat
            gregorianToHijri(gregorianDate) {
                try {
                    const date = new Date(gregorianDate);
                    if (isNaN(date.getTime())) {
                        throw new Error('تاريخ غير صحيح');
                    }

                    // استخدام Intl.DateTimeFormat للتحويل الدقيق مع أرقام إنجليزية
                    const hijriDateFormatted = new Intl.DateTimeFormat('en-SA-u-ca-islamic', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    }).format(date);

                    // الحصول على أسماء الأشهر بالعربية
                    const hijriDateArabicMonth = new Intl.DateTimeFormat('ar-SA-u-ca-islamic', {
                        month: 'long'
                    }).format(date);

                    // استخراج الأجزاء المنفصلة باستخدام أرقام إنجليزية
                    const hijriParts = new Intl.DateTimeFormat('en-SA-u-ca-islamic', {
                        day: 'numeric',
                        month: 'numeric',
                        year: 'numeric'
                    }).formatToParts(date);

                    let day, month, year;
                    hijriParts.forEach(part => {
                        if (part.type === 'day') {
                            day = parseInt(part.value);
                        }
                        if (part.type === 'month') {
                            month = parseInt(part.value);
                        }
                        if (part.type === 'year') {
                            year = parseInt(part.value);
                        }
                    });

                    // التحقق من صحة القيم
                    if (isNaN(day) || isNaN(month) || isNaN(year)) {
                        console.warn('فشل في استخراج التاريخ الهجري، استخدام الطريقة الاحتياطية');
                        return this.fallbackGregorianToHijri(gregorianDate);
                    }

                    // الحصول على اسم الشهر بالعربية
                    const monthName = this.hijriMonths[month - 1] || `الشهر ${month}`;

                    return {
                        year: year,
                        month: month,
                        day: day,
                        monthName: monthName,
                        formatted: `${day} ${monthName} ${year}`,
                        originalFormatted: hijriDateFormatted,
                        arabicMonthName: hijriDateArabicMonth
                    };
                } catch (error) {
                    console.error('خطأ في تحويل التاريخ:', error);
                    // fallback للطريقة التقريبية
                    return this.fallbackGregorianToHijri(gregorianDate);
                }
            }

            // طريقة احتياطية للتحويل (في حالة عدم دعم المتصفح)
            fallbackGregorianToHijri(gregorianDate) {
                try {
                    const date = new Date(gregorianDate);
                    const gregorianYear = date.getFullYear();

                    // حساب تقريبي محسن
                    const hijriYear = Math.floor((gregorianYear - 622) * 1.030684);
                    const dayOfYear = this.getDayOfYear(date);
                    const hijriDayOfYear = Math.floor(dayOfYear * 354 / 365);

                    let hijriMonth = Math.floor(hijriDayOfYear / 29.5) + 1;
                    let hijriDay = hijriDayOfYear % 30;

                    // تصحيحات
                    if (hijriMonth > 12) hijriMonth = 12;
                    if (hijriDay === 0) hijriDay = 1;
                    if (hijriDay > 29) hijriDay = 29;

                    return {
                        year: hijriYear,
                        month: hijriMonth,
                        day: hijriDay,
                        monthName: this.hijriMonths[hijriMonth - 1],
                        formatted: `${hijriDay} ${this.hijriMonths[hijriMonth - 1]} ${hijriYear}`,
                        originalFormatted: `${hijriDay} ${this.hijriMonths[hijriMonth - 1]} ${hijriYear}`,
                        isFallback: true
                    };
                } catch (error) {
                    console.error('خطأ في التحويل الاحتياطي:', error);
                    return null;
                }
            }

            // تحويل من هجري إلى ميلادي (محسن)
            hijriToGregorian(hijriYear, hijriMonth, hijriDay) {
                try {
                    // محاولة التحويل العكسي باستخدام تقدير محسن
                    // هذا تقريبي لأن التحويل العكسي الدقيق يتطلب مكتبات متخصصة

                    const gregorianYear = Math.floor(hijriYear / 1.030684) + 622;
                    const hijriDayOfYear = (hijriMonth - 1) * 29.5 + hijriDay;
                    const gregorianDayOfYear = Math.floor(hijriDayOfYear * 365 / 354);

                    const date = new Date(gregorianYear, 0, gregorianDayOfYear);

                    // تحسين الدقة بالتحقق من التحويل العكسي
                    const verificationHijri = this.gregorianToHijri(date.toISOString().split('T')[0]);
                    if (verificationHijri && !verificationHijri.isFallback) {
                        // إذا كان التحويل العكسي مختلف، نحاول تصحيحه
                        const dayDiff = hijriDay - verificationHijri.day;
                        if (Math.abs(dayDiff) <= 2) {
                            date.setDate(date.getDate() + dayDiff);
                        }
                    }

                    return {
                        year: date.getFullYear(),
                        month: date.getMonth() + 1,
                        day: date.getDate(),
                        monthName: this.gregorianMonths[date.getMonth()],
                        formatted: `${date.getDate()} ${this.gregorianMonths[date.getMonth()]} ${date.getFullYear()}`,
                        iso: date.toISOString().split('T')[0],
                        isApproximate: true
                    };
                } catch (error) {
                    console.error('خطأ في تحويل التاريخ الهجري:', error);
                    return null;
                }
            }

            // حساب يوم السنة
            getDayOfYear(date) {
                const start = new Date(date.getFullYear(), 0, 0);
                const diff = date - start;
                return Math.floor(diff / (1000 * 60 * 60 * 24));
            }

            // تحليل النص الهجري المحسن
            parseHijriText(hijriText) {
                try {
                    if (!hijriText || hijriText.trim() === '') {
                        return null;
                    }

                    const text = hijriText.trim();

                    // أنماط مختلفة للتاريخ الهجري
                    const patterns = [
                        /(\d+)\s+(\w+)\s+(\d+)/,  // 5 رجب 1445
                        /(\d+)\/(\d+)\/(\d+)/,    // 5/7/1445
                        /(\d+)-(\d+)-(\d+)/,      // 5-7-1445
                        /(\d+)\s*-\s*(\d+)\s*-\s*(\d+)/, // 5 - 7 - 1445
                        /(\d+)\s*\/\s*(\d+)\s*\/\s*(\d+)/ // 5 / 7 / 1445
                    ];

                    for (const pattern of patterns) {
                        const match = text.match(pattern);
                        if (match) {
                            const day = parseInt(match[1]);
                            let month = match[2];
                            const year = parseInt(match[3]);

                            // إذا كان الشهر نص، ابحث عن رقمه
                            if (isNaN(month)) {
                                // البحث الدقيق أولاً
                                month = this.hijriMonths.indexOf(month) + 1;

                                if (month === 0) {
                                    // البحث الجزئي المحسن
                                    const monthText = match[2].trim();
                                    for (let i = 0; i < this.hijriMonths.length; i++) {
                                        const hijriMonth = this.hijriMonths[i];
                                        if (hijriMonth.includes(monthText) ||
                                            monthText.includes(hijriMonth) ||
                                            this.similarText(hijriMonth, monthText)) {
                                            month = i + 1;
                                            break;
                                        }
                                    }
                                }
                            } else {
                                month = parseInt(month);
                            }

                            // التحقق من صحة القيم
                            if (month > 0 && month <= 12 &&
                                day > 0 && day <= 30 &&
                                year > 1400 && year < 1500) {
                                return { day, month, year };
                            }
                        }
                    }
                    return null;
                } catch (error) {
                    console.error('خطأ في تحليل النص الهجري:', error);
                    return null;
                }
            }

            // مقارنة النصوص للتشابه
            similarText(str1, str2) {
                if (!str1 || !str2) return false;

                // إزالة التشكيل والمسافات
                const clean1 = str1.replace(/[\u064B-\u0652\s]/g, '');
                const clean2 = str2.replace(/[\u064B-\u0652\s]/g, '');

                // فحص التشابه
                return clean1.includes(clean2) || clean2.includes(clean1) ||
                       this.levenshteinDistance(clean1, clean2) <= 2;
            }

            // حساب مسافة Levenshtein للتشابه
            levenshteinDistance(str1, str2) {
                const matrix = [];
                for (let i = 0; i <= str2.length; i++) {
                    matrix[i] = [i];
                }
                for (let j = 0; j <= str1.length; j++) {
                    matrix[0][j] = j;
                }
                for (let i = 1; i <= str2.length; i++) {
                    for (let j = 1; j <= str1.length; j++) {
                        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                            matrix[i][j] = matrix[i - 1][j - 1];
                        } else {
                            matrix[i][j] = Math.min(
                                matrix[i - 1][j - 1] + 1,
                                matrix[i][j - 1] + 1,
                                matrix[i - 1][j] + 1
                            );
                        }
                    }
                }
                return matrix[str2.length][str1.length];
            }

            // تحويل الأرقام العربية إلى إنجليزية
            convertArabicToEnglishNumbers(str) {
                if (!str) return str;

                const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

                let result = str.toString();
                for (let i = 0; i < arabicNumbers.length; i++) {
                    result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
                }

                return result;
            }

            // تحويل الأرقام الإنجليزية إلى عربية (للعرض)
            convertEnglishToArabicNumbers(str) {
                if (!str) return str;

                const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

                let result = str.toString();
                for (let i = 0; i < englishNumbers.length; i++) {
                    result = result.replace(new RegExp(englishNumbers[i], 'g'), arabicNumbers[i]);
                }

                return result;
            }

            // فحص دعم المتصفح للتقويم الإسلامي
            isIslamicCalendarSupported() {
                try {
                    const testDate = new Date('2024-01-01');
                    const hijriTest = new Intl.DateTimeFormat('en-SA-u-ca-islamic', {
                        year: 'numeric'
                    }).format(testDate);

                    // التحقق من أن النتيجة ليست نفس السنة الميلادية
                    const hijriYear = parseInt(hijriTest);
                    return hijriTest && !isNaN(hijriYear) && hijriYear !== 2024;
                } catch (error) {
                    console.warn('خطأ في فحص دعم التقويم الإسلامي:', error);
                    return false;
                }
            }
        }

        // إنشاء instance من المحول الدقيق
        const hijriConverter = new AccurateHijriConverter();

        // كلاس تحويل التاريخ إلى كتابة بالحروف
        class DateToWordsConverter {
            constructor() {
                // أرقام الوحدات
                this.units = [
                    '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'
                ];

                // أرقام العشرات
                this.tens = [
                    '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
                ];

                // أرقام من 10 إلى 19
                this.teens = [
                    'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
                    'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
                ];

                // أسماء الأشهر الميلادية بالعربية
                this.gregorianMonths = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'دجنبر'
                ];

                // أسماء الأشهر الهجرية
                this.hijriMonths = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ];

                // أرقام المئات
                this.hundreds = [
                    '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
                ];

                // أرقام الآلاف
                this.thousands = [
                    '', 'ألف', 'ألفان', 'ثلاثة آلاف', 'أربعة آلاف', 'خمسة آلاف', 'ستة آلاف', 'سبعة آلاف', 'ثمانية آلاف', 'تسعة آلاف'
                ];
            }

            // تحويل رقم إلى كلمات (1-99)
            numberToWords(num) {
                if (num === 0) return '';
                if (num === 1) return 'واحد';
                if (num === 2) return 'اثنان';
                if (num >= 10 && num <= 19) {
                    return this.teens[num - 10];
                }
                if (num >= 20 && num <= 99) {
                    const tensDigit = Math.floor(num / 10);
                    const unitsDigit = num % 10;
                    if (unitsDigit === 0) {
                        return this.tens[tensDigit];
                    } else {
                        return this.units[unitsDigit] + ' و' + this.tens[tensDigit];
                    }
                }
                if (num >= 3 && num <= 9) {
                    return this.units[num];
                }
                return num.toString();
            }

            // تحويل رقم كامل إلى كلمات (يدعم حتى 9999)
            fullNumberToWords(num) {
                if (num === 0) return 'صفر';
                if (num === 1) return 'واحد';
                if (num === 2) return 'اثنان';

                let result = '';

                // الآلاف
                if (num >= 1000) {
                    const thousandsDigit = Math.floor(num / 1000);
                    if (thousandsDigit === 1) {
                        result += 'ألف';
                    } else if (thousandsDigit === 2) {
                        result += 'ألفان';
                    } else if (thousandsDigit >= 3 && thousandsDigit <= 10) {
                        result += this.numberToWords(thousandsDigit) + ' آلاف';
                    } else {
                        result += this.numberToWords(thousandsDigit) + ' ألف';
                    }
                    num = num % 1000;
                    if (num > 0) result += ' و';
                }

                // المئات
                if (num >= 100) {
                    const hundredsDigit = Math.floor(num / 100);
                    if (hundredsDigit === 1) {
                        result += 'مائة';
                    } else if (hundredsDigit === 2) {
                        result += 'مائتان';
                    } else {
                        result += this.units[hundredsDigit] + 'مائة';
                    }
                    num = num % 100;
                    if (num > 0) result += ' و';
                }

                // العشرات والوحدات
                if (num > 0) {
                    result += this.numberToWords(num);
                }

                return result;
            }

            // تحويل تاريخ ميلادي إلى كلمات (الشهر فقط)
            gregorianDateToWords(dateString) {
                try {
                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) {
                        return null;
                    }

                    const day = date.getDate();
                    const month = date.getMonth(); // 0-based
                    const year = date.getFullYear();

                    // عرض اليوم والسنة بالأرقام، والشهر بالحروف فقط
                    const monthName = this.gregorianMonths[month];

                    return `${day} ${monthName} ${year}`;
                } catch (error) {
                    console.error('خطأ في تحويل التاريخ الميلادي إلى كلمات:', error);
                    return null;
                }
            }

            // تحويل تاريخ هجري إلى كلمات (الشهر فقط)
            hijriDateToWords(hijriDateString) {
                try {
                    // تحليل التاريخ الهجري
                    const parsed = hijriConverter.parseHijriText(hijriDateString);
                    if (!parsed) {
                        return null;
                    }

                    // عرض اليوم والسنة بالأرقام، والشهر بالحروف فقط
                    const monthName = this.hijriMonths[parsed.month - 1];

                    return `${parsed.day} ${monthName} ${parsed.year}`;
                } catch (error) {
                    console.error('خطأ في تحويل التاريخ الهجري إلى كلمات:', error);
                    return null;
                }
            }
        }

        // إنشاء instance من محول التاريخ إلى كلمات
        const dateToWords = new DateToWordsConverter();

        // فحص دعم المتصفح وعرض رسالة
        document.addEventListener('DOMContentLoaded', function() {
            if (hijriConverter.isIslamicCalendarSupported()) {
                console.log('✅ المتصفح يدعم التحويل الدقيق للتقويم الإسلامي');

                // اختبار سريع للتأكد من عمل التحويل
                const testResult = hijriConverter.gregorianToHijri('2024-01-01');
                if (testResult && !isNaN(testResult.day)) {
                    console.log('✅ اختبار التحويل نجح:', testResult.formatted);
                } else {
                    console.warn('⚠️ فشل اختبار التحويل، سيتم استخدام الطريقة الاحتياطية');
                }
            } else {
                console.warn('⚠️ المتصفح لا يدعم التحويل الدقيق، سيتم استخدام التحويل التقريبي');
            }
        });
    </script>
    <script>
        // Global variables
        let editingId = null;
        let currentCertificateImage = null;

        // إضافة مستمع لتغيير حجم النافذة لإعادة ضبط الصورة
        window.addEventListener('resize', function() {
            const image = document.getElementById('uploadedImage');
            const container = document.getElementById('imageContainer');
            const imageDisplay = document.getElementById('imageDisplay');

            // إعادة ضبط الصورة فقط إذا كانت معروضة
            if (image && container && imageDisplay &&
                imageDisplay.style.display !== 'none' &&
                imageNaturalWidth > 0 && imageNaturalHeight > 0) {

                console.log('🔄 إعادة ضبط الصورة بعد تغيير حجم النافذة');
                adjustImageSize(image, container);
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Initialize IndexedDB
                await citizensDB.init();
                console.log('تم تهيئة IndexedDB بنجاح');

                // Update statistics
                await updateStatistics();

                // Set today's date as default
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('registrationDate').value = today;

                // عرض التاريخ الهجري لتاريخ اليوم
                showRegistrationHijri();

                // عرض تاريخ التسجيل بالحروف
                showRegistrationDateInWords();

                // Check for edit parameters FIRST
                checkForEditParameters();

                // Generate automatic act number ONLY if not editing
                if (!editingId) {
                    generateActNumber();
                }

                // Add keyboard shortcuts
                setupKeyboardShortcuts();

                // Initialize virtual keyboard
                initKeyboardEvents();

            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showAlert('❌ خطأ في تهيئة قاعدة البيانات', 'error');
            }
        });

        // Setup keyboard shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+S or Cmd+S for save and new (الوظيفة الوحيدة المتبقية)
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    saveAndNew();
                }

                // Ctrl+R or Cmd+R for clear form (prevent default browser refresh)
                if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                    e.preventDefault();
                    clearForm();
                }
            });
        }

        // Update statistics with IndexedDB support
        async function updateStatistics() {
            try {
                const dbInfo = await citizensDB.getDatabaseInfo();

                if (dbInfo) {
                    document.getElementById('totalCitizens').textContent = dbInfo.totalCitizens.toLocaleString();
                    document.getElementById('withCertificates').textContent = dbInfo.withCertificates.toLocaleString();

                    // Get all citizens for detailed stats
                    const citizens = await citizensDB.getAllCitizens();
                    const males = citizens.filter(c => c.gender === 'ذكر').length;
                    const females = citizens.filter(c => c.gender === 'أنثى').length;

                    const today = new Date().toISOString().split('T')[0];
                    const todayRegistrations = citizens.filter(c => c.registrationDate === today).length;

                    document.getElementById('maleCount').textContent = males.toLocaleString();
                    document.getElementById('femaleCount').textContent = females.toLocaleString();
                    document.getElementById('todayRegistrations').textContent = todayRegistrations.toLocaleString();
                }
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // Generate automatic act number with uniqueness guarantee
        async function generateActNumber() {
            try {
                const citizens = await citizensDB.getAllCitizens();
                const currentYear = new Date().getFullYear();

                // جمع جميع أرقام العقود الموجودة لهذه السنة
                const existingActNumbers = new Set();
                citizens.forEach(citizen => {
                    if (citizen.actNumber) {
                        const actNumber = citizen.actNumber.toString();
                        // التحقق من أن الرقم ينتهي بالسنة الحالية
                        if (actNumber.endsWith(`/${currentYear}`) || actNumber.endsWith(`-${currentYear}`) || actNumber.endsWith(`.${currentYear}`)) {
                            existingActNumbers.add(actNumber);
                        }
                    }
                });

                console.log(`📊 Existing act numbers for ${currentYear}:`, Array.from(existingActNumbers));

                // البحث عن أول رقم متاح
                let nextNumber = 1;
                let actNumber = `${nextNumber}/${currentYear}`;

                while (existingActNumbers.has(actNumber)) {
                    nextNumber++;
                    actNumber = `${nextNumber}/${currentYear}`;
                }

                console.log(`✅ Generated unique act number: ${actNumber}`);
                document.getElementById('actNumber').value = actNumber;

                return actNumber;
            } catch (error) {
                console.error('خطأ في توليد رقم القيد:', error);
                const currentYear = new Date().getFullYear();
                const fallbackNumber = `1/${currentYear}`;
                document.getElementById('actNumber').value = fallbackNumber;
                return fallbackNumber;
            }
        }

        // Toggle act number mode
        function toggleActNumberMode() {
            const mode = document.querySelector('input[name="actNumberMode"]:checked').value;
            const actNumberInput = document.getElementById('actNumber');
            const helpText = document.getElementById('actNumberHelp');

            if (mode === 'auto') {
                actNumberInput.readOnly = true;
                actNumberInput.style.backgroundColor = '#f8f9fa';
                helpText.textContent = 'سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة';
                generateActNumber();
            } else {
                actNumberInput.readOnly = false;
                actNumberInput.style.backgroundColor = 'white';
                helpText.textContent = 'أدخل رقم القيد يدوياً بصيغة: رقم/سنة';
                actNumberInput.focus();
            }
        }

        // Toggle hijri date mode
        function toggleHijriMode() {
            const mode = document.querySelector('input[name="hijriMode"]:checked').value;
            const hijriInput = document.getElementById('hijriDate');
            const helpText = document.getElementById('hijriHelp');


            if (mode === 'auto') {
                hijriInput.readOnly = true;
                hijriInput.style.backgroundColor = '#f8f9fa';
                helpText.textContent = 'سيتم تحويل التاريخ الميلادي تلقائياً';

                // تحويل التاريخ الحالي إذا كان موجود
                const birthDate = document.getElementById('birthDate').value;
                if (birthDate) {
                    convertToHijri();
                }
            } else {
                hijriInput.readOnly = false;
                hijriInput.style.backgroundColor = 'white';
                helpText.textContent = 'أدخل التاريخ الهجري يدوياً (مثال: 5 رجب 1445)';

                hijriInput.focus();

                // إضافة مستمع للتحويل العكسي
                hijriInput.addEventListener('input', convertToGregorian);
            }
        }

        // تحويل التاريخ الميلادي إلى هجري (محسن)
        function convertToHijri() {
            const birthDate = document.getElementById('birthDate').value;
            const hijriMode = document.querySelector('input[name="hijriMode"]:checked').value;

            if (!birthDate || hijriMode !== 'auto') {
                return;
            }

            const hijriDate = hijriConverter.gregorianToHijri(birthDate);
            if (hijriDate) {
                // عرض التاريخ الهجري
                document.getElementById('hijriDate').value = hijriDate.formatted;

                // إضافة تأثير بصري للتحويل
                const hijriInput = document.getElementById('hijriDate');
                hijriInput.classList.add('date-conversion-animation');
                setTimeout(() => hijriInput.classList.remove('date-conversion-animation'), 500);



                console.log('✅ تم تحويل التاريخ:', {
                    gregorian: birthDate,
                    hijri: hijriDate.formatted,
                    accurate: !hijriDate.isFallback,
                    originalFormatted: hijriDate.originalFormatted
                });
            } else {
                console.error('❌ فشل في تحويل التاريخ');

            }
        }

        // تحويل التاريخ الهجري إلى ميلادي (للوضع اليدوي - محسن)
        function convertToGregorian() {
            const hijriText = document.getElementById('hijriDate').value;
            const hijriMode = document.querySelector('input[name="hijriMode"]:checked').value;

            if (!hijriText || hijriMode !== 'manual') {
                return;
            }

            const parsedHijri = hijriConverter.parseHijriText(hijriText);
            if (parsedHijri) {
                const gregorianDate = hijriConverter.hijriToGregorian(
                    parsedHijri.year,
                    parsedHijri.month,
                    parsedHijri.day
                );

                if (gregorianDate) {


                    // اقتراح تحديث التاريخ الميلادي (اختياري)
                    if (gregorianDate.iso) {
                        const birthDateInput = document.getElementById('birthDate');
                        if (!birthDateInput.value || confirm('هل تريد تحديث التاريخ الميلادي المقابل؟')) {
                            birthDateInput.value = gregorianDate.iso;
                        }
                    }

                    console.log('✅ تم تحويل التاريخ الهجري:', {
                        hijri: hijriText,
                        parsed: parsedHijri,
                        gregorian: gregorianDate.formatted,
                        iso: gregorianDate.iso,
                        approximate: gregorianDate.isApproximate
                    });
                } else {
                    console.warn('⚠️ فشل في تحويل التاريخ الهجري');

                }
            } else {
                console.warn('⚠️ لم يتم التعرف على تنسيق التاريخ الهجري');


                // عرض رسالة مساعدة
                const hijriInput = document.getElementById('hijriDate');
                const originalPlaceholder = hijriInput.placeholder;
                hijriInput.placeholder = 'تنسيق غير صحيح - مثال: 15 رمضان 1445';
                setTimeout(() => {
                    hijriInput.placeholder = originalPlaceholder;
                }, 3000);
            }
        }

        // عرض التاريخ الهجري لتاريخ التسجيل
        function showRegistrationHijri() {
            const registrationDate = document.getElementById('registrationDate').value;

            if (!registrationDate) {
                document.getElementById('registrationHijriDisplay').style.display = 'none';
                return;
            }

            const hijriDate = hijriConverter.gregorianToHijri(registrationDate);
            if (hijriDate) {
                // إضافة معلومات الدقة
                let accuracyInfo = '';
                if (hijriDate.isFallback) {
                    accuracyInfo = ' (تقريبي)';
                } else {
                    accuracyInfo = ' (دقيق)';
                }

                document.getElementById('registrationHijriDate').innerHTML = `${hijriDate.formatted}<span style="color: #856404; font-size: 9px;">${accuracyInfo}</span>`;
                document.getElementById('registrationHijriDisplay').style.display = 'block';
            }
        }

        // عرض التاريخ بالحروف لتاريخ الازدياد في نفس الحقل
        function showDateInWords() {
            const birthDateInput = document.getElementById('birthDate');
            const birthDate = birthDateInput.value;
            const birthDateWordsOverlay = document.getElementById('birthDateWordsOverlay');
            const birthDateWordsHidden = document.getElementById('birthDateWords');

            if (!birthDate) {
                birthDateWordsOverlay.style.display = 'none';
                birthDateWordsOverlay.textContent = '';
                birthDateWordsHidden.value = '';
                // إعادة عرض التاريخ الرقمي
                birthDateInput.type = 'date';
                return;
            }

            // حفظ القيمة الأصلية للتاريخ
            saveOriginalDateValue('birthDate', birthDate);

            try {
                const dateInWords = dateToWords.gregorianDateToWords(birthDate);
                if (dateInWords) {
                    // حفظ التاريخ بالحروف في الحقل المخفي
                    birthDateWordsHidden.value = dateInWords;

                    // تحويل حقل التاريخ إلى نص وعرض التاريخ بالحروف
                    birthDateInput.type = 'text';
                    birthDateInput.value = dateInWords;
                    birthDateInput.style.background = '#f8fff8';
                    birthDateInput.style.color = '#155724';
                    birthDateInput.style.fontWeight = '500';
                    birthDateInput.style.textAlign = 'center';

                    // إضافة تأثير بصري جميل
                    birthDateInput.style.transform = 'scale(0.98)';
                    birthDateInput.style.transition = 'all 0.3s ease';
                    setTimeout(() => {
                        birthDateInput.style.transform = 'scale(1)';
                    }, 100);

                    // إخفاء الطبقة المتراكبة لأننا نعرض في نفس الحقل
                    birthDateWordsOverlay.style.display = 'none';

                    console.log('📝 تم تحويل تاريخ الازدياد إلى كلمات وعرضه في نفس الحقل:', dateInWords);
                } else {
                    birthDateWordsOverlay.style.display = 'none';
                    birthDateWordsOverlay.textContent = '';
                    birthDateWordsHidden.value = '';
                    console.warn('⚠️ فشل في تحويل تاريخ الازدياد إلى كلمات');
                }
            } catch (error) {
                console.error('❌ خطأ في عرض التاريخ بالحروف:', error);
                birthDateWordsOverlay.style.display = 'none';
                birthDateWordsOverlay.textContent = '';
                birthDateWordsHidden.value = '';
            }
        }

        // تبديل بين عرض التاريخ بالحروف والتاريخ الرقمي
        function toggleDateWordsOverlay(overlayId) {
            if (overlayId === 'birthDateWordsOverlay') {
                toggleDateDisplay('birthDate', 'birthDateWords');
            } else if (overlayId === 'registrationDateWordsOverlay') {
                toggleDateDisplay('registrationDate', 'registrationDateWords');
            }
        }

        // دالة التبديل بين التاريخ بالحروف والتاريخ الرقمي
        function toggleDateDisplay(inputId, hiddenId) {
            const input = document.getElementById(inputId);
            const hidden = document.getElementById(hiddenId);

            if (!input || !hidden) return;

            if (input.type === 'text' && hidden.value) {
                // التبديل إلى التاريخ الرقمي
                input.type = 'date';
                input.value = getOriginalDateValue(inputId);
                input.style.background = '';
                input.style.color = '';
                input.style.fontWeight = '';
                input.style.textAlign = '';
                console.log('📅 تم التبديل إلى التاريخ الرقمي');
            } else if (input.type === 'date' && input.value && hidden.value) {
                // التبديل إلى التاريخ بالحروف
                input.type = 'text';
                input.value = hidden.value;
                input.style.background = '#f8fff8';
                input.style.color = '#155724';
                input.style.fontWeight = '500';
                input.style.textAlign = 'center';
                console.log('📝 تم التبديل إلى التاريخ بالحروف');
            }
        }

        // متغيرات لحفظ القيم الأصلية للتواريخ
        let originalBirthDate = '';
        let originalRegistrationDate = '';

        // الحصول على القيمة الأصلية للتاريخ
        function getOriginalDateValue(inputId) {
            if (inputId === 'birthDate') {
                return originalBirthDate;
            } else if (inputId === 'registrationDate') {
                return originalRegistrationDate;
            }
            return '';
        }

        // حفظ القيم الأصلية للتواريخ
        function saveOriginalDateValue(inputId, value) {
            if (inputId === 'birthDate') {
                originalBirthDate = value;
            } else if (inputId === 'registrationDate') {
                originalRegistrationDate = value;
            }
        }

        // عرض التاريخ بالحروف لتاريخ التسجيل في نفس الحقل
        function showRegistrationDateInWords() {
            const registrationDateInput = document.getElementById('registrationDate');
            const registrationDate = registrationDateInput.value;
            const registrationDateWordsOverlay = document.getElementById('registrationDateWordsOverlay');
            const registrationDateWordsHidden = document.getElementById('registrationDateWords');

            if (!registrationDate) {
                registrationDateWordsOverlay.style.display = 'none';
                registrationDateWordsOverlay.textContent = '';
                registrationDateWordsHidden.value = '';
                // إعادة عرض التاريخ الرقمي
                registrationDateInput.type = 'date';
                return;
            }

            // حفظ القيمة الأصلية للتاريخ
            saveOriginalDateValue('registrationDate', registrationDate);

            try {
                const dateInWords = dateToWords.gregorianDateToWords(registrationDate);
                if (dateInWords) {
                    // حفظ التاريخ بالحروف في الحقل المخفي
                    registrationDateWordsHidden.value = dateInWords;

                    // تحويل حقل التاريخ إلى نص وعرض التاريخ بالحروف
                    registrationDateInput.type = 'text';
                    registrationDateInput.value = dateInWords;
                    registrationDateInput.style.background = '#f8fff8';
                    registrationDateInput.style.color = '#155724';
                    registrationDateInput.style.fontWeight = '500';
                    registrationDateInput.style.textAlign = 'center';

                    // إضافة تأثير بصري جميل
                    registrationDateInput.style.transform = 'scale(0.98)';
                    registrationDateInput.style.transition = 'all 0.3s ease';
                    setTimeout(() => {
                        registrationDateInput.style.transform = 'scale(1)';
                    }, 100);

                    // إخفاء الطبقة المتراكبة لأننا نعرض في نفس الحقل
                    registrationDateWordsOverlay.style.display = 'none';

                    console.log('📝 تم تحويل تاريخ التسجيل إلى كلمات وعرضه في نفس الحقل:', dateInWords);
                } else {
                    registrationDateWordsOverlay.style.display = 'none';
                    registrationDateWordsOverlay.textContent = '';
                    registrationDateWordsHidden.value = '';
                    console.warn('⚠️ فشل في تحويل تاريخ التسجيل إلى كلمات');
                }
            } catch (error) {
                console.error('❌ خطأ في عرض تاريخ التسجيل بالحروف:', error);
                registrationDateWordsOverlay.style.display = 'none';
                registrationDateWordsOverlay.textContent = '';
                registrationDateWordsHidden.value = '';
            }
        }

        // Check if act number is unique
        async function checkActNumberUniqueness(actNumber, excludeId = null) {
            try {
                const citizens = await citizensDB.getAllCitizens();
                const duplicate = citizens.find(citizen =>
                    citizen.actNumber === actNumber && citizen.id !== excludeId
                );
                return !duplicate; // true if unique, false if duplicate
            } catch (error) {
                console.error('خطأ في فحص فرادة رقم العقد:', error);
                return true; // افتراض الفرادة في حالة الخطأ
            }
        }

        // Handle form submission
        document.getElementById('citizenForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.style.display = 'block';

            try {
                const formData = new FormData(e.target);
                const actNumber = formData.get('actNumber');

                // التحقق من فرادة رقم العقد
                const isUnique = await checkActNumberUniqueness(actNumber, editingId);
                if (!isUnique) {
                    showAlert('❌ رقم العقد موجود مسبقاً! يرجى استخدام رقم آخر.', 'error');

                    // إذا كان في الوضع التلقائي، قم بتوليد رقم جديد
                    if (document.querySelector('input[name="actNumberMode"]:checked').value === 'auto') {
                        await generateActNumber();
                        showAlert('🔄 تم توليد رقم عقد جديد تلقائياً.', 'info');
                    }
                    loadingIndicator.style.display = 'none';
                    return;
                }

                // Generate unique ID for new citizens
                const citizenId = editingId || `citizen_${Date.now()}_${Math.floor(Math.random() * 100000)}_${performance.now().toString().replace('.', '')}`;

                const citizenData = {
                    id: citizenId, // إضافة ID مطلوب لقاعدة البيانات
                    firstNameAr: formData.get('firstNameAr'),
                    firstNameFr: formData.get('firstNameFr'),
                    familyNameAr: formData.get('familyNameAr'),
                    familyNameFr: formData.get('familyNameFr'),
                    birthPlaceAr: formData.get('birthPlaceAr'),
                    birthPlaceFr: formData.get('birthPlaceFr'),
                    birthDate: formData.get('birthDate'),
                    birthDateWords: formData.get('birthDateWords'), // التاريخ بالحروف
                    hijriDate: formData.get('hijriDate'),
                    hijriMode: document.querySelector('input[name="hijriMode"]:checked').value,
                    gender: formData.get('gender'),
                    fatherNameAr: formData.get('fatherNameAr'),
                    fatherNameFr: formData.get('fatherNameFr'),
                    motherNameAr: formData.get('motherNameAr'),
                    motherNameFr: formData.get('motherNameFr'),
                    actNumber: formData.get('actNumber'),
                    registrationDate: formData.get('registrationDate'),
                    registrationDateWords: formData.get('registrationDateWords'), // التاريخ بالحروف
                    createdAt: new Date().toISOString(),
                    timestamp: Date.now() // إضافة timestamp للتوافق
                };

                // Add certificate image if available
                if (currentCertificateImage) {
                    citizenData.certificateImage = currentCertificateImage;
                    console.log('📷 إضافة بيانات الصورة للحفظ:', {
                        fileName: currentCertificateImage.fileName,
                        hasImage: currentCertificateImage.hasImage,
                        dataLength: currentCertificateImage.data ? currentCertificateImage.data.length : 0
                    });
                } else {
                    console.log('📷 لا توجد صورة لإضافتها');
                }

                // الآن دائماً "حفظ وإضافة جديد" بما أن هذا هو الزر الوحيد
                const isSaveAndNew = e.target.dataset.saveAndNew === 'true' || !editingId; // دائماً true للسجلات الجديدة

                if (editingId) {
                    // Update existing citizen - ID already set above
                    await citizensDB.updateCitizen(citizenData);
                    showAlert('✅ تم تحديث بيانات المواطن بنجاح', 'success');
                    editingId = null;
                } else {
                    // Add new citizen - دائماً مع إعداد للتسجيل التالي
                    console.log('إضافة مواطن جديد بـ ID:', citizenData.id);
                    console.log('بيانات المواطن:', citizenData);

                    // التحقق من وجود ID
                    if (!citizenData.id) {
                        throw new Error('لم يتم توليد ID للمواطن');
                    }

                    await citizensDB.addCitizen(citizenData);
                    showAlert('✅ تم حفظ البيانات بنجاح - جاهز لإدخال مواطن جديد', 'success');
                }

                // Update statistics
                await updateStatistics();

                // دائماً مسح النموذج وإعداده للتسجيل التالي
                clearForm();

                // Remove the flag if exists
                if (e.target.dataset.saveAndNew) {
                    delete e.target.dataset.saveAndNew;
                }

                // Generate new act number for next entry
                if (document.querySelector('input[name="actNumberMode"]:checked').value === 'auto') {
                    generateActNumber();
                }

            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('❌ خطأ في حفظ البيانات: ' + error.message, 'error');
            } finally {
                loadingIndicator.style.display = 'none';
            }
        });

        // Save and add new function (الوظيفة الافتراضية الآن)
        async function saveAndNew() {
            // Trigger form submission with save and new flag
            const form = document.getElementById('citizenForm');
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });

            // Add flag to indicate this is save and new
            form.dataset.saveAndNew = 'true';
            form.dispatchEvent(submitEvent);
        }

        // Clear form
        function clearForm() {
            document.getElementById('citizenForm').reset();
            document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];
            removeImage();
            editingId = null;

            // مسح طبقات التاريخ بالحروف والحقول المخفية
            const birthDateWordsOverlay = document.getElementById('birthDateWordsOverlay');
            const registrationDateWordsOverlay = document.getElementById('registrationDateWordsOverlay');
            const birthDateWordsHidden = document.getElementById('birthDateWords');
            const registrationDateWordsHidden = document.getElementById('registrationDateWords');

            if (birthDateWordsOverlay) {
                birthDateWordsOverlay.textContent = '';
                birthDateWordsOverlay.style.display = 'none';
            }

            if (registrationDateWordsOverlay) {
                registrationDateWordsOverlay.textContent = '';
                registrationDateWordsOverlay.style.display = 'none';
            }

            if (birthDateWordsHidden) {
                birthDateWordsHidden.value = '';
            }

            if (registrationDateWordsHidden) {
                registrationDateWordsHidden.value = '';
            }

            // Reset act number mode to auto
            document.querySelector('input[name="actNumberMode"][value="auto"]').checked = true;
            toggleActNumberMode();

            // عرض تاريخ اليوم بالحروف لحقل التسجيل
            setTimeout(() => {
                showRegistrationDateInWords();
            }, 100);

            // Focus on first field
            document.getElementById('firstNameAr').focus();
        }

        // Show alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // Check for edit parameters
        function checkForEditParameters() {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('edit') === 'true') {
                const citizenId = urlParams.get('id'); // استخدام النص كما هو بدلاً من parseInt

                // Fill form with citizen data
                console.log('🏠 فحص بيانات مكان الازدياد المستقبلة:');
                console.log('birthPlaceAr من URL:', urlParams.get('birthPlaceAr'));
                console.log('birthPlace من URL:', urlParams.get('birthPlace'));
                console.log('📷 فحص بيانات الصورة المستقبلة:');
                console.log('hasCertificate من URL:', urlParams.get('hasCertificate'));
                console.log('certificateFileName من URL:', urlParams.get('certificateFileName'));
                console.log('certificateImageData موجود:', urlParams.get('certificateImageData') ? 'نعم' : 'لا');
                console.log('hasImageInStorage من URL:', urlParams.get('hasImageInStorage'));
                console.log('جميع المعاملات:', Object.fromEntries(urlParams.entries()));

                document.getElementById('firstNameAr').value = urlParams.get('firstNameAr') || '';
                document.getElementById('firstNameFr').value = urlParams.get('firstNameFr') || '';
                document.getElementById('familyNameAr').value = urlParams.get('familyNameAr') || '';
                document.getElementById('familyNameFr').value = urlParams.get('familyNameFr') || '';

                // معالجة مكان الازدياد بالعربي مع تسجيل مفصل - فحص جميع الاحتمالات
                const birthPlaceArFromUrl = urlParams.get('birthPlaceAr');
                const birthPlaceFromUrl = urlParams.get('birthPlace');
                const birthPlaceArabicFromUrl = urlParams.get('birthPlaceArabic');
                const placeOfBirthArFromUrl = urlParams.get('placeOfBirthAr');

                // اختيار أول قيمة متاحة
                const birthPlaceArValue = birthPlaceArFromUrl || birthPlaceFromUrl || birthPlaceArabicFromUrl || placeOfBirthArFromUrl || '';

                console.log('🏠 قيم مكان الازدياد المستقبلة:');
                console.log('  - birthPlaceAr:', birthPlaceArFromUrl);
                console.log('  - birthPlace:', birthPlaceFromUrl);
                console.log('  - birthPlaceArabic:', birthPlaceArabicFromUrl);
                console.log('  - placeOfBirthAr:', placeOfBirthArFromUrl);
                console.log('  - القيمة النهائية:', birthPlaceArValue);

                const birthPlaceElement = document.getElementById('birthPlaceAr');
                if (birthPlaceElement) {
                    birthPlaceElement.value = birthPlaceArValue;
                    console.log('✅ تم تعيين مكان الازدياد:', birthPlaceArValue);

                    // تأكيد إضافي من أن القيمة تم تعيينها
                    setTimeout(() => {
                        console.log('🔍 فحص نهائي - قيمة الحقل:', birthPlaceElement.value);
                    }, 100);
                } else {
                    console.error('❌ لم يتم العثور على عنصر birthPlaceAr في الصفحة');
                }

                // معالجة مكان الازدياد بالفرنسي
                const birthPlaceFrValue = urlParams.get('birthPlaceFr') || urlParams.get('birthPlaceFrench') || urlParams.get('placeOfBirthFr') || '';
                document.getElementById('birthPlaceFr').value = birthPlaceFrValue;
                console.log('🏠 مكان الازدياد بالفرنسي:', birthPlaceFrValue);
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('birthDateWords').value = urlParams.get('birthDateWords') || '';
                document.getElementById('hijriDate').value = urlParams.get('hijriDate') || '';

                // تعيين وضع التاريخ الهجري
                const hijriMode = urlParams.get('hijriMode') || 'auto';
                document.querySelector(`input[name="hijriMode"][value="${hijriMode}"]`).checked = true;
                toggleHijriMode();

                // إذا كان الوضع تلقائي وهناك تاريخ ميلادي، قم بالتحويل
                const birthDate = urlParams.get('birthDate');
                if (hijriMode === 'auto' && birthDate) {
                    setTimeout(() => convertToHijri(), 100);
                }

                // عرض تاريخ الازدياد بالحروف إذا كان موجود
                if (birthDate) {
                    setTimeout(() => showDateInWords(), 150);
                }
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').value = urlParams.get('fatherNameAr') || '';
                document.getElementById('fatherNameFr').value = urlParams.get('fatherNameFr') || '';
                document.getElementById('motherNameAr').value = urlParams.get('motherNameAr') || '';
                document.getElementById('motherNameFr').value = urlParams.get('motherNameFr') || '';

                // تعيين رقم العقد والتبديل للوضع اليدوي
                const actNumber = urlParams.get('actNumber') || '';
                document.getElementById('actNumber').value = actNumber;

                // تعيين الوضع اليدوي لرقم العقد لمنع التوليد التلقائي
                if (actNumber) {
                    document.querySelector('input[name="actNumberMode"][value="manual"]').checked = true;
                    toggleActNumberMode();
                    console.log('✅ تم تعيين رقم العقد للوضع اليدوي:', actNumber);
                }

                document.getElementById('registrationDate').value = urlParams.get('registrationDate') || '';
                document.getElementById('registrationDateWords').value = urlParams.get('registrationDateWords') || '';

                // عرض تاريخ التسجيل بالحروف إذا كان موجود
                const registrationDate = urlParams.get('registrationDate');
                if (registrationDate) {
                    setTimeout(() => showRegistrationDateInWords(), 200);
                }

                // Set editing mode
                editingId = citizenId;

                // Update form title
                document.querySelector('.form-section h2').innerHTML = '✏️ تعديل بيانات المواطن';

                console.log('✅ تم تحميل بيانات المواطن للتعديل:', citizenId);
                console.log('📋 رقم العقد المحمل:', actNumber);

                // معالجة عرض الصورة إذا كانت موجودة
                const hasCertificate = urlParams.get('hasCertificate') === 'true';
                const hasImageInStorage = urlParams.get('hasImageInStorage') === 'true';
                const certificateImageData = urlParams.get('certificateImageData');
                const certificateFileName = urlParams.get('certificateFileName') || 'certificate.jpg';

                let imageDataToDisplay = null;

                // محاولة الحصول على بيانات الصورة من مصادر مختلفة
                console.log('📷 فحص مصادر بيانات الصورة:');
                console.log('  - hasImageInStorage:', hasImageInStorage);
                console.log('  - citizenId:', citizenId);
                console.log('  - certificateImageData موجود:', !!certificateImageData);

                if (hasImageInStorage && citizenId) {
                    // محاولة الحصول على البيانات من sessionStorage
                    const storageKey = 'editImageData_' + citizenId;
                    console.log('📷 البحث عن البيانات بالمفتاح:', storageKey);
                    imageDataToDisplay = sessionStorage.getItem(storageKey);
                    if (imageDataToDisplay) {
                        console.log('📷 تم العثور على بيانات الصورة في sessionStorage، الحجم:', imageDataToDisplay.length);
                        // حذف البيانات من sessionStorage بعد الاستخدام
                        sessionStorage.removeItem(storageKey);
                    } else {
                        console.log('📷 لم يتم العثور على بيانات في sessionStorage');
                        // فحص جميع مفاتيح sessionStorage للتشخيص
                        console.log('📷 جميع مفاتيح sessionStorage:', Object.keys(sessionStorage));
                    }
                } else if (certificateImageData) {
                    // استخدام البيانات من URL مباشرة
                    imageDataToDisplay = certificateImageData;
                    console.log('📷 تم العثور على بيانات الصورة في URL، الحجم:', certificateImageData.length);
                } else if (hasCertificate) {
                    // محاولة أخيرة: البحث في جميع مفاتيح sessionStorage
                    console.log('📷 محاولة البحث في جميع مفاتيح sessionStorage...');
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        if (key && key.includes('editImageData_')) {
                            console.log('📷 تم العثور على مفتاح محتمل:', key);
                            const data = sessionStorage.getItem(key);
                            if (data && data.startsWith('data:image')) {
                                imageDataToDisplay = data;
                                sessionStorage.removeItem(key);
                                console.log('📷 تم استرجاع البيانات من المفتاح:', key);
                                break;
                            }
                        }
                    }
                }

                if (hasCertificate && imageDataToDisplay) {
                    console.log('📷 تم العثور على صورة شهادة، جاري عرضها...');

                    // إعداد بيانات الصورة
                    currentCertificateImage = {
                        data: imageDataToDisplay,
                        fileName: certificateFileName,
                        hasImage: true,
                        uploadDate: new Date().toISOString()
                    };

                    // عرض الصورة
                    displayImage(imageDataToDisplay);
                    console.log('✅ تم عرض صورة الشهادة بنجاح');
                } else if (hasCertificate && !imageDataToDisplay) {
                    console.warn('⚠️ تم تحديد وجود شهادة لكن لم يتم العثور على بيانات الصورة');
                    console.log('📷 محاولة جلب الصورة مباشرة من قاعدة البيانات...');

                    // محاولة جلب الصورة مباشرة من قاعدة البيانات
                    loadImageFromDatabase(citizenId, certificateFileName);
                } else {
                    console.log('📷 لا توجد صورة شهادة لعرضها');
                }

                // Clear URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

        // Load image directly from database as fallback
        async function loadImageFromDatabase(citizenId, fileName) {
            try {
                console.log('📷 جلب الصورة من قاعدة البيانات للمواطن:', citizenId);

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await citizensDB.init();
                }

                // جلب بيانات المواطن مع الصورة
                const citizen = await citizensDB.getCitizen(citizenId, true);

                if (citizen && citizen.certificateImage && citizen.certificateImage.data) {
                    console.log('📷 تم العثور على الصورة في قاعدة البيانات');

                    // إعداد بيانات الصورة
                    currentCertificateImage = {
                        data: citizen.certificateImage.data,
                        fileName: fileName || citizen.certificateImage.fileName || 'certificate.jpg',
                        hasImage: true,
                        uploadDate: citizen.certificateImage.uploadDate || new Date().toISOString()
                    };

                    // عرض الصورة
                    displayImage(citizen.certificateImage.data);
                    console.log('✅ تم عرض الصورة من قاعدة البيانات بنجاح');
                } else {
                    console.log('📷 لم يتم العثور على صورة في قاعدة البيانات');
                }

            } catch (error) {
                console.error('❌ خطأ في جلب الصورة من قاعدة البيانات:', error);
            }
        }

        // Image handling functions
        let currentZoom = 1;
        let currentRotation = 0;
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;
        let imageNaturalWidth = 0;
        let imageNaturalHeight = 0;
        let originalDisplayWidth = 0;
        let originalDisplayHeight = 0;
        let originalContainerWidth = 0;
        let originalContainerHeight = 0;

        function adjustImageSize(image, container) {
            console.log('📏 بدء ضبط حجم الصورة باستخدام CSS المرن');

            // إزالة أي أبعاد ثابتة مطبقة من JavaScript
            image.style.width = '';
            image.style.height = '';
            image.style.maxWidth = '';
            image.style.maxHeight = '';

            // الاعتماد على CSS للتحكم في الأبعاد
            // CSS سيطبق: width: 100%, height: 100%, object-fit: contain

            // ضبط الحاوية لتتناسب مع منطقة التحميل
            const uploadArea = document.getElementById('uploadArea');
            if (uploadArea) {
                const uploadAreaRect = uploadArea.getBoundingClientRect();

                // إزالة أي أبعاد ثابتة من الحاوية أيضاً
                container.style.width = '';
                container.style.height = '';

                console.log('📏 أبعاد منطقة التحميل:', uploadAreaRect.width + 'x' + uploadAreaRect.height);
                console.log('✅ تم الاعتماد على CSS للتحكم في أبعاد الصورة والحاوية');
            }

            // حفظ الأبعاد الطبيعية للصورة فقط (للتكبير)
            originalDisplayWidth = imageNaturalWidth;
            originalDisplayHeight = imageNaturalHeight;

            console.log('💾 تم حفظ الأبعاد الطبيعية للصورة:', {
                naturalWidth: imageNaturalWidth,
                naturalHeight: imageNaturalHeight
            });

            // إزالة فئة التكبير
            image.classList.remove('zoomed');
        }

        function restoreOriginalSize() {
            const image = document.getElementById('uploadedImage');
            const container = document.getElementById('imageContainer');

            if (image && container) {
                console.log('🔄 استعادة الحجم الأصلي باستخدام CSS');

                // إزالة أي أبعاد ثابتة وترك CSS يتحكم
                image.style.width = '';
                image.style.height = '';
                image.style.maxWidth = '';
                image.style.maxHeight = '';

                container.style.width = '';
                container.style.height = '';

                // إعادة تعيين التحويل
                currentZoom = 1;
                image.style.transform = `scale(1) rotate(${currentRotation}deg)`;
                image.style.transformOrigin = 'center center';

                updateImageTransform();
                return true;
            }
            return false;
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = e.target.result;

                // Store image data
                currentCertificateImage = {
                    data: imageData,
                    fileName: file.name,
                    uploadDate: new Date().toISOString(),
                    hasImage: true
                };

                // Display image
                displayImage(imageData);
            };
            reader.readAsDataURL(file);
        }

        function displayImage(imageSrc) {
            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const uploadedImage = document.getElementById('uploadedImage');
            const imageContainer = document.getElementById('imageContainer');
            const uploadArea = document.getElementById('uploadArea');

            uploadContent.style.display = 'none';
            imageDisplay.style.display = 'block';
            imageControls.style.display = 'block';
            uploadedImage.src = imageSrc;

            // إضافة كلاس has-image لتحسين المظهر
            if (uploadArea) {
                uploadArea.classList.add('has-image');
            }

            // Reset transformations
            currentZoom = 1;
            currentRotation = 0;
            uploadedImage.style.transform = 'scale(1) rotate(0deg)';
            uploadedImage.style.transformOrigin = 'center center';

            // Get image natural dimensions when loaded
            uploadedImage.onload = function() {
                imageNaturalWidth = this.naturalWidth;
                imageNaturalHeight = this.naturalHeight;

                console.log('📐 أبعاد الصورة الأصلية:', imageNaturalWidth + 'x' + imageNaturalHeight);

                // انتظار قصير للتأكد من تطبيق CSS
                setTimeout(() => {
                    // تطبيق حجم مناسب للصورة
                    adjustImageSize(this, imageContainer);
                }, 100);
            };

            // Initialize mouse and keyboard controls
            initializeImageControls(uploadedImage, imageContainer);
        }

        function initializeImageControls(image, container) {
            // Remove existing event listeners
            const newImage = image.cloneNode(true);
            image.parentNode.replaceChild(newImage, image);

            // Add enhanced click to zoom
            newImage.addEventListener('click', function(e) {
                e.preventDefault();

                if (currentZoom === 1) {
                    console.log('🖱️ نقر للتكبير');
                    zoomIn();
                } else {
                    console.log('🖱️ نقر للتصغير');
                    resetZoom();
                }
            });

            // Enhanced wheel zoom with smooth scaling
            newImage.addEventListener('wheel', function(e) {
                e.preventDefault();

                if (e.deltaY < 0) {
                    zoomIn();
                } else {
                    zoomOut();
                }
            });

            // تم إزالة وظائف السحب والإفلات لأن الصورة محصورة داخل الحدود

            // Add keyboard controls with POIUY layout
            document.addEventListener('keydown', handleKeyboardControls);
        }

        function zoomIn(event) {
            if (event) event.preventDefault();

            // التكبير باستخدام transform scale فقط
            const previousZoom = currentZoom;
            currentZoom = Math.min(currentZoom * 1.2, 5);

            // إذا وصلنا للحد الأقصى، أعلم المستخدم
            if (currentZoom === 5 && previousZoom < 5) {
                console.log('⚠️ وصلت للحد الأقصى للتكبير (500%)');
            }

            updateImageTransform();
            console.log('🔍 تكبير إلى:', Math.round(currentZoom * 100) + '% (محصور داخل الحدود)');
        }

        function zoomOut(event) {
            if (event) event.preventDefault();

            const previousZoom = currentZoom;
            currentZoom = Math.max(currentZoom / 1.2, 0.2);

            // إذا وصلنا للحد الأدنى، أعلم المستخدم
            if (currentZoom === 0.2 && previousZoom > 0.2) {
                console.log('⚠️ وصلت للحد الأدنى للتصغير (20%)');
            }

            updateImageTransform();
            console.log('🔍 تصغير إلى:', Math.round(currentZoom * 100) + '%');
        }

        function resetZoom(event) {
            if (event) event.preventDefault();
            currentZoom = 1;
            const image = document.getElementById('uploadedImage');
            const container = document.getElementById('imageContainer');

            if (image && container) {
                // إعادة نقطة الأصل للمنتصف عند إعادة التعيين
                image.style.transformOrigin = 'center center';

                // إزالة أي أبعاد ثابتة وترك CSS يتحكم
                console.log('🔄 إعادة تعيين التكبير باستخدام CSS');

                image.style.width = '';
                image.style.height = '';
                image.style.maxWidth = '';
                image.style.maxHeight = '';

                container.style.width = '';
                container.style.height = '';

                console.log('🔄 تم إعادة تعيين التكبير للمنتصف');
            }

            updateImageTransform();
        }

        function rotateLeft(event) {
            if (event) event.preventDefault();
            currentRotation -= 90;
            if (currentRotation < 0) currentRotation += 360;
            updateImageTransform();
        }

        function rotateRight(event) {
            if (event) event.preventDefault();
            currentRotation += 90;
            if (currentRotation >= 360) currentRotation -= 360;
            updateImageTransform();
        }

        function resetRotation(event) {
            if (event) event.preventDefault();
            currentRotation = 0;
            updateImageTransform();
        }

        function updateImageTransform() {
            const image = document.getElementById('uploadedImage');
            const container = document.getElementById('imageContainer');

            if (image) {
                // تطبيق التحويل مع نقطة أصل ثابتة في المنتصف
                image.style.transform = `scale(${currentZoom}) rotate(${currentRotation}deg)`;
                image.style.transformOrigin = 'center center';

                // إضافة أو إزالة فئة التكبير
                if (currentZoom > 1) {
                    image.classList.add('zoomed');
                    // عند التكبير، نبقي الصورة داخل حدود الحاوية
                    if (container) {
                        container.style.overflow = 'hidden'; // منع التمرير
                        container.style.cursor = 'zoom-out';

                        // التأكد من أن الصورة تبقى داخل الحدود
                        image.style.width = '100%';
                        image.style.height = '100%';
                        image.style.maxWidth = '100%';
                        image.style.maxHeight = '100%';

                        // إظهار أزرار التحكم في الاتجاهات
                        const directionControls = document.getElementById('directionControls');
                        if (directionControls) {
                            directionControls.style.display = 'block';
                        }

                        console.log('🔒 الصورة محصورة داخل الحدود - التكبير:', Math.round(currentZoom * 100) + '%');
                    }
                } else {
                    image.classList.remove('zoomed');
                    // عند الحجم الطبيعي، نخفي التمرير
                    if (container) {
                        container.style.overflow = 'hidden';
                        container.style.cursor = 'zoom-in';
                    }

                    // إخفاء أزرار التحكم في الاتجاهات
                    const directionControls = document.getElementById('directionControls');
                    if (directionControls) {
                        directionControls.style.display = 'none';
                    }

                    // التأكد من أن CSS يتحكم في الأبعاد
                    if (currentZoom === 1) {
                        image.style.width = '';
                        image.style.height = '';
                        image.style.maxWidth = '';
                        image.style.maxHeight = '';
                        image.style.transformOrigin = 'center center';
                        console.log('✅ الحجم الطبيعي - CSS يتحكم في الأبعاد');
                    }
                }

                console.log('🔄 تحديث التحويل: تكبير=' + Math.round(currentZoom * 100) + '% دوران=' + currentRotation + '°');
            }
        }

        function handleKeyboardControls(event) {
            // Only handle keyboard controls when image is displayed
            const imageDisplay = document.getElementById('imageDisplay');
            if (!imageDisplay || imageDisplay.style.display === 'none') return;

            // Don't handle keys when user is typing in input fields
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') return;

            // POIUY keyboard layout mapping
            const key = event.key.toLowerCase();

            // Prevent default for our handled keys
            const handledKeys = ['+', '-', '0', 'p', 'o', 'i', 'u', 'y', 'delete', 'arrowup', 'arrowdown', 'arrowleft', 'arrowright'];
            if (handledKeys.includes(key)) {
                event.preventDefault();
            }

            switch(key) {
                case '+':
                case '=':
                    zoomIn();
                    console.log('🔍 تكبير بالكيبورد');
                    break;
                case '-':
                    zoomOut();
                    console.log('🔍 تصغير بالكيبورد');
                    break;
                case '0':
                    resetZoom();
                    console.log('🔄 إعادة تعيين التكبير بالكيبورد');
                    break;
                case 'p': // بدلاً من Q - دوران يسار
                    rotateLeft();
                    console.log('↺ دوران يسار بالكيبورد');
                    break;
                case 'o': // بدلاً من E - دوران يمين
                    rotateRight();
                    console.log('↻ دوران يمين بالكيبورد');
                    break;
                case 'i': // بدلاً من R - إعادة تعيين الدوران
                    resetRotation();
                    console.log('🔄 إعادة تعيين الدوران بالكيبورد');
                    break;
                case 'u': // بدلاً من F - ملء الشاشة
                    fitToScreen();
                    console.log('📐 ملء الشاشة بالكيبورد');
                    break;
                case 'y': // بدلاً من D - تحميل الصورة
                    downloadImage();
                    console.log('💾 تحميل الصورة بالكيبورد');
                    break;
                case 'delete':
                    if (confirm('هل أنت متأكد من حذف الصورة؟')) {
                        removeImage();
                    }
                    break;
                case 'arrowup':
                    moveImage(0, -20);
                    break;
                case 'arrowdown':
                    moveImage(0, 20);
                    break;
                case 'arrowleft':
                    moveImage(-20, 0);
                    break;
                case 'arrowright':
                    moveImage(20, 0);
                    break;
                case 't': // اختبار التمرير العمودي (معطل)
                case 'f': // إجبار التمدد العمودي (معطل)
                    console.log('⚠️ هذه الوظيفة معطلة - الصورة محصورة داخل الحدود');
                    break;
                case 'g': // عرض نقطة الأصل الحالية
                    showTransformOrigin();
                    break;
            }
        }

        function moveImage(deltaX, deltaY) {
            const image = document.getElementById('uploadedImage');
            const container = document.getElementById('imageContainer');

            if (image && container && currentZoom > 1) {
                // حساب الموقع الحالي للصورة
                const currentTransform = image.style.transform || '';
                const scaleMatch = currentTransform.match(/scale\(([^)]+)\)/);
                const rotateMatch = currentTransform.match(/rotate\(([^)]+)\)/);
                const translateMatch = currentTransform.match(/translate\(([^,]+),\s*([^)]+)\)/);

                let currentX = 0;
                let currentY = 0;

                if (translateMatch) {
                    currentX = parseFloat(translateMatch[1]) || 0;
                    currentY = parseFloat(translateMatch[2]) || 0;
                }

                // حساب الموقع الجديد
                const newX = currentX + deltaX;
                const newY = currentY + deltaY;

                // حساب الحدود المسموحة للحركة
                const imageRect = image.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();

                // حساب المساحة الإضافية بسبب التكبير
                const scaledWidth = imageRect.width;
                const scaledHeight = imageRect.height;
                const maxMoveX = Math.max(0, (scaledWidth - containerRect.width) / 2);
                const maxMoveY = Math.max(0, (scaledHeight - containerRect.height) / 2);

                // تقييد الحركة داخل الحدود المسموحة
                const constrainedX = Math.max(-maxMoveX, Math.min(maxMoveX, newX));
                const constrainedY = Math.max(-maxMoveY, Math.min(maxMoveY, newY));

                // تطبيق التحويل الجديد
                const scaleValue = scaleMatch ? scaleMatch[1] : currentZoom;
                const rotateValue = rotateMatch ? rotateMatch[1] : currentRotation + 'deg';

                image.style.transform = `scale(${scaleValue}) rotate(${rotateValue}) translate(${constrainedX}px, ${constrainedY}px)`;

                console.log('🎮 تحريك الصورة:', {
                    deltaX: deltaX,
                    deltaY: deltaY,
                    newX: constrainedX,
                    newY: constrainedY,
                    maxMoveX: maxMoveX,
                    maxMoveY: maxMoveY,
                    zoom: currentZoom
                });
            } else if (currentZoom <= 1) {
                console.log('⚠️ التحريك متاح فقط عند التكبير');
            }
        }

        function testVerticalScroll() {
            const container = document.getElementById('imageContainer');
            const image = document.getElementById('uploadedImage');

            if (container && currentZoom > 1) {
                console.log('🧪 اختبار التمرير العمودي:');
                console.log('📊 قبل الاختبار:', {
                    scrollTop: container.scrollTop,
                    scrollHeight: container.scrollHeight,
                    clientHeight: container.clientHeight,
                    maxScroll: container.scrollHeight - container.clientHeight,
                    imageHeight: image.offsetHeight,
                    containerHeight: container.clientHeight
                });

                // إجبار الصورة على أن تكون أطول من الحاوية
                if (image.offsetHeight <= container.clientHeight) {
                    console.log('⚠️ الصورة ليست أطول من الحاوية - إجبار التمديد');
                    image.style.minHeight = (container.clientHeight * 1.5) + 'px';
                }

                // محاولة التمرير لأعلى
                container.scrollTop = Math.max(0, container.scrollTop - 50);

                setTimeout(() => {
                    console.log('📊 بعد الاختبار:', {
                        scrollTop: container.scrollTop,
                        scrollHeight: container.scrollHeight,
                        clientHeight: container.clientHeight,
                        imageHeight: image.offsetHeight
                    });
                }, 100);
            }
        }

        function forceVerticalExpansion() {
            const container = document.getElementById('imageContainer');
            const image = document.getElementById('uploadedImage');

            if (container && image && currentZoom > 1) {
                console.log('🔧 إجبار التمدد العمودي للصورة');

                // إجبار الصورة على التمدد عمودياً
                const scaledHeight = imageNaturalHeight * currentZoom;
                const scaledWidth = imageNaturalWidth * currentZoom;

                console.log('📐 الأبعاد المحسوبة:', {
                    naturalWidth: imageNaturalWidth,
                    naturalHeight: imageNaturalHeight,
                    scaledWidth: scaledWidth,
                    scaledHeight: scaledHeight,
                    containerWidth: container.clientWidth,
                    containerHeight: container.clientHeight,
                    zoom: currentZoom
                });

                // تطبيق الأبعاد المحسوبة
                image.style.width = scaledWidth + 'px';
                image.style.height = scaledHeight + 'px';
                image.style.minWidth = 'auto';
                image.style.minHeight = 'auto';
                image.style.maxWidth = 'none';
                image.style.maxHeight = 'none';

                setTimeout(() => {
                    console.log('📊 بعد التمدد:', {
                        imageWidth: image.offsetWidth,
                        imageHeight: image.offsetHeight,
                        scrollWidth: container.scrollWidth,
                        scrollHeight: container.scrollHeight,
                        canScrollVertically: container.scrollHeight > container.clientHeight
                    });
                }, 100);
            }
        }

        function showTransformOrigin() {
            const image = document.getElementById('uploadedImage');
            if (image) {
                const transformOrigin = image.style.transformOrigin || 'center center';
                console.log('🎯 نقطة الأصل الحالية:', transformOrigin);
                console.log('🔍 مستوى التكبير:', Math.round(currentZoom * 100) + '%');
                console.log('🔄 زاوية الدوران:', currentRotation + '°');
            }
        }

        function resetImagePosition() {
            const image = document.getElementById('uploadedImage');
            if (image && currentZoom > 1) {
                // إعادة تعيين موقع الصورة للمنتصف مع الحفاظ على التكبير والدوران
                const scaleValue = currentZoom;
                const rotateValue = currentRotation;

                image.style.transform = `scale(${scaleValue}) rotate(${rotateValue}deg)`;
                image.style.transformOrigin = 'center center';

                console.log('🎯 تم إعادة تعيين موقع الصورة للمنتصف');
                console.log('🔍 التكبير الحالي:', Math.round(currentZoom * 100) + '%');
            } else {
                console.log('⚠️ إعادة تعيين الموقع متاحة فقط عند التكبير');
            }
        }

        function fitToScreen(event) {
            if (event) event.preventDefault();
            const container = document.getElementById('imageContainer');
            const image = document.getElementById('uploadedImage');

            if (container && image && imageNaturalWidth && imageNaturalHeight) {
                // إعادة ضبط الصورة باستخدام CSS
                console.log('📐 إعادة ضبط الصورة لملء الحاوية باستخدام CSS');

                image.style.width = '';
                image.style.height = '';
                image.style.maxWidth = '';
                image.style.maxHeight = '';

                container.style.width = '';
                container.style.height = '';

                // إعادة تعيين التكبير
                currentZoom = 1;
                image.style.transformOrigin = 'center center';
                updateImageTransform();

                console.log('✅ تم إعادة ضبط الصورة لملء الحاوية');
            }
        }

        function downloadImage(event) {
            if (event) event.preventDefault();

            if (currentCertificateImage && currentCertificateImage.data) {
                const link = document.createElement('a');
                link.href = currentCertificateImage.data;
                link.download = currentCertificateImage.fileName || 'certificate.jpg';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('💾 تم تحميل الصورة:', currentCertificateImage.fileName);
            }
        }







        function removeImage(event) {
            if (event) event.preventDefault();

            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const uploadArea = document.getElementById('uploadArea');

            uploadContent.style.display = 'block';
            imageDisplay.style.display = 'none';
            imageControls.style.display = 'none';

            // إزالة كلاس has-image
            if (uploadArea) {
                uploadArea.classList.remove('has-image');
            }

            // Remove keyboard event listener
            document.removeEventListener('keydown', handleKeyboardControls);

            // Reset values
            currentCertificateImage = null;
            currentZoom = 1;
            currentRotation = 0;
            imageNaturalWidth = 0;
            imageNaturalHeight = 0;
            originalDisplayWidth = 0;
            originalDisplayHeight = 0;
            originalContainerWidth = 0;
            originalContainerHeight = 0;

            document.getElementById('fileInput').value = '';
            console.log('🗑️ تم حذف الصورة وإعادة تعيين القيم');
        }

        // Export all data
        async function exportAllData() {
            try {
                const citizens = await citizensDB.getAllCitizens();

                if (citizens.length === 0) {
                    alert('⚠️ لا توجد بيانات للتصدير');
                    return;
                }

                const currentDate = new Date();
                const dateStr = currentDate.toISOString().split('T')[0];
                const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                const dbInfo = await citizensDB.getDatabaseInfo();

                const backupData = {
                    exportDate: currentDate.toISOString(),
                    totalCitizens: citizens.length,
                    withCertificates: dbInfo ? dbInfo.withCertificates : 0,
                    version: '3.0-IndexedDB',
                    storageInfo: dbInfo,
                    data: citizens
                };

                const dataStr = JSON.stringify(backupData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `citizens_backup_indexeddb_${dateStr}_${timeStr}.json`;
                link.click();
                URL.revokeObjectURL(url);

                showAlert(`✅ تم تصدير النسخة الاحتياطية بنجاح\nعدد المواطنين: ${citizens.length.toLocaleString()}`, 'success');

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showAlert('❌ خطأ في تصدير البيانات', 'error');
            }
        }

        // Import data function
        function importData() {
            document.getElementById('importFileInput').click();
        }

        // Handle import file
        async function handleImportFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const importData = JSON.parse(text);

                // Validate import data
                if (!importData.data || !Array.isArray(importData.data)) {
                    throw new Error('ملف البيانات غير صحيح');
                }

                const confirmMessage = `هل تريد استيراد ${importData.data.length} سجل؟\n\n` +
                                     `⚠️ تحذير: سيتم دمج البيانات مع البيانات الموجودة\n` +
                                     `📅 تاريخ النسخة: ${importData.exportDate || 'غير محدد'}\n` +
                                     `📊 الإصدار: ${importData.version || 'غير محدد'}`;

                if (!confirm(confirmMessage)) return;

                let imported = 0;
                let errors = 0;

                showAlert('🔄 جاري استيراد البيانات...', 'success');

                for (const citizen of importData.data) {
                    try {
                        // Generate new ID if not exists or conflicts
                        if (!citizen.id) {
                            citizen.id = `imported_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
                        }

                        await citizensDB.addCitizen(citizen);
                        imported++;
                    } catch (error) {
                        console.warn('فشل في استيراد السجل:', citizen, error);
                        errors++;
                    }
                }

                await updateStatistics();
                showAlert(`✅ تم استيراد ${imported} سجل بنجاح${errors > 0 ? ` (${errors} أخطاء)` : ''}`, 'success');

            } catch (error) {
                console.error('خطأ في الاستيراد:', error);
                showAlert('❌ خطأ في استيراد البيانات: ' + error.message, 'error');
            }

            // Clear file input
            event.target.value = '';
        }

        // Auto backup
        async function autoBackup() {
            try {
                const citizens = await citizensDB.getAllCitizens();

                if (citizens.length === 0) {
                    showAlert('⚠️ لا توجد بيانات للنسخ الاحتياطي', 'error');
                    return;
                }

                const lastBackup = localStorage.getItem('lastBackupDate');
                const today = new Date().toDateString();

                if (lastBackup === today) {
                    const confirmBackup = confirm('تم عمل نسخة احتياطية اليوم بالفعل.\nهل تريد عمل نسخة احتياطية أخرى؟');
                    if (!confirmBackup) return;
                }

                await exportAllData();
                localStorage.setItem('lastBackupDate', today);

            } catch (error) {
                console.error('خطأ في النسخ الاحتياطي:', error);
                showAlert('❌ خطأ في النسخ الاحتياطي', 'error');
            }
        }

        // Virtual Keyboard Functions
        let currentActiveField = null;
        let keyboardIsDragging = false;
        let keyboardDragOffset = { x: 0, y: 0 };

        // Toggle keyboard visibility
        function toggleKeyboard() {
            const keyboard = document.getElementById('virtualKeyboard');
            if (keyboard.style.display === 'none') {
                showKeyboard();
            } else {
                hideKeyboard();
            }
        }

        // Show keyboard under active field
        function showKeyboard() {
            const keyboard = document.getElementById('virtualKeyboard');
            keyboard.style.display = 'block';

            // Try to load saved position first
            const hasSavedPosition = loadKeyboardPosition();

            if (!hasSavedPosition) {
                // If no saved position, position based on active field or center
                if (currentActiveField) {
                    positionKeyboard(currentActiveField);
                } else {
                    // Position in center if no active field
                    keyboard.style.left = '50%';
                    keyboard.style.top = '50%';
                    keyboard.style.transform = 'translate(-50%, -50%)';
                }
            }

            if (currentActiveField) {
                autoSwitchKeyboard(currentActiveField);
            }
        }

        // Hide keyboard
        function hideKeyboard() {
            const keyboard = document.getElementById('virtualKeyboard');
            keyboard.style.display = 'none';
        }

        // Position keyboard under the active field
        function positionKeyboard(field) {
            const keyboard = document.getElementById('virtualKeyboard');
            const fieldRect = field.getBoundingClientRect();
            const keyboardHeight = 200; // Approximate keyboard height
            const margin = 10;

            // Calculate position
            let left = fieldRect.left;
            let top = fieldRect.bottom + margin;

            // Adjust if keyboard goes off screen
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const keyboardWidth = Math.min(600, viewportWidth * 0.9);

            // Adjust horizontal position
            if (left + keyboardWidth > viewportWidth) {
                left = viewportWidth - keyboardWidth - margin;
            }
            if (left < margin) {
                left = margin;
            }

            // Adjust vertical position
            if (top + keyboardHeight > viewportHeight) {
                // Show above the field if not enough space below
                top = fieldRect.top - keyboardHeight - margin;
                if (top < margin) {
                    // If still not enough space, show at bottom of viewport
                    top = viewportHeight - keyboardHeight - margin;
                }
            }

            // Apply position
            keyboard.style.left = left + 'px';
            keyboard.style.top = top + 'px';
            keyboard.style.transform = 'none';
            keyboard.style.width = keyboardWidth + 'px';

            // Scroll to ensure keyboard is visible
            setTimeout(() => {
                const keyboardRect = keyboard.getBoundingClientRect();
                if (keyboardRect.bottom > viewportHeight || keyboardRect.top < 0) {
                    keyboard.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'nearest'
                    });
                }
            }, 100);
        }

        // Auto switch keyboard based on field
        function autoSwitchKeyboard(field) {
            const fieldId = field.id || field.name || '';
            const isArabicField = fieldId.includes('Ar') ||
                                fieldId.includes('Arabic') ||
                                fieldId.includes('hijri');

            if (isArabicField) {
                switchToArabic();
            } else {
                switchToFrench();
            }
        }

        // Switch to Arabic keyboard
        function switchToArabic() {
            document.getElementById('arabicKeys').style.display = 'block';
            document.getElementById('frenchKeys').style.display = 'none';
            document.getElementById('arabicKeyboard').className = 'btn btn-primary';
            document.getElementById('frenchKeyboard').className = 'btn btn-secondary';
        }

        // Switch to French keyboard
        function switchToFrench() {
            document.getElementById('arabicKeys').style.display = 'none';
            document.getElementById('frenchKeys').style.display = 'block';
            document.getElementById('arabicKeyboard').className = 'btn btn-secondary';
            document.getElementById('frenchKeyboard').className = 'btn btn-primary';
        }

        // Handle key press
        function handleKeyPress(char, action) {
            if (!currentActiveField) return;

            if (action === 'backspace') {
                const currentValue = currentActiveField.value;
                currentActiveField.value = currentValue.slice(0, -1);
            } else if (action === 'clear') {
                currentActiveField.value = '';
            } else {
                currentActiveField.value += char;
            }

            // Trigger input event for any listeners
            currentActiveField.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Drag and Drop Functions
        function initDragAndDrop() {
            const keyboard = document.getElementById('virtualKeyboard');
            const header = document.getElementById('keyboardHeader');

            // Mouse events
            header.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // Touch events for mobile
            header.addEventListener('touchstart', startDragTouch, { passive: false });
            document.addEventListener('touchmove', dragTouch, { passive: false });
            document.addEventListener('touchend', endDrag);

            function startDrag(e) {
                keyboardIsDragging = true;
                keyboard.classList.add('dragging');

                const rect = keyboard.getBoundingClientRect();
                keyboardDragOffset.x = e.clientX - rect.left;
                keyboardDragOffset.y = e.clientY - rect.top;

                e.preventDefault();
            }

            function startDragTouch(e) {
                const touch = e.touches[0];
                keyboardIsDragging = true;
                keyboard.classList.add('dragging');

                const rect = keyboard.getBoundingClientRect();
                keyboardDragOffset.x = touch.clientX - rect.left;
                keyboardDragOffset.y = touch.clientY - rect.top;

                e.preventDefault();
            }

            function drag(e) {
                if (!keyboardIsDragging) return;

                const x = e.clientX - keyboardDragOffset.x;
                const y = e.clientY - keyboardDragOffset.y;

                // Keep keyboard within viewport
                const maxX = window.innerWidth - keyboard.offsetWidth;
                const maxY = window.innerHeight - keyboard.offsetHeight;

                const constrainedX = Math.max(0, Math.min(x, maxX));
                const constrainedY = Math.max(0, Math.min(y, maxY));

                keyboard.style.left = constrainedX + 'px';
                keyboard.style.top = constrainedY + 'px';
                keyboard.style.transform = 'none';

                e.preventDefault();
            }

            function dragTouch(e) {
                if (!keyboardIsDragging) return;

                const touch = e.touches[0];
                const x = touch.clientX - keyboardDragOffset.x;
                const y = touch.clientY - keyboardDragOffset.y;

                // Keep keyboard within viewport
                const maxX = window.innerWidth - keyboard.offsetWidth;
                const maxY = window.innerHeight - keyboard.offsetHeight;

                const constrainedX = Math.max(0, Math.min(x, maxX));
                const constrainedY = Math.max(0, Math.min(y, maxY));

                keyboard.style.left = constrainedX + 'px';
                keyboard.style.top = constrainedY + 'px';
                keyboard.style.transform = 'none';

                e.preventDefault();
            }

            function endDrag() {
                if (keyboardIsDragging) {
                    keyboardIsDragging = false;
                    keyboard.classList.remove('dragging');

                    // Save keyboard position
                    saveKeyboardPosition();
                }
            }
        }

        // Save keyboard position to localStorage
        function saveKeyboardPosition() {
            const keyboard = document.getElementById('virtualKeyboard');
            const position = {
                left: keyboard.style.left,
                top: keyboard.style.top
            };
            localStorage.setItem('keyboardPosition', JSON.stringify(position));
        }

        // Load keyboard position from localStorage
        function loadKeyboardPosition() {
            const savedPosition = localStorage.getItem('keyboardPosition');
            if (savedPosition) {
                const position = JSON.parse(savedPosition);
                const keyboard = document.getElementById('virtualKeyboard');

                // Validate position is still within viewport
                const maxX = window.innerWidth - 600; // approximate keyboard width
                const maxY = window.innerHeight - 200; // approximate keyboard height

                const left = Math.max(0, Math.min(parseInt(position.left), maxX));
                const top = Math.max(0, Math.min(parseInt(position.top), maxY));

                keyboard.style.left = left + 'px';
                keyboard.style.top = top + 'px';
                keyboard.style.transform = 'none';

                return true;
            }
            return false;
        }

        // Initialize keyboard events - will be called from main DOMContentLoaded
        function initKeyboardEvents() {
            // Initialize drag and drop
            initDragAndDrop();

            // Track active field
            const textInputs = document.querySelectorAll('input[type="text"]');
            textInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    currentActiveField = this;
                    // Auto-show and position keyboard if it's already visible
                    const keyboard = document.getElementById('virtualKeyboard');
                    if (keyboard.style.display === 'block') {
                        positionKeyboard(this);
                        autoSwitchKeyboard(this);
                    }
                });

                // Hide keyboard when clicking outside
                input.addEventListener('blur', function() {
                    // Small delay to allow keyboard clicks
                    setTimeout(() => {
                        if (!document.querySelector('#virtualKeyboard:hover')) {
                            // Don't hide if user is interacting with keyboard
                        }
                    }, 150);
                });
            });

            // Keyboard button events
            document.getElementById('arabicKeyboard').addEventListener('click', switchToArabic);
            document.getElementById('frenchKeyboard').addEventListener('click', switchToFrench);
            document.getElementById('closeKeyboard').addEventListener('click', hideKeyboard);

            // Key button events
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('key-btn')) {
                    const char = e.target.dataset.char;
                    const action = e.target.dataset.action;

                    if (char) {
                        handleKeyPress(char);
                    } else if (action) {
                        handleKeyPress(null, action);
                    }
                }
            });

            // Reposition keyboard on window resize
            window.addEventListener('resize', function() {
                const keyboard = document.getElementById('virtualKeyboard');
                if (keyboard.style.display === 'block' && currentActiveField) {
                    positionKeyboard(currentActiveField);
                }
            });

            // Hide keyboard when scrolling
            window.addEventListener('scroll', function() {
                const keyboard = document.getElementById('virtualKeyboard');
                if (keyboard.style.display === 'block' && currentActiveField) {
                    positionKeyboard(currentActiveField);
                }
            });
        }
        // ===== نظام التحويل الدقيق الجديد =====

        // تحديد ما إذا كانت السنة الهجرية كبيسة
        function isHijriLeapYearAccurate(year) {
            // دورة 30 سنة: السنوات الكبيسة هي 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
            const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
            const yearInCycle = year % 30;
            return leapYears.includes(yearInCycle);
        }

        // التحقق من صحة التاريخ الهجري
        function isValidHijriDateAccurate(dateStr) {
            const pattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
            const match = dateStr.match(pattern);
            if (!match) return false;

            const day = parseInt(match[1]);
            const month = parseInt(match[2]);
            const year = parseInt(match[3]);

            // التحقق من النطاقات الأساسية
            if (year < 1 || year > 1500 || month < 1 || month > 12 || day < 1) {
                return false;
            }

            // التحقق من عدد أيام الشهر
            const hijriMonthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
            let maxDays = hijriMonthDays[month - 1];

            // ذو الحجة في السنة الكبيسة له 30 يوم
            if (month === 12 && isHijriLeapYearAccurate(year)) {
                maxDays = 30;
            }

            return day <= maxDays;
        }

        // التحقق من صحة التاريخ الميلادي
        function isValidGregorianDateAccurate(dateStr) {
            const date = new Date(dateStr);
            return date instanceof Date && !isNaN(date) && dateStr === date.toISOString().split('T')[0];
        }

        // تحويل من هجري إلى ميلادي (دقيق)
        function convertHijriToGregorianAccurate(hijriDateStr) {
            try {
                const parts = hijriDateStr.split('/');
                if (parts.length !== 3) return null;

                const hijriDay = parseInt(parts[0]);
                const hijriMonth = parseInt(parts[1]);
                const hijriYear = parseInt(parts[2]);

                // حساب إجمالي الأيام من 1/1/1 هـ
                let totalDays = 0;

                // إضافة أيام السنوات الكاملة
                for (let year = 1; year < hijriYear; year++) {
                    totalDays += isHijriLeapYearAccurate(year) ? 355 : 354;
                }

                // إضافة أيام الأشهر الكاملة في السنة الحالية
                const hijriMonthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
                for (let month = 1; month < hijriMonth; month++) {
                    let daysInMonth = hijriMonthDays[month - 1];
                    // ذو الحجة في السنة الكبيسة له 30 يوم
                    if (month === 12 && isHijriLeapYearAccurate(hijriYear)) {
                        daysInMonth = 30;
                    }
                    totalDays += daysInMonth;
                }

                // إضافة الأيام في الشهر الحالي (ناقص 1 لأن اليوم الأول = 0)
                totalDays += hijriDay - 1;

                // نقطة البداية المصححة: 19 يوليو 622 م
                const hijriEpoch = new Date(622, 6, 19);

                // حساب التاريخ الميلادي
                const gregorianDate = new Date(hijriEpoch.getTime() + totalDays * 24 * 60 * 60 * 1000);

                // تنسيق التاريخ
                const year = gregorianDate.getFullYear();
                const month = (gregorianDate.getMonth() + 1).toString().padStart(2, '0');
                const day = gregorianDate.getDate().toString().padStart(2, '0');

                return `${year}-${month}-${day}`;
            } catch (error) {
                console.error('خطأ في تحويل التاريخ الهجري:', error);
                return null;
            }
        }

        // تحويل من ميلادي إلى هجري (دقيق)
        function convertGregorianToHijriAccurate(gregorianDateStr) {
            try {
                const date = new Date(gregorianDateStr);
                if (isNaN(date)) return null;

                // نقطة البداية المصححة: 19 يوليو 622 م
                const hijriEpoch = new Date(622, 6, 19);

                // حساب الفرق بالأيام
                const diffTime = date.getTime() - hijriEpoch.getTime();
                const diffDays = Math.floor(diffTime / (24 * 60 * 60 * 1000));

                if (diffDays < 0) return null; // تاريخ قبل بداية التقويم الهجري

                // حساب السنة الهجرية
                let hijriYear = 1;
                let remainingDays = diffDays;

                while (true) {
                    const daysInYear = isHijriLeapYearAccurate(hijriYear) ? 355 : 354;
                    if (remainingDays >= daysInYear) {
                        remainingDays -= daysInYear;
                        hijriYear++;
                    } else {
                        break;
                    }
                }

                // حساب الشهر واليوم الهجري
                const hijriMonthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];
                let hijriMonth = 1;

                while (hijriMonth <= 12) {
                    let daysInMonth = hijriMonthDays[hijriMonth - 1];

                    // ذو الحجة في السنة الكبيسة له 30 يوم
                    if (hijriMonth === 12 && isHijriLeapYearAccurate(hijriYear)) {
                        daysInMonth = 30;
                    }

                    if (remainingDays >= daysInMonth) {
                        remainingDays -= daysInMonth;
                        hijriMonth++;
                    } else {
                        break;
                    }
                }

                const hijriDay = remainingDays + 1;

                // التأكد من صحة النتائج
                if (hijriMonth > 12) {
                    hijriMonth = 12;
                    hijriDay = isHijriLeapYearAccurate(hijriYear) ? 30 : 29;
                }

                // تنسيق التاريخ الهجري
                const formattedDay = hijriDay.toString().padStart(2, '0');
                const formattedMonth = hijriMonth.toString().padStart(2, '0');

                return `${formattedDay}/${formattedMonth}/${hijriYear}`;
            } catch (error) {
                console.error('خطأ في تحويل التاريخ الميلادي:', error);
                return null;
            }
        }

        // إظهار رسالة تأكيد التحويل
        function showConversionMessageDB(message, targetElement) {
            // إزالة أي رسالة سابقة
            const existingMessage = targetElement.parentNode.querySelector('.conversion-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // إنشاء رسالة جديدة
            const messageDiv = document.createElement('div');
            messageDiv.className = 'conversion-message';
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: absolute;
                top: -25px;
                right: 0;
                background: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                z-index: 1000;
                opacity: 0;
                transform: translateY(10px);
                transition: all 0.3s ease;
                pointer-events: none;
            `;

            // إضافة الرسالة
            targetElement.parentNode.style.position = 'relative';
            targetElement.parentNode.appendChild(messageDiv);

            // إظهار الرسالة
            setTimeout(() => {
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0)';
            }, 100);

            // إخفاء الرسالة بعد 2 ثانية
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 2000);
        }

        // تحسين دالة التحويل الموجودة لاستخدام النظام الجديد
        function setupAccurateConversion() {
            const birthDateInput = document.getElementById('birthDate');
            const hijriDateInput = document.getElementById('hijriDate');

            // إضافة مستمع للتحويل من ميلادي إلى هجري
            birthDateInput.addEventListener('input', function() {
                const hijriMode = document.querySelector('input[name="hijriMode"]:checked');
                if (hijriMode && hijriMode.value === 'auto') {
                    const gregorianDate = this.value.trim();
                    if (gregorianDate && isValidGregorianDateAccurate(gregorianDate)) {
                        const hijriDate = convertGregorianToHijriAccurate(gregorianDate);
                        if (hijriDate) {
                            hijriDateInput.value = hijriDate;
                            hijriDateInput.classList.add('date-conversion-animation');
                            setTimeout(() => {
                                hijriDateInput.classList.remove('date-conversion-animation');
                            }, 500);
                            showConversionMessageDB('✅ تم التحويل إلى الهجري', hijriDateInput);
                        }
                    }
                }
            });

            // إضافة مستمع للتحويل من هجري إلى ميلادي
            hijriDateInput.addEventListener('input', function() {
                const hijriMode = document.querySelector('input[name="hijriMode"]:checked');
                if (hijriMode && hijriMode.value === 'manual') {
                    const hijriDate = this.value.trim();
                    if (hijriDate && isValidHijriDateAccurate(hijriDate)) {
                        const gregorianDate = convertHijriToGregorianAccurate(hijriDate);
                        if (gregorianDate) {
                            birthDateInput.value = gregorianDate;
                            birthDateInput.classList.add('date-conversion-animation');
                            setTimeout(() => {
                                birthDateInput.classList.remove('date-conversion-animation');
                            }, 500);
                            showConversionMessageDB('✅ تم التحويل إلى الميلادي', birthDateInput);
                        }
                    }
                }
            });
        }

        // تفعيل النظام الجديد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupAccurateConversion();
            console.log('✅ تم تفعيل نظام التحويل الدقيق الجديد');
        });
    </script>
</body>
</html>
