<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسيت كلمة المرور - مكتب الحالة المدنية أيير</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇲🇦</text></svg>">
    
    <style>
        /* ===== إعدادات عامة ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* ألوان المملكة المغربية */
            --morocco-red: #C1272D;
            --morocco-green: #006233;
            --morocco-gold: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #bdc3c7;
            --background-light: #f8f9fa;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);
            
            /* خطوط */
            --font-primary: 'Segoe UI', 'Amiri', 'Times New Roman', serif;
            --font-secondary: 'Arial', 'Tahoma', sans-serif;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: var(--text-dark);
            line-height: 1.6;
            padding: 20px;
        }

        .forgot-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }

        .forgot-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--morocco-green) 0%, var(--morocco-red) 50%, var(--morocco-green) 100%);
        }

        .forgot-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .morocco-emblem {
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, var(--morocco-gold) 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            border: 3px solid rgba(196, 30, 58, 0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
            margin: 0 auto 20px;
        }

        .forgot-title {
            color: var(--morocco-red);
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .forgot-subtitle {
            color: var(--text-light);
            font-size: 1em;
            margin-bottom: 10px;
        }

        .forgot-department {
            color: var(--morocco-green);
            font-size: 0.9em;
            font-weight: 600;
        }

        .forgot-description {
            background: rgba(0, 98, 51, 0.05);
            border: 1px solid rgba(0, 98, 51, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-size: 0.9em;
            line-height: 1.5;
            color: var(--text-dark);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 25px;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, var(--morocco-green), #004d26);
            color: white;
        }

        .step.inactive {
            background: rgba(0, 0, 0, 0.1);
            color: var(--text-light);
        }

        .forgot-form {
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            font-family: var(--font-primary);
            transition: all 0.3s ease;
            background: white;
            direction: rtl;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--morocco-green);
            box-shadow: 0 0 0 3px rgba(0, 98, 51, 0.1);
            transform: translateY(-2px);
        }

        .form-group input:invalid {
            border-color: var(--morocco-red);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2em;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--morocco-green);
        }

        .forgot-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--morocco-red), #8b0000);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .forgot-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .forgot-btn:hover::before {
            left: 100%;
        }

        .forgot-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(196, 30, 58, 0.4);
            background: linear-gradient(135deg, #8b0000, var(--morocco-red));
        }

        .forgot-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .back-links {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .back-links a {
            color: var(--morocco-green);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
            margin: 0 10px;
            display: inline-block;
            padding: 5px 10px;
        }

        .back-links a:hover {
            color: var(--morocco-red);
            text-decoration: underline;
        }

        /* رسائل التنبيه */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
            display: none;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(0, 98, 51, 0.1), rgba(0, 77, 38, 0.1));
            color: var(--morocco-green);
            border: 1px solid rgba(0, 98, 51, 0.3);
        }

        .alert-error {
            background: linear-gradient(135deg, rgba(196, 30, 58, 0.1), rgba(139, 0, 0, 0.1));
            color: var(--morocco-red);
            border: 1px solid rgba(196, 30, 58, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
            color: #2980b9;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        /* خطوات إعادة تعيين كلمة المرور */
        .reset-step {
            display: none;
        }

        .reset-step.active {
            display: block;
        }

        .verification-code {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }

        .verification-code input {
            width: 50px;
            height: 50px;
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: white;
        }

        .verification-code input:focus {
            border-color: var(--morocco-green);
            box-shadow: 0 0 0 2px rgba(0, 98, 51, 0.1);
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .forgot-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .forgot-title {
                font-size: 1.7em;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 2em;
            }

            .step-indicator {
                flex-direction: column;
                gap: 10px;
            }

            .verification-code input {
                width: 40px;
                height: 40px;
                font-size: 1.2em;
            }
        }

        /* تأثيرات التحميل */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-header">
            <div class="morocco-emblem">🔑</div>
            <h1 class="forgot-title">نسيت كلمة المرور</h1>
            <p class="forgot-subtitle">مكتب الحالة المدنية - أيير</p>
            <p class="forgot-department">قسم استعادة كلمة المرور</p>
        </div>

        <div class="forgot-description">
            <strong>📋 خطوات استعادة كلمة المرور:</strong><br>
            1. أدخل اسم المستخدم أو البريد الإلكتروني<br>
            2. سيتم إرسال رمز التحقق (محاكاة)<br>
            3. أدخل الرمز وكلمة المرور الجديدة
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">1️⃣ البحث عن الحساب</div>
        </div>

        <div id="alertContainer"></div>

        <!-- الخطوة الأولى: البحث عن الحساب -->
        <div class="reset-step active" id="stepFindAccount">
            <form class="forgot-form" id="findAccountForm">
                <div class="form-group">
                    <label for="identifier">اسم المستخدم أو البريد الإلكتروني:</label>
                    <input type="text" id="identifier" name="identifier" required placeholder="أدخل اسم المستخدم أو البريد الإلكتروني">
                </div>

                <button type="submit" class="forgot-btn" id="findAccountBtn">
                    <span id="findAccountBtnText">البحث عن الحساب</span>
                </button>
            </form>
        </div>

        <!-- الخطوة الثانية: التحقق من الرمز -->
        <div class="reset-step" id="stepVerifyCode">
            <form class="forgot-form" id="verifyCodeForm">
                <div class="form-group">
                    <label>رمز التحقق المرسل إلى بريدك الإلكتروني:</label>
                    <div class="verification-code">
                        <input type="text" maxlength="1" id="code1" oninput="moveToNext(this, 'code2')">
                        <input type="text" maxlength="1" id="code2" oninput="moveToNext(this, 'code3')">
                        <input type="text" maxlength="1" id="code3" oninput="moveToNext(this, 'code4')">
                        <input type="text" maxlength="1" id="code4" oninput="moveToNext(this, 'code5')">
                        <input type="text" maxlength="1" id="code5" oninput="moveToNext(this, 'code6')">
                        <input type="text" maxlength="1" id="code6">
                    </div>
                </div>

                <button type="submit" class="forgot-btn" id="verifyCodeBtn">
                    <span id="verifyCodeBtnText">التحقق من الرمز</span>
                </button>

                <div style="text-align: center; margin-top: 15px;">
                    <a href="#" onclick="resendCode()" style="color: var(--morocco-green); font-size: 0.9em;">إعادة إرسال الرمز</a>
                </div>
            </form>
        </div>

        <!-- الخطوة الثالثة: تعيين كلمة مرور جديدة -->
        <div class="reset-step" id="stepNewPassword">
            <form class="forgot-form" id="newPasswordForm">
                <div class="form-group">
                    <label for="newPassword">كلمة المرور الجديدة:</label>
                    <div class="password-container">
                        <input type="password" id="newPassword" name="newPassword" required placeholder="كلمة مرور قوية" minlength="6">
                        <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">👁️</button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmNewPassword">تأكيد كلمة المرور الجديدة:</label>
                    <div class="password-container">
                        <input type="password" id="confirmNewPassword" name="confirmNewPassword" required placeholder="أعد كتابة كلمة المرور">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmNewPassword')">👁️</button>
                    </div>
                </div>

                <button type="submit" class="forgot-btn" id="newPasswordBtn">
                    <span id="newPasswordBtnText">تحديث كلمة المرور</span>
                </button>
            </form>
        </div>

        <div class="back-links">
            <a href="login.html">🔙 العودة لتسجيل الدخول</a>
            <a href="register.html">📝 إنشاء حساب جديد</a>
        </div>
    </div>

    <script src="login-db.js"></script>
    <script>
        // إدارة نسيت كلمة المرور
        class ForgotPasswordManager {
            constructor() {
                this.loginDB = new LoginDB();
                this.currentUser = null;
                this.verificationCode = null;
                this.currentStep = 1;
                this.init();
            }

            async init() {
                await this.loginDB.init();
                this.setupEventListeners();
            }

            setupEventListeners() {
                document.getElementById('findAccountForm').addEventListener('submit', (e) => this.handleFindAccount(e));
                document.getElementById('verifyCodeForm').addEventListener('submit', (e) => this.handleVerifyCode(e));
                document.getElementById('newPasswordForm').addEventListener('submit', (e) => this.handleNewPassword(e));
            }

            async handleFindAccount(event) {
                event.preventDefault();
                
                const identifier = document.getElementById('identifier').value.trim();

                if (!identifier) {
                    this.showAlert('يرجى إدخال اسم المستخدم أو البريد الإلكتروني', 'error');
                    return;
                }

                this.setLoading('findAccount', true);

                try {
                    // البحث عن المستخدم
                    let user = await this.loginDB.getUserByUsername(identifier);
                    
                    if (!user) {
                        // البحث بالبريد الإلكتروني
                        const users = await this.loginDB.getAllUsers();
                        user = users.find(u => u.email === identifier.toLowerCase());
                    }

                    if (user) {
                        this.currentUser = user;
                        this.generateVerificationCode();
                        this.showStep(2);
                        this.showAlert(`تم إرسال رمز التحقق إلى: ${this.maskEmail(user.email)}`, 'success');
                    } else {
                        this.showAlert('لم يتم العثور على حساب بهذا الاسم أو البريد الإلكتروني', 'error');
                    }
                } catch (error) {
                    console.error('خطأ في البحث عن الحساب:', error);
                    this.showAlert('حدث خطأ أثناء البحث عن الحساب', 'error');
                } finally {
                    this.setLoading('findAccount', false);
                }
            }

            async handleVerifyCode(event) {
                event.preventDefault();
                
                const enteredCode = this.getVerificationCode();

                if (enteredCode.length !== 6) {
                    this.showAlert('يرجى إدخال رمز التحقق كاملاً', 'error');
                    return;
                }

                this.setLoading('verifyCode', true);

                try {
                    if (enteredCode === this.verificationCode) {
                        this.showStep(3);
                        this.showAlert('تم التحقق من الرمز بنجاح!', 'success');
                    } else {
                        this.showAlert('رمز التحقق غير صحيح', 'error');
                        this.clearVerificationCode();
                    }
                } catch (error) {
                    console.error('خطأ في التحقق من الرمز:', error);
                    this.showAlert('حدث خطأ أثناء التحقق من الرمز', 'error');
                } finally {
                    this.setLoading('verifyCode', false);
                }
            }

            async handleNewPassword(event) {
                event.preventDefault();
                
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmNewPassword').value;

                if (!this.validateNewPassword(newPassword, confirmPassword)) {
                    return;
                }

                this.setLoading('newPassword', true);

                try {
                    // تشفير كلمة المرور الجديدة
                    const hashedPassword = await this.loginDB.hashPassword(newPassword);
                    
                    // تحديث كلمة المرور في قاعدة البيانات
                    this.currentUser.password = hashedPassword;
                    this.currentUser.updatedAt = new Date().toISOString();
                    
                    await this.loginDB.updateUser(this.currentUser);
                    
                    // تسجيل النشاط
                    await this.loginDB.logUserActivity(this.currentUser.id, 'password_reset', {
                        method: 'forgot_password',
                        timestamp: new Date().toISOString()
                    });

                    this.showAlert('تم تحديث كلمة المرور بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...', 'success');
                    
                    // إعادة توجيه لصفحة تسجيل الدخول بعد 3 ثوان
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);

                } catch (error) {
                    console.error('خطأ في تحديث كلمة المرور:', error);
                    this.showAlert('حدث خطأ أثناء تحديث كلمة المرور', 'error');
                } finally {
                    this.setLoading('newPassword', false);
                }
            }

            generateVerificationCode() {
                // توليد رمز تحقق من 6 أرقام
                this.verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
                console.log('🔑 رمز التحقق المولد:', this.verificationCode); // للاختبار فقط
                
                // في بيئة الإنتاج، سيتم إرسال الرمز عبر البريد الإلكتروني
                this.showAlert(`رمز التحقق للاختبار: ${this.verificationCode}`, 'info');
            }

            getVerificationCode() {
                let code = '';
                for (let i = 1; i <= 6; i++) {
                    code += document.getElementById(`code${i}`).value;
                }
                return code;
            }

            clearVerificationCode() {
                for (let i = 1; i <= 6; i++) {
                    document.getElementById(`code${i}`).value = '';
                }
                document.getElementById('code1').focus();
            }

            validateNewPassword(password, confirmPassword) {
                if (password.length < 6) {
                    this.showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                    return false;
                }

                if (password !== confirmPassword) {
                    this.showAlert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'error');
                    return false;
                }

                return true;
            }

            showStep(stepNumber) {
                // إخفاء جميع الخطوات
                document.querySelectorAll('.reset-step').forEach(step => {
                    step.classList.remove('active');
                });

                // إظهار الخطوة المطلوبة
                const stepElement = document.getElementById(`step${stepNumber === 1 ? 'FindAccount' : stepNumber === 2 ? 'VerifyCode' : 'NewPassword'}`);
                stepElement.classList.add('active');

                // تحديث مؤشر الخطوة
                const stepIndicator = document.getElementById('step1');
                const stepTexts = [
                    '1️⃣ البحث عن الحساب',
                    '2️⃣ التحقق من الرمز',
                    '3️⃣ كلمة مرور جديدة'
                ];
                stepIndicator.textContent = stepTexts[stepNumber - 1];

                this.currentStep = stepNumber;
            }

            maskEmail(email) {
                const [username, domain] = email.split('@');
                const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
                return `${maskedUsername}@${domain}`;
            }

            setLoading(type, loading) {
                const btn = document.getElementById(`${type}Btn`);
                const btnText = document.getElementById(`${type}BtnText`);
                
                if (loading) {
                    btn.disabled = true;
                    const loadingTexts = {
                        findAccount: 'جاري البحث...',
                        verifyCode: 'جاري التحقق...',
                        newPassword: 'جاري التحديث...'
                    };
                    btnText.innerHTML = `${loadingTexts[type]} <span class="loading"></span>`;
                } else {
                    btn.disabled = false;
                    const normalTexts = {
                        findAccount: 'البحث عن الحساب',
                        verifyCode: 'التحقق من الرمز',
                        newPassword: 'تحديث كلمة المرور'
                    };
                    btnText.textContent = normalTexts[type];
                }
            }

            showAlert(message, type) {
                const alertContainer = document.getElementById('alertContainer');
                const alertClass = type === 'success' ? 'alert-success' : type === 'info' ? 'alert-info' : 'alert-error';
                
                alertContainer.innerHTML = `
                    <div class="alert ${alertClass}" style="display: block;">
                        ${message}
                    </div>
                `;

                // إخفاء الرسالة بعد 5 ثوان (إلا إذا كانت رمز التحقق)
                if (!message.includes('رمز التحقق للاختبار')) {
                    setTimeout(() => {
                        alertContainer.innerHTML = '';
                    }, 5000);
                }
            }
        }

        // وظائف مساعدة
        function moveToNext(current, nextId) {
            if (current.value.length === 1) {
                const nextInput = document.getElementById(nextId);
                if (nextInput) {
                    nextInput.focus();
                }
            }
        }

        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleBtn = passwordInput.nextElementSibling;
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        function resendCode() {
            // محاكاة إعادة إرسال الرمز
            alert('تم إعادة إرسال رمز التحقق إلى بريدك الإلكتروني');
        }

        // تهيئة مدير نسيت كلمة المرور
        document.addEventListener('DOMContentLoaded', () => {
            new ForgotPasswordManager();
        });
    </script>
</body>
</html>
