<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عقد ازدياد مزدوج - عربي/فرنسي</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 5mm;
            font-size: 12px; /* تكبير الخط من 10px إلى 12px */
            font-weight: 500;
            line-height: 1.3; /* زيادة المسافة بين الأسطر */
            background: #f0f0f0;
        }
        .document {
            width: 190mm; /* عرض نصف ورقة A4 أفقي مع هامش 1 سم من كل جهة (210mm - 20mm) */
            height: 128.5mm; /* ارتفاع نصف ورقة A4 أفقي مع هامش 1 سم من كل جهة (148.5mm - 20mm) */
            margin: 10mm auto; /* هامش 1 سم من جميع الجهات */
            padding: 5mm; /* حشو داخلي */
            background: white;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            position: relative;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4mm;
            padding-bottom: 3mm;
            font-size: 11px;
            font-weight: bold;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }
        .header-right {
            text-align: left;
            direction: ltr;
            flex: 1;
            max-width: 30%; /* تحديد عرض أقصى */
        }
        .header-center {
            text-align: center;
            flex: 1;
            margin: 0 2mm; /* تقليل المسافة الجانبية */
            max-width: 40%; /* تحديد عرض أقصى */
            overflow: hidden; /* منع تجاوز النص */
        }
        .header-left {
            text-align: right;
            direction: rtl;
            flex: 1;
            max-width: 30%; /* تحديد عرض أقصى */
        }

        /* Unified content layout */
        .content {
            height: calc(100% - 20mm);
            padding: 2mm;
            display: flex;
            flex-direction: column;
        }

        /* Title */
        .dual-title {
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            margin: 2mm 0;
            padding: 2mm;
            border: 1px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .title-french {
            flex: 1;
            text-align: left;
            direction: ltr;
        }
        .title-arabic {
            flex: 1;
            text-align: right;
            direction: rtl;
        }

        /* Content lines - Merged */
        .merged-line {
            margin: 0.4mm 0; /* تقليل المسافة بين الأسطر لتناسب نصف A4 */
            font-size: 11px; /* تكبير الخط من 9px إلى 11px */
            font-weight: 500;
            display: flex;
            align-items: baseline;
            justify-content: space-between;
        }
        .french-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: ltr;
            text-align: left;
        }
        .arabic-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: rtl;
            text-align: right;
        }
        .label {
            font-weight: 500; /* تقليل سمك النصوص الثابتة من 600 إلى 500 */
            margin: 0 2mm;
            white-space: nowrap;
            min-width: 15mm;
            color: #333; /* لون أفتح للنصوص الثابتة */
        }
        .underline {
            border-bottom: 1.5px solid #000; /* تسميك الخط السفلي */
            min-width: 15mm;
            padding: 0 1mm;
            flex: 1;
            min-height: 2mm; /* تقليل ارتفاع المنطقة لتوفير مساحة */
            font-weight: 700; /* تسميك المعلومات الشخصية */
            color: #000; /* لون أسود قوي للمعلومات الشخصية */
        }

        /* إصلاح اتجاه التاريخ الهجري بالفرنسية */
        #hijriDateFr {
            direction: ltr !important;
            text-align: left !important;
            unicode-bidi: embed;
        }

        /* خط تابع للنص في مكان الازدياد - يزداد مع الكلام */
        .birth-place-adaptive {
            border-bottom: 1.5px solid #000;
            min-width: 15mm;        /* حد أدنى فقط */
            width: auto;            /* عرض تلقائي حسب النص */
            max-width: none;        /* بدون حد أقصى */
            padding: 0 1mm;
            min-height: 2mm;
            font-weight: 700;
            color: #000;
            overflow: visible;      /* إظهار النص الزائد */
            white-space: nowrap;    /* منع التفاف - سطر واحد */
            text-overflow: clip;    /* عدم قطع النص */
            font-size: 15px;        /* زيادة من 13px إلى 15px */
            display: inline-block;
            vertical-align: baseline;
            word-wrap: normal;      /* عدم كسر الكلمات */
            line-height: 1.1;       /* تقليل ارتفاع السطر */
        }



        /* تصغير الخط للنصوص الطويلة */
        .birth-place-adaptive.long-text {
            font-size: 13px;        /* زيادة من 11px إلى 13px */
        }

        .birth-place-adaptive.very-long-text {
            font-size: 11px;        /* زيادة من 9px إلى 11px */
        }



        /* Act info */
        .act-info {
            margin: 2mm 0;
            font-size: 8px;
        }
        .act-line {
            display: flex;
            justify-content: space-between;
            margin: 1mm 0;
        }

        /* Signature area */
        .signature {
            margin-top: auto;
            text-align: center;
            font-size: 7px;
            padding-top: 3mm;
            border-top: 0.5px solid #000;
        }
        .stamp {
            width: 15mm;
            height: 15mm;
            border: 1px solid #000;
            margin: 2mm auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6px;
            line-height: 1;
        }

        /* Test controls */
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .controls button {
            background: white;
            color: #007bff;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
        }

        @media print {
            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                font-size: 11px !important;
                width: 210mm !important;
                height: 148.5mm !important;
                overflow: hidden !important;
            }

            .document {
                width: 210mm !important; /* استخدام عرض الصفحة الكامل */
                height: 148.5mm !important; /* استخدام ارتفاع الصفحة الكامل */
                margin: 0 !important; /* إزالة جميع الهوامش */
                padding: 10mm !important; /* الهوامش كحشو داخلي */
                box-sizing: border-box !important;
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                transform: none !important;
                zoom: 1 !important;
                background: white !important;
            }

            .controls {
                display: none !important;
            }

            /* مطابقة إعدادات الهيدر مع المعاينة */
            .header {
                font-size: 11px !important;
                margin-bottom: 4mm !important;
                padding-bottom: 3mm !important;
                overflow: hidden !important;
                position: relative !important;
                z-index: 1 !important;
            }

            .header-center {
                font-size: 9px !important;
                line-height: 1.2 !important;
            }

            /* إعدادات الصفحة للطباعة - نصف ورقة A4 أفقي */
            @page {
                margin: 0 !important; /* إزالة جميع هوامش الطباعة */
                size: 210mm 148.5mm !important; /* حجم نصف ورقة A4 أفقي */
                padding: 0 !important;
            }

            /* مطابقة إعدادات المحتوى مع المعاينة */
            .content {
                height: calc(100% - 20mm) !important;
                padding: 2mm !important;
                display: flex !important;
                flex-direction: column !important;
                position: relative !important;
                z-index: 0 !important;
            }

            .merged-line {
                margin: 0.4mm 0 !important;
                font-size: 11px !important;
            }

            .underline, .birth-place-adaptive {
                min-height: 2mm !important;
                padding: 0 1mm !important;
                border-bottom: 1.5px solid #000 !important;
            }

            .birth-place-adaptive {
                font-size: 15px !important;
            }

            /* ضمان عدم تجاوز المحتوى للحدود */
            * {
                box-sizing: border-box !important;
            }
        }
    </style>
</head>
<body>
    <div class="controls">
        <a href="main-dashboard.html" style="background: white; color: #007bff; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block;">🏠 الرئيسية</a>
        <a href="citizens-database.html" style="background: white; color: #007bff; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block;">👥 قاعدة البيانات</a>
        <button onclick="window.print()">طباعة</button>
        <button onclick="fillTestData()">بيانات تجريبية</button>
    </div>

    <div class="document">
        <!-- Header -->
        <div class="header">
            <div class="header-right">
                <div>ROYAUME DU MAROC</div>
                <div>Ministère de l'Intérieur</div>
                <div>Province de Safi</div>
                <div>Commune d'Ayir</div>
                <div>Bureau de l'État-Civil d'Ayir</div>
            </div>
            <div class="header-center">
                <div style="font-size: 9px; line-height: 1.2; font-weight: 700; color: #000; text-align: center;">
                    <div style="margin-bottom: 0.5mm;">طبقا للقانون رقم 37.99</div>
                    <div style="margin-bottom: 0.5mm;">المتعلق بالحالة المدنية</div>
                    <div style="margin-bottom: 0.5mm;">الظهير الشريف رقم 1.02.239</div>
                    <div>بتاريخ 25 رجب 1423 (3 أكتوبر 2002)</div>
                </div>
            </div>
            <div class="header-left">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
            </div>
        </div>

        <!-- Unified content with merged languages -->
        <div class="content">


            <!-- Title with Act Information -->
            <div style="display: flex; align-items: flex-start; margin: 1mm 0; padding: 1mm;">
                <!-- French Act Info (Left) -->
                <div style="flex: 1; text-align: left; direction: ltr; font-size: 10px;">
                    <div>Acte N°: <span style="border-bottom: 1.5px solid #000; min-width: 25mm; display: inline-block; font-weight: 700;" id="actNumberFr"></span></div>
                    <div style="margin-top: 2mm; display: flex; align-items: flex-start; display: none;">
                        <span style="margin-right: 3mm;">Année</span>
                        <div style="display: flex; flex-direction: column;">
                            <span style="font-size: 11px; font-weight: 700; color: #000;">Hégirienne</span>
                            <span style="font-size: 11px; font-weight: 700; color: #000; margin-top: 1mm;">Grégorienne</span>
                        </div>
                        <div style="display: flex; flex-direction: column; margin-left: 2mm;">
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="hijriYearFr"></span>
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block; margin-top: 1mm;" id="gregYearFr"></span>
                        </div>
                    </div>
                </div>

                <!-- Centered Title -->
                <div style="flex: 2; text-align: center; font-weight: bold;">
                    <div style="font-size: 14px; margin-bottom: 2mm; font-weight: 800;">نسخة موجزة من رسم الولادة</div>
                    <div style="font-size: 12px; font-weight: 700;">EXTRAIT D'ACTE DE NAISSANCE</div>
                </div>

                <!-- Arabic Act Info (Right) -->
                <div style="flex: 1; text-align: right; direction: rtl; font-size: 10px;">
                    <div>رقم العقد<span style="border-bottom: 1.5px solid #000; min-width: 25mm; display: inline-block; font-weight: 700;" id="actNumberAr"></span></div>
                    <table style="margin-top: 2mm; border-collapse: collapse; direction: rtl; display: none;">
                        <tr>
                            <td rowspan="2" style="vertical-align: top; padding-left: 3mm;">سنة</td>
                            <td style="font-size: 11px; font-weight: 700; color: #000; padding-left: 2mm;">هجرية</td>
                            <td><span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="hijriYearAr"></span></td>
                        </tr>
                        <tr>
                            <td style="font-size: 11px; font-weight: 700; color: #000; padding-left: 2mm; padding-top: 1mm;">ميلادية</td>
                            <td style="padding-top: 1mm;"><span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="gregYearAr"></span></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Prénom</span>
                    <span class="underline" id="firstNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">الاسم الشخصي</span>
                    <span class="underline" id="firstNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Nom de famille</span>
                    <span class="underline" id="familyNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">الاسم العائلي</span>
                    <span class="underline" id="familyNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Lieu de naissance</span>
                    <span class="birth-place-adaptive" id="birthPlaceFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">مكان الازدياد</span>
                    <span class="birth-place-adaptive" id="birthPlaceAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Né(e) le</span>
                    <span class="underline" id="birthDateFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">ولد(ت) في يوم</span>
                    <span class="underline" id="birthDateAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Correspondant au</span>
                    <span class="underline" id="hijriDateFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">الموافق لـ</span>
                    <span class="underline" id="hijriDateAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Sexe</span>
                    <span class="underline" id="genderFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">جنسه(ا)</span>
                    <span class="underline" id="genderAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Père</span>
                    <span class="underline" id="fatherNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">والده(ا) هو</span>
                    <span class="underline" id="fatherNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Mère</span>
                    <span class="underline" id="motherNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">والدته(ا) هي</span>
                    <span class="underline" id="motherNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Mention marginale</span>
                    <span class="underline" id="marginalNoteFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">بيان (الوفاة) المشار إليه في طرة الرسم</span>
                    <span class="underline" id="marginalNoteAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Nous certifions en qualité d'officier</span>
                    <span class="underline" id="certificationFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">نشهد بصفتنا ضابطا الحالة المدنية نحن</span>
                    <span class="underline" id="certificationAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">La conformité de cette copie aux registres</span>
                </div>
                <div class="arabic-part">
                    <span class="label">بمطابقة هذه النسخة لما هو مضمن بسجلات الحالة المدنية لمكتب الحالة المدنية بأيير</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Établi à</span>
                    <span class="underline" id="issuePlaceFr">Ayir</span>
                    <span class="label">le</span>
                    <span class="underline" id="issueDateFr">20 Janvier 2024</span>
                </div>
                <div class="arabic-part">
                    <span class="label">حرر بـ</span>
                    <span class="underline" id="issuePlaceAr">أيير</span>
                    <span class="label">بتاريخ</span>
                    <span class="underline" id="issueDateAr">20 يناير 2024</span>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="merged-line" style="margin-top: 4mm; padding-top: 0mm;">
                <div class="french-part" style="text-align: center; padding-right: 0mm; margin-left: 20mm;">
                    <div style="font-weight: 600;">ضابط الحالة المدنية<br><span style="font-size: 7px; font-weight: 500;">L'Officier de l'État-Civil</span></div>
                </div>
                <div class="arabic-part" style="text-align: center; padding-left: 0mm; margin-right: 20mm;">
                    <div style="font-weight: 600;">طابع مكتب الحالة المدنية<br><span style="font-size: 7px; font-weight: 500;">Cachet du Bureau de l'État-Civil</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillTestData() {
            // Arabic data
            document.getElementById('firstNameAr').textContent = 'أحمد';
            document.getElementById('familyNameAr').textContent = 'محمد علي';
            document.getElementById('birthPlaceAr').textContent = 'الرباط';
            document.getElementById('hijriDateAr').textContent = '5 جمادى الآخرة 1445';
            document.getElementById('fatherNameAr').textContent = 'محمد علي حسن';
            document.getElementById('motherNameAr').textContent = 'فاطمة أحمد محمد';

            // French data
            document.getElementById('firstNameFr').textContent = 'Ahmed';
            document.getElementById('familyNameFr').textContent = 'Mohamed Ali';
            document.getElementById('birthPlaceFr').textContent = 'Rabat';
            document.getElementById('hijriDateFr').textContent = convertHijriToFrench('5 جمادى الآخرة 1445');
            document.getElementById('fatherNameFr').textContent = 'Mohamed Ali Hassan';
            document.getElementById('motherNameFr').textContent = 'Fatima Ahmed Mohamed';

            // تحويل تاريخ الميلاد التجريبي إلى كلمات وعرضه في الحقول
            convertBirthDateToWords('2024-01-15');
        }

        // Load data from URL parameters
        async function loadDataFromURL() {
            try {
                console.log('🔍 Starting loadDataFromURL...');
                const urlParams = new URLSearchParams(window.location.search);

                console.log('🔍 URL Parameters:', Object.fromEntries(urlParams.entries()));
                console.log('🔍 Current URL:', window.location.href);

                // Check if this is a request with citizen ID
                if (urlParams.has('id')) {
                    const citizenId = decodeURIComponent(urlParams.get('id'));
                    console.log('📜 Loading citizen data for ID:', citizenId);
                    await loadCitizenDataById(citizenId);
                    return;
                }

                // Check if this is a print request with ID (legacy support)
                if (urlParams.has('print')) {
                    const citizenId = decodeURIComponent(urlParams.get('print'));
                    console.log('🖨️ Loading citizen data for print ID:', citizenId);
                    await loadCitizenDataById(citizenId);
                    return;
                }

                // Original method - load from URL parameters
                if (urlParams.has('firstNameAr') || urlParams.has('firstName')) {
                    console.log('📋 Loading data from URL parameters...');
                // Fill Arabic fields
                document.getElementById('firstNameAr').textContent = urlParams.get('firstNameAr') || urlParams.get('firstName') || '';
                document.getElementById('familyNameAr').textContent = urlParams.get('familyNameAr') || urlParams.get('familyName') || '';
                document.getElementById('birthPlaceAr').textContent = urlParams.get('birthPlaceAr') || urlParams.get('birthPlace') || '';
                // استخدام التاريخ بالحروف إذا كان متوفراً، وإلا استخدام التاريخ العادي
                const birthDateWords = urlParams.get('birthDateWords');
                const birthDate = urlParams.get('birthDate') || '';

                if (birthDateWords) {
                    document.getElementById('birthDateAr').textContent = birthDateWords;
                } else if (birthDate) {
                    // تحويل التاريخ إلى كلمات إذا لم يكن متوفراً
                    const arabicWords = dateToWords.gregorianDateToWordsArabic(birthDate);
                    document.getElementById('birthDateAr').textContent = arabicWords || birthDate;
                } else {
                    document.getElementById('birthDateAr').textContent = '';
                }

                document.getElementById('hijriDateAr').textContent = urlParams.get('hijriDate') || '';
                document.getElementById('genderAr').textContent = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').textContent = urlParams.get('fatherNameAr') || urlParams.get('fatherName') || '';
                document.getElementById('motherNameAr').textContent = urlParams.get('motherNameAr') || urlParams.get('motherName') || '';
                document.getElementById('actNumberAr').textContent = urlParams.get('actNumber') || '';

                // Fill French fields
                document.getElementById('firstNameFr').textContent = urlParams.get('firstNameFr') || urlParams.get('firstName') || '';
                document.getElementById('familyNameFr').textContent = urlParams.get('familyNameFr') || urlParams.get('familyName') || '';
                document.getElementById('birthPlaceFr').textContent = urlParams.get('birthPlaceFr') || urlParams.get('birthPlace') || '';

                if (birthDateWords) {
                    // تحويل التاريخ بالحروف العربية إلى فرنسية
                    const frenchWords = dateToWords.gregorianDateToWordsFrench(birthDate);
                    document.getElementById('birthDateFr').textContent = frenchWords || birthDate;
                } else if (birthDate) {
                    // تحويل التاريخ إلى كلمات فرنسية إذا لم يكن متوفراً
                    const frenchWords = dateToWords.gregorianDateToWordsFrench(birthDate);
                    document.getElementById('birthDateFr').textContent = frenchWords || birthDate;
                } else {
                    document.getElementById('birthDateFr').textContent = '';
                }
                const hijriDateAr = urlParams.get('hijriDate') || '';
                document.getElementById('hijriDateFr').textContent = convertHijriToFrench(hijriDateAr);
                document.getElementById('genderFr').textContent = urlParams.get('gender') === 'ذكر' ? 'Masculin' : 'Féminin';
                document.getElementById('fatherNameFr').textContent = urlParams.get('fatherNameFr') || urlParams.get('fatherName') || '';
                document.getElementById('motherNameFr').textContent = urlParams.get('motherNameFr') || urlParams.get('motherName') || '';
                document.getElementById('actNumberFr').textContent = urlParams.get('actNumber') || '';

                // تحديث تاريخ الإصدار بالتاريخ الحالي
                const currentDate = new Date();

                // التاريخ بالعربية
                const arabicMonths = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];
                const arabicDate = `${currentDate.getDate()} ${arabicMonths[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                document.getElementById('issueDateAr').textContent = arabicDate;

                // التاريخ بالفرنسية
                const frenchMonths = [
                    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
                ];
                const frenchDate = `${currentDate.getDate()} ${frenchMonths[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                document.getElementById('issueDateFr').textContent = frenchDate;

                console.log('📅 تم تحديث تاريخ الإصدار من URL:', { arabicDate, frenchDate });

                // تحويل تاريخ الميلاد إلى كلمات
                const birthDateParam = urlParams.get('birthDate') || '';
                if (birthDateParam) {
                    convertBirthDateToWords(birthDateParam);
                }

                // تم إزالة تاريخ الطباعة التلقائي حسب طلب الموظف
                } else {
                    console.log('⚠️ No URL parameters found for data loading');

                    // تحديث تاريخ الإصدار بالتاريخ الحالي حتى للبيانات التجريبية
                    const currentDate = new Date();

                    // التاريخ بالعربية
                    const arabicMonths = [
                        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                    ];
                    const arabicDate = `${currentDate.getDate()} ${arabicMonths[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                    document.getElementById('issueDateAr').textContent = arabicDate;

                    // التاريخ بالفرنسية
                    const frenchMonths = [
                        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
                    ];
                    const frenchDate = `${currentDate.getDate()} ${frenchMonths[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                    document.getElementById('issueDateFr').textContent = frenchDate;

                    console.log('📅 تم تحديث تاريخ الإصدار للبيانات التجريبية:', { arabicDate, frenchDate });
                }
            } catch (error) {
                console.error('❌ Error in loadDataFromURL:', error);
            }
        }

        // Load citizen data by ID from IndexedDB
        async function loadCitizenDataById(citizenId) {
            try {
                console.log('🔍 Attempting to load citizen with ID:', citizenId);

                // Initialize IndexedDB if not already done
                if (typeof citizensDB === 'undefined') {
                    console.log('📚 Loading IndexedDB manager...');
                    // Load IndexedDB manager
                    const script = document.createElement('script');
                    script.src = 'indexeddb-manager.js';
                    document.head.appendChild(script);

                    // Wait for script to load
                    await new Promise(resolve => {
                        script.onload = resolve;
                    });

                    console.log('🔧 Initializing database...');
                    // Initialize database
                    await citizensDB.init();
                }

                // Get citizen data - try different methods
                console.log('📊 Fetching citizen data...');
                let citizen = await citizensDB.getCitizen(citizenId);
                console.log('📋 Retrieved citizen data (direct):', citizen);

                // If not found, try searching by different ID formats
                if (!citizen) {
                    console.log('🔍 Trying alternative search methods...');

                    // Get all citizens and search manually
                    const allCitizens = await citizensDB.getAllCitizens();
                    console.log('📊 Total citizens in database:', allCitizens.length);

                    // Try to find by exact ID match
                    citizen = allCitizens.find(c => c.id === citizenId);
                    if (citizen) {
                        console.log('✅ Found citizen by exact ID match:', citizen);
                    } else {
                        // Try to find by ID as string
                        citizen = allCitizens.find(c => c.id === citizenId.toString());
                        if (citizen) {
                            console.log('✅ Found citizen by string ID match:', citizen);
                        } else {
                            // Try to find by ID as number
                            citizen = allCitizens.find(c => c.id === parseInt(citizenId));
                            if (citizen) {
                                console.log('✅ Found citizen by number ID match:', citizen);
                            } else {
                                // Show some sample IDs for debugging
                                console.log('🔍 Sample citizen IDs in database:',
                                    allCitizens.slice(0, 5).map(c => ({id: c.id, type: typeof c.id, name: c.firstNameAr || c.personalName}))
                                );
                            }
                        }
                    }
                }

                if (citizen) {
                    console.log('📋 Filling certificate with citizen data:', citizen);

                    // Clear any existing data first
                    console.log('🧹 Clearing existing data...');

                    // Fill Arabic fields with detailed logging
                    console.log('📝 Filling Arabic fields...');
                    const firstNameAr = citizen.firstNameAr || '';
                    const familyNameAr = citizen.familyNameAr || '';
                    const birthPlaceAr = citizen.birthPlaceAr || '';
                    const actNumber = citizen.actNumber || '';

                    console.log('📝 Data to fill:', {
                        firstNameAr,
                        familyNameAr,
                        birthPlaceAr,
                        actNumber,
                        birthDate: citizen.birthDate,
                        gender: citizen.gender
                    });

                    document.getElementById('firstNameAr').textContent = firstNameAr;
                    document.getElementById('familyNameAr').textContent = familyNameAr;
                    document.getElementById('birthPlaceAr').textContent = birthPlaceAr;

                    // استخدام التاريخ بالحروف إذا كان متوفراً، وإلا تحويل التاريخ العادي
                    if (citizen.birthDateWords) {
                        document.getElementById('birthDateAr').textContent = citizen.birthDateWords;
                    } else if (citizen.birthDate) {
                        const arabicWords = dateToWords.gregorianDateToWordsArabic(citizen.birthDate);
                        document.getElementById('birthDateAr').textContent = arabicWords || citizen.birthDate;
                    } else {
                        document.getElementById('birthDateAr').textContent = '';
                    }

                    document.getElementById('hijriDateAr').textContent = citizen.hijriDate || '';
                    document.getElementById('genderAr').textContent = citizen.gender || '';
                    document.getElementById('fatherNameAr').textContent = citizen.fatherNameAr || '';
                    document.getElementById('motherNameAr').textContent = citizen.motherNameAr || '';
                    document.getElementById('actNumberAr').textContent = actNumber;

                    // Fill French fields
                    console.log('📝 Filling French fields...');
                    document.getElementById('firstNameFr').textContent = citizen.firstNameFr || '';
                    document.getElementById('familyNameFr').textContent = citizen.familyNameFr || '';
                    document.getElementById('birthPlaceFr').textContent = citizen.birthPlaceFr || '';

                    // استخدام التاريخ بالحروف الفرنسية
                    console.log('🇫🇷 معالجة التاريخ الفرنسي:');
                    console.log('  - citizen.birthDate:', citizen.birthDate);
                    console.log('  - citizen.birthDateWords:', citizen.birthDateWords);

                    if (citizen.birthDate) {
                        const frenchWords = dateToWords.gregorianDateToWordsFrench(citizen.birthDate);
                        console.log('  - frenchWords result:', frenchWords);
                        document.getElementById('birthDateFr').textContent = frenchWords || citizen.birthDate;
                    } else {
                        document.getElementById('birthDateFr').textContent = '';
                    }
                    const hijriDateAr = citizen.hijriDate || '';
                    document.getElementById('hijriDateFr').textContent = convertHijriToFrench(hijriDateAr);
                    document.getElementById('genderFr').textContent = citizen.gender === 'ذكر' ? 'Masculin' : 'Féminin';
                    document.getElementById('fatherNameFr').textContent = citizen.fatherNameFr || '';
                    document.getElementById('motherNameFr').textContent = citizen.motherNameFr || '';
                    document.getElementById('actNumberFr').textContent = actNumber;

                    // Extract and fill years from birth date
                    if (citizen.birthDate) {
                        console.log('📅 Processing birth date:', citizen.birthDate);
                        const birthYear = new Date(citizen.birthDate).getFullYear();
                        document.getElementById('gregYearAr').textContent = birthYear;
                        document.getElementById('gregYearFr').textContent = birthYear;

                        // Calculate Hijri year (approximate)
                        const hijriYear = Math.floor(birthYear - 579);
                        document.getElementById('hijriYearAr').textContent = hijriYear;
                        document.getElementById('hijriYearFr').textContent = hijriYear;
                        console.log('📅 Years filled - Gregorian:', birthYear, 'Hijri:', hijriYear);
                    }

                    // تحديث تاريخ الإصدار بالتاريخ الحالي
                    const currentDate = new Date();

                    // التاريخ بالعربية
                    const arabicMonths = [
                        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                    ];
                    const arabicDate = `${currentDate.getDate()} ${arabicMonths[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                    document.getElementById('issueDateAr').textContent = arabicDate;

                    // التاريخ بالفرنسية
                    const frenchMonths = [
                        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
                    ];
                    const frenchDate = `${currentDate.getDate()} ${frenchMonths[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                    document.getElementById('issueDateFr').textContent = frenchDate;

                    console.log('📅 تم تحديث تاريخ الإصدار:', { arabicDate, frenchDate });

                    // تحويل تاريخ الميلاد إلى كلمات
                    if (citizen.birthDate) {
                        convertBirthDateToWords(citizen.birthDate);
                    }

                    // Verify data was actually filled
                    console.log('🔍 Verifying filled data...');
                    console.log('Arabic Name:', document.getElementById('firstNameAr').textContent);
                    console.log('Family Name:', document.getElementById('familyNameAr').textContent);
                    console.log('Act Number:', document.getElementById('actNumberAr').textContent);

                    console.log('✅ تم تحميل بيانات المواطن بنجاح:', citizen);
                } else {
                    console.error('❌ لم يتم العثور على المواطن بالرقم:', citizenId);
                    alert('❌ لم يتم العثور على بيانات المواطن');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                alert('❌ خطأ في تحميل بيانات المواطن');
            }
        }

        function printDocument() {
            window.print();
        }

        // تكييف حجم الخط حسب طول النص
        function adaptTextSize() {
            const birthPlaceElements = document.querySelectorAll('.birth-place-adaptive');

            birthPlaceElements.forEach(element => {
                const text = element.textContent || '';
                const textLength = text.length;

                // إزالة الكلاسات السابقة
                element.classList.remove('long-text', 'very-long-text');

                // تطبيق الكلاس حسب طول النص
                if (textLength > 25) {
                    element.classList.add('very-long-text');
                } else if (textLength > 15) {
                    element.classList.add('long-text');
                }

                console.log(`📝 Text: "${text}" (${textLength} chars) - Class: ${element.className}`);
            });
        }

        // مراقبة تغيير النص
        function observeTextChanges() {
            const birthPlaceElements = document.querySelectorAll('.birth-place-adaptive');

            birthPlaceElements.forEach(element => {
                // مراقب لتغيير المحتوى
                const observer = new MutationObserver(() => {
                    adaptTextSize();
                });

                observer.observe(element, {
                    childList: true,
                    characterData: true,
                    subtree: true
                });
            });
        }

        // دالة لتحويل التاريخ الهجري إلى الفرنسية بالترتيب الصحيح
        function convertHijriToFrench(hijriDateAr) {
            if (!hijriDateAr) return '';

            // قاموس الأشهر الهجرية
            const hijriMonthsAr = {
                'محرم': 'Muharram',
                'صفر': 'Safar',
                'ربيع الأول': 'Rabi\' al-awwal',
                'ربيع الثاني': 'Rabi\' al-thani',
                'جمادى الأولى': 'Jumada al-awwal',
                'جمادى الآخرة': 'Jumada al-thani',
                'رجب': 'Rajab',
                'شعبان': 'Sha\'ban',
                'رمضان': 'Ramadan',
                'شوال': 'Shawwal',
                'ذو القعدة': 'Dhu al-Qi\'dah',
                'ذو الحجة': 'Dhu al-Hijjah'
            };

            try {
                // تحليل التاريخ العربي (مثال: "2 ربيع الثاني 1437")
                const parts = hijriDateAr.trim().split(' ');
                if (parts.length >= 3) {
                    const day = parts[0];
                    const monthAr = parts.slice(1, -1).join(' '); // في حالة كان اسم الشهر مكون من كلمتين
                    const year = parts[parts.length - 1];

                    const monthFr = hijriMonthsAr[monthAr] || monthAr;

                    // ترتيب فرنسي: يوم شهر سنة
                    return `${day} ${monthFr} ${year}`;
                }
            } catch (error) {
                console.warn('خطأ في تحويل التاريخ الهجري:', error);
            }

            return hijriDateAr; // إرجاع النص الأصلي في حالة الفشل
        }

        // دالة تحويل تاريخ الميلاد إلى كلمات وعرضه في نفس الحقل
        function convertBirthDateToWords(birthDate) {
            try {
                if (!birthDate) {
                    console.warn('⚠️ لا يوجد تاريخ ميلاد للتحويل');
                    return;
                }

                // تحويل إلى كلمات عربية
                const arabicWords = dateToWords.gregorianDateToWordsArabic(birthDate);
                if (arabicWords) {
                    document.getElementById('birthDateAr').textContent = arabicWords;
                    console.log('📝 تم تحويل التاريخ إلى كلمات عربية:', arabicWords);
                }

                // تحويل إلى كلمات فرنسية
                const frenchWords = dateToWords.gregorianDateToWordsFrench(birthDate);
                if (frenchWords) {
                    document.getElementById('birthDateFr').textContent = frenchWords;
                    console.log('📝 تم تحويل التاريخ إلى كلمات فرنسية:', frenchWords);
                }

                console.log('✅ تم تحويل تاريخ الميلاد إلى كلمات بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تحويل تاريخ الميلاد إلى كلمات:', error);
            }
        }

        // كلاس تحويل التاريخ إلى كتابة بالحروف
        class DateToWordsConverter {
            constructor() {
                // أرقام الوحدات
                this.units = [
                    '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'
                ];

                // أرقام العشرات
                this.tens = [
                    '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
                ];

                // أرقام من 10 إلى 19
                this.teens = [
                    'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
                    'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
                ];

                // أسماء الأشهر الميلادية بالعربية
                this.gregorianMonths = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'دجنبر'
                ];

                // أسماء الأشهر الميلادية بالفرنسية
                this.gregorianMonthsFr = [
                    'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL', 'MAI', 'JUIN',
                    'JUILLET', 'AOÛT', 'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
                ];

                // أرقام بالفرنسية
                this.frenchUnits = [
                    '', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf'
                ];

                this.frenchTens = [
                    '', 'dix', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingts', 'quatre-vingt-dix'
                ];

                this.frenchTeens = [
                    'dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze',
                    'seize', 'dix-sept', 'dix-huit', 'dix-neuf'
                ];
            }

            // تحويل رقم إلى كلمات عربية (1-99)
            numberToWordsArabic(num) {
                if (num === 0) return '';
                if (num === 1) return 'واحد';
                if (num === 2) return 'اثنان';
                if (num >= 10 && num <= 19) {
                    return this.teens[num - 10];
                }
                if (num >= 20 && num <= 99) {
                    const tensDigit = Math.floor(num / 10);
                    const unitsDigit = num % 10;
                    if (unitsDigit === 0) {
                        return this.tens[tensDigit];
                    } else {
                        return this.units[unitsDigit] + ' و' + this.tens[tensDigit];
                    }
                }
                if (num >= 3 && num <= 9) {
                    return this.units[num];
                }
                return num.toString();
            }

            // تحويل رقم إلى كلمات فرنسية (1-99)
            numberToWordsFrench(num) {
                if (num === 0) return '';
                if (num === 1) return 'un';
                if (num >= 10 && num <= 19) {
                    return this.frenchTeens[num - 10];
                }
                if (num >= 20 && num <= 99) {
                    const tensDigit = Math.floor(num / 10);
                    const unitsDigit = num % 10;

                    if (tensDigit === 7) {
                        if (unitsDigit === 0) return 'soixante-dix';
                        return 'soixante-' + this.frenchTeens[unitsDigit];
                    }
                    if (tensDigit === 9) {
                        if (unitsDigit === 0) return 'quatre-vingt-dix';
                        return 'quatre-vingt-' + this.frenchTeens[unitsDigit];
                    }
                    if (tensDigit === 8) {
                        if (unitsDigit === 0) return 'quatre-vingts';
                        return 'quatre-vingt-' + this.frenchUnits[unitsDigit];
                    }

                    if (unitsDigit === 0) {
                        return this.frenchTens[tensDigit];
                    } else {
                        return this.frenchTens[tensDigit] + '-' + this.frenchUnits[unitsDigit];
                    }
                }
                if (num >= 2 && num <= 9) {
                    return this.frenchUnits[num];
                }
                return num.toString();
            }

            // تحويل رقم كامل إلى كلمات عربية (يدعم حتى 9999)
            fullNumberToWordsArabic(num) {
                if (num === 0) return 'صفر';
                if (num === 1) return 'واحد';
                if (num === 2) return 'اثنان';

                let result = '';

                // الآلاف
                if (num >= 1000) {
                    const thousandsDigit = Math.floor(num / 1000);
                    if (thousandsDigit === 1) {
                        result += 'ألف';
                    } else if (thousandsDigit === 2) {
                        result += 'ألفان';
                    } else if (thousandsDigit >= 3 && thousandsDigit <= 10) {
                        result += this.numberToWordsArabic(thousandsDigit) + ' آلاف';
                    } else {
                        result += this.numberToWordsArabic(thousandsDigit) + ' ألف';
                    }
                    num = num % 1000;
                    if (num > 0) result += ' ';
                }

                // المئات
                if (num >= 100) {
                    const hundredsDigit = Math.floor(num / 100);
                    if (hundredsDigit === 1) {
                        result += 'مائة';
                    } else if (hundredsDigit === 2) {
                        result += 'مائتان';
                    } else {
                        result += this.units[hundredsDigit] + 'مائة';
                    }
                    num = num % 100;
                    if (num > 0) result += ' ';
                }

                // العشرات والوحدات
                if (num > 0) {
                    result += this.numberToWordsArabic(num);
                }

                return result;
            }

            // تحويل رقم كامل إلى كلمات فرنسية (يدعم حتى 9999)
            fullNumberToWordsFrench(num) {
                if (num === 0) return 'zéro';
                if (num === 1) return 'un';

                let result = '';

                // الآلاف
                if (num >= 1000) {
                    const thousandsDigit = Math.floor(num / 1000);
                    if (thousandsDigit === 1) {
                        result += 'mille';
                    } else {
                        result += this.numberToWordsFrench(thousandsDigit) + ' mille';
                    }
                    num = num % 1000;
                    if (num > 0) result += ' ';
                }

                // المئات
                if (num >= 100) {
                    const hundredsDigit = Math.floor(num / 100);
                    if (hundredsDigit === 1) {
                        result += 'cent';
                    } else {
                        result += this.numberToWordsFrench(hundredsDigit) + ' cent';
                    }
                    num = num % 100;
                    if (num > 0) result += ' ';
                }

                // العشرات والوحدات
                if (num > 0) {
                    result += this.numberToWordsFrench(num);
                }

                return result;
            }

            // تحويل تاريخ ميلادي إلى كلمات عربية (الشهر فقط)
            gregorianDateToWordsArabic(dateString) {
                try {
                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) {
                        return null;
                    }

                    const day = date.getDate();
                    const month = date.getMonth(); // 0-based
                    const year = date.getFullYear();

                    // عرض اليوم والسنة بالأرقام، والشهر بالعربية فقط
                    const monthName = this.gregorianMonths[month];

                    return `${day} ${monthName} ${year}`;
                } catch (error) {
                    console.error('خطأ في تحويل التاريخ الميلادي إلى كلمات عربية:', error);
                    return null;
                }
            }

            // تحويل تاريخ ميلادي إلى كلمات فرنسية (الشهر فقط)
            gregorianDateToWordsFrench(dateString) {
                try {
                    console.log('🇫🇷 gregorianDateToWordsFrench called with:', dateString);
                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) {
                        console.log('🇫🇷 Invalid date, returning null');
                        return null;
                    }

                    const day = date.getDate();
                    const month = date.getMonth(); // 0-based
                    const year = date.getFullYear();

                    console.log('🇫🇷 Date parts:', { day, month, year });
                    console.log('🇫🇷 Available months:', this.gregorianMonthsFr);

                    // عرض اليوم والسنة بالأرقام، والشهر بالفرنسية فقط
                    const monthName = this.gregorianMonthsFr[month];
                    console.log('🇫🇷 Selected month name:', monthName);

                    const result = `${day} ${monthName} ${year}`;
                    console.log('🇫🇷 Final result:', result);
                    return result;
                } catch (error) {
                    console.error('خطأ في تحويل التاريخ الميلادي إلى كلمات فرنسية:', error);
                    return null;
                }
            }
        }

        // إنشاء instance من محول التاريخ إلى كلمات
        const dateToWords = new DateToWordsConverter();

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page loaded, starting data loading process...');
            loadDataFromURL();

            // تطبيق التكييف بعد تحميل البيانات
            setTimeout(() => {
                adaptTextSize();
                observeTextChanges();
            }, 1000);
        });
    </script>
</body>
</html>
