<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - مكتب الحالة المدنية أيير</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇲🇦</text></svg>">

    <style>
        /* ===== إعدادات عامة ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* ألوان المملكة المغربية */
            --morocco-red: #C1272D;
            --morocco-green: #006233;
            --morocco-gold: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #bdc3c7;
            --background-light: #f8f9fa;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);

            /* خطوط */
            --font-primary: 'Segoe UI', 'Amiri', 'Times New Roman', serif;
            --font-secondary: 'Arial', 'Tahoma', sans-serif;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            direction: rtl;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* ===== Header ===== */
        .header {
            background: linear-gradient(135deg, var(--morocco-red) 0%, #8b0000 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--morocco-green) 0%, var(--morocco-red) 50%, var(--morocco-green) 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 20px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, var(--morocco-gold) 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
        }

        .header-text {
            text-align: right;
        }

        .header-text h1 {
            font-size: 2.2em;
            margin: 0 0 5px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        /* ===== Navigation ===== */
        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        /* ===== المحتوى الرئيسي ===== */
        .content {
            flex: 1;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .page-title {
            text-align: center;
            color: var(--morocco-red);
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .management-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* ===== شريط البحث والفلترة ===== */
        .search-filter-bar {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(0, 98, 51, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(0, 98, 51, 0.1);
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            font-family: var(--font-primary);
            transition: all 0.3s ease;
            background: white;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--morocco-green);
            box-shadow: 0 0 0 3px rgba(0, 98, 51, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 1.2em;
        }

        .filter-select {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            font-family: var(--font-primary);
            background: white;
            min-width: 150px;
        }

        .add-employee-btn {
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--morocco-green), #004d26);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-employee-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 98, 51, 0.3);
        }

        /* ===== جدول الموظفين ===== */
        .employees-table-container {
            overflow-x: auto;
            border-radius: 15px;
            border: 1px solid var(--border-color);
            background: white;
        }

        .employees-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.95rem;
        }

        .employees-table th,
        .employees-table td {
            padding: 15px 12px;
            text-align: right;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .employees-table th {
            background: linear-gradient(135deg, var(--morocco-red), #8b0000);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .employees-table tbody tr {
            transition: all 0.3s ease;
        }

        .employees-table tbody tr:hover {
            background: rgba(0, 98, 51, 0.05);
            transform: translateY(-1px);
        }

        .employee-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--morocco-gold), #ffb347);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            font-weight: bold;
            color: var(--text-dark);
            margin: 0 auto;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-align: center;
        }

        .status-active {
            background: rgba(0, 98, 51, 0.1);
            color: var(--morocco-green);
            border: 1px solid rgba(0, 98, 51, 0.3);
        }

        .status-inactive {
            background: rgba(196, 30, 58, 0.1);
            color: var(--morocco-red);
            border: 1px solid rgba(196, 30, 58, 0.3);
        }

        .role-badge {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            text-align: center;
        }

        .role-admin {
            background: linear-gradient(135deg, var(--morocco-red), #8b0000);
            color: white;
        }

        .role-employee {
            background: linear-gradient(135deg, var(--morocco-green), #004d26);
            color: white;
        }

        .role-user {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        /* ===== أزرار العمليات ===== */
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #f39c12, #d68910);
            color: white;
        }

        .btn-password {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-delete {
            background: linear-gradient(135deg, var(--morocco-red), #8b0000);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* ===== النوافذ المنبثقة ===== */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .modal-title {
            color: var(--morocco-red);
            font-size: 1.5em;
            font-weight: 700;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .close-btn:hover {
            color: var(--morocco-red);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 1em;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            font-family: var(--font-primary);
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--morocco-green);
            box-shadow: 0 0 0 3px rgba(0, 98, 51, 0.1);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1em;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--morocco-green);
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .modal-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--morocco-green), #004d26);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--text-light), #95a5a6);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--morocco-red), #8b0000);
            color: white;
        }

        .modal-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        /* ===== رسائل التنبيه ===== */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
            display: none;
        }

        .alert.show {
            display: block;
            animation: alertSlideIn 0.3s ease;
        }

        @keyframes alertSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(0, 98, 51, 0.1), rgba(0, 77, 38, 0.1));
            color: var(--morocco-green);
            border: 1px solid rgba(0, 98, 51, 0.3);
        }

        .alert-error {
            background: linear-gradient(135deg, rgba(196, 30, 58, 0.1), rgba(139, 0, 0, 0.1));
            color: var(--morocco-red);
            border: 1px solid rgba(196, 30, 58, 0.3);
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(214, 137, 16, 0.1));
            color: #f39c12;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }

        /* ===== إحصائيات سريعة ===== */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .stat-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: var(--morocco-red);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9em;
            font-weight: 600;
        }

        /* ===== تصميم متجاوب ===== */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.8em;
            }

            .header-nav-links {
                gap: 6px;
                justify-content: center;
            }

            .header-nav-link {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .header-right {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .content {
                padding: 20px 15px;
            }

            .search-filter-bar {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .employees-table {
                font-size: 0.8rem;
            }

            .employees-table th,
            .employees-table td {
                padding: 10px 8px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 5px;
            }

            .action-btn {
                font-size: 0.75rem;
                padding: 4px 8px;
            }

            .modal-content {
                width: 95%;
                padding: 20px;
            }

            .modal-actions {
                flex-direction: column;
            }

            .stats-container {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
        }

        /* ===== تأثيرات التحميل ===== */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .table-loading {
            text-align: center;
            padding: 40px;
            color: var(--text-light);
        }

        .no-employees {
            text-align: center;
            padding: 40px;
            color: var(--text-light);
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">👥 إدارة الموظفين والمستخدمين</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="header-navigation">
                <div class="header-nav-content">
                    <div class="header-nav-links">
                        <a href="main-dashboard.html" class="header-nav-link">🏠 الصفحة الرئيسية</a>
                        <a href="citizens-database-indexeddb.html" class="header-nav-link">📝 إدارة البيانات</a>
                        <a href="search-citizens.html" class="header-nav-link">🔍 البحث في السجلات</a>
                        <a href="personal-id-form.html" class="header-nav-link">🆔 البطاقة الشخصية</a>
                        <a href="death-data-entry.html" class="header-nav-link">⚱️ تسجيل الوفاة</a>
                        <a href="employee-management.html" class="header-nav-link active">👥 إدارة الموظفين</a>
                        <a href="data-transfer.html" class="header-nav-link">🔄 و 🛡️ النسخ الاحتياطية</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content">
            <h1 class="page-title">👥 إدارة الموظفين والمستخدمين</h1>

            <div class="management-container">
                <!-- رسائل التنبيه -->
                <div id="alertContainer"></div>

                <!-- إحصائيات سريعة -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-number" id="totalEmployees">0</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-number" id="activeEmployees">0</div>
                        <div class="stat-label">الموظفين النشطين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👑</div>
                        <div class="stat-number" id="adminEmployees">0</div>
                        <div class="stat-label">المديرين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏸️</div>
                        <div class="stat-number" id="inactiveEmployees">0</div>
                        <div class="stat-label">غير النشطين</div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="search-filter-bar">
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني...">
                        <span class="search-icon">🔍</span>
                    </div>
                    <select id="roleFilter" class="filter-select">
                        <option value="">جميع الأدوار</option>
                        <option value="admin">مدير</option>
                        <option value="employee">موظف</option>
                        <option value="user">مستخدم</option>
                    </select>
                    <button class="add-employee-btn" onclick="showAddEmployeeModal()">
                        <span>➕</span>
                        <span>إضافة موظف</span>
                    </button>
                </div>

                <!-- جدول الموظفين -->
                <div class="employees-table-container">
                    <table class="employees-table">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>آخر دخول</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <tr>
                                <td colspan="9" class="table-loading">
                                    <div class="loading"></div>
                                    جاري تحميل بيانات الموظفين...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل موظف -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">إضافة موظف جديد</h2>
                <button class="close-btn" onclick="closeModal('employeeModal')">&times;</button>
            </div>
            <form id="employeeForm">
                <input type="hidden" id="employeeId">
                <div class="form-group">
                    <label for="firstName">الاسم الأول:</label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>
                <div class="form-group">
                    <label for="lastName">الاسم الأخير:</label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">رقم الهاتف:</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                <div class="form-group">
                    <label for="role">الدور:</label>
                    <select id="role" name="role" required>
                        <option value="user">مستخدم</option>
                        <option value="employee">موظف</option>
                        <option value="admin">مدير</option>
                    </select>
                </div>
                <div class="form-group" id="passwordGroup">
                    <label for="password">كلمة المرور:</label>
                    <div class="password-container">
                        <input type="password" id="password" name="password">
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">👁️</button>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="modal-btn btn-secondary" onclick="closeModal('employeeModal')">إلغاء</button>
                    <button type="submit" class="modal-btn btn-primary" id="saveEmployeeBtn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تغيير كلمة المرور -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">تغيير كلمة المرور</h2>
                <button class="close-btn" onclick="closeModal('passwordModal')">&times;</button>
            </div>
            <form id="passwordForm">
                <input type="hidden" id="passwordEmployeeId">
                <div class="form-group">
                    <label>الموظف:</label>
                    <input type="text" id="passwordEmployeeName" readonly style="background: #f8f9fa;">
                </div>
                <div class="form-group">
                    <label for="newPassword">كلمة المرور الجديدة:</label>
                    <div class="password-container">
                        <input type="password" id="newPassword" name="newPassword" required minlength="6">
                        <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">👁️</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور:</label>
                    <div class="password-container">
                        <input type="password" id="confirmPassword" name="confirmPassword" required minlength="6">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">👁️</button>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="modal-btn btn-secondary" onclick="closeModal('passwordModal')">إلغاء</button>
                    <button type="submit" class="modal-btn btn-primary">تحديث كلمة المرور</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">تأكيد حذف الموظف</h2>
                <button class="close-btn" onclick="closeModal('deleteModal')">&times;</button>
            </div>
            <div style="padding: 20px 0;">
                <p style="font-size: 1.1em; margin-bottom: 15px;">⚠️ هل أنت متأكد من حذف هذا الموظف؟</p>
                <div style="background: rgba(196, 30, 58, 0.1); padding: 15px; border-radius: 10px; border: 1px solid rgba(196, 30, 58, 0.3);">
                    <strong>الموظف:</strong> <span id="deleteEmployeeName"></span><br>
                    <strong>اسم المستخدم:</strong> <span id="deleteEmployeeUsername"></span><br>
                    <strong style="color: var(--morocco-red);">تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="modal-btn btn-secondary" onclick="closeModal('deleteModal')">إلغاء</button>
                <button type="button" class="modal-btn btn-danger" id="confirmDeleteBtn">حذف الموظف</button>
            </div>
        </div>
    </div>

    <script src="login-db.js"></script>
    <script>
        // إدارة الموظفين
        class EmployeeManager {
            constructor() {
                this.loginDB = new LoginDB();
                this.employees = [];
                this.filteredEmployees = [];
                this.currentEditingEmployee = null;
                this.init();
            }

            async init() {
                await this.loginDB.init();
                this.setupEventListeners();
                await this.loadEmployees();
                this.updateStats();
            }

            setupEventListeners() {
                // البحث والفلترة
                document.getElementById('searchInput').addEventListener('input', () => this.filterEmployees());
                document.getElementById('roleFilter').addEventListener('change', () => this.filterEmployees());

                // النماذج
                document.getElementById('employeeForm').addEventListener('submit', (e) => this.handleSaveEmployee(e));
                document.getElementById('passwordForm').addEventListener('submit', (e) => this.handleChangePassword(e));
                document.getElementById('confirmDeleteBtn').addEventListener('click', () => this.handleDeleteEmployee());

                // إغلاق النوافذ عند النقر خارجها
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('modal')) {
                        this.closeModal(e.target.id);
                    }
                });
            }

            async loadEmployees() {
                try {
                    this.employees = await this.loginDB.getAllUsers();
                    this.filteredEmployees = [...this.employees];
                    this.renderEmployeesTable();
                } catch (error) {
                    console.error('خطأ في تحميل الموظفين:', error);
                    this.showAlert('حدث خطأ في تحميل بيانات الموظفين', 'error');
                }
            }

            filterEmployees() {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const roleFilter = document.getElementById('roleFilter').value;

                this.filteredEmployees = this.employees.filter(employee => {
                    const matchesSearch = !searchTerm ||
                        employee.fullName?.toLowerCase().includes(searchTerm) ||
                        employee.username?.toLowerCase().includes(searchTerm) ||
                        employee.email?.toLowerCase().includes(searchTerm) ||
                        employee.firstName?.toLowerCase().includes(searchTerm) ||
                        employee.lastName?.toLowerCase().includes(searchTerm);

                    const matchesRole = !roleFilter || employee.role === roleFilter;

                    return matchesSearch && matchesRole;
                });

                this.renderEmployeesTable();
            }

            renderEmployeesTable() {
                const tbody = document.getElementById('employeesTableBody');

                if (this.filteredEmployees.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" class="no-employees">
                                📭 لا توجد موظفين مطابقين للبحث
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = this.filteredEmployees.map(employee => `
                    <tr>
                        <td>
                            <div class="employee-avatar">
                                ${this.getEmployeeInitials(employee)}
                            </div>
                        </td>
                        <td>${employee.fullName || `${employee.firstName || ''} ${employee.lastName || ''}`.trim()}</td>
                        <td>${employee.username}</td>
                        <td>${employee.email}</td>
                        <td>
                            <span class="role-badge role-${employee.role}">
                                ${this.getRoleLabel(employee.role)}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge ${employee.isActive ? 'status-active' : 'status-inactive'}">
                                ${employee.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>${this.formatDate(employee.createdAt)}</td>
                        <td>${employee.lastLogin ? this.formatDate(employee.lastLogin) : 'لم يسجل دخول'}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn btn-edit" onclick="employeeManager.showEditEmployeeModal(${employee.id})" title="تعديل">
                                    ✏️ تعديل
                                </button>
                                <button class="action-btn btn-password" onclick="employeeManager.showPasswordModal(${employee.id})" title="تغيير كلمة المرور">
                                    🔑 كلمة المرور
                                </button>
                                <button class="action-btn btn-delete" onclick="employeeManager.showDeleteModal(${employee.id})" title="حذف">
                                    🗑️ حذف
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }

            getEmployeeInitials(employee) {
                const firstName = employee.firstName || employee.fullName?.split(' ')[0] || employee.username;
                const lastName = employee.lastName || employee.fullName?.split(' ')[1] || '';
                return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
            }

            getRoleLabel(role) {
                const labels = {
                    'admin': 'مدير',
                    'employee': 'موظف',
                    'user': 'مستخدم'
                };
                return labels[role] || role;
            }

            formatDate(dateString) {
                if (!dateString) return '-';
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            updateStats() {
                const total = this.employees.length;
                const active = this.employees.filter(e => e.isActive).length;
                const admin = this.employees.filter(e => e.role === 'admin').length;
                const inactive = total - active;

                document.getElementById('totalEmployees').textContent = total;
                document.getElementById('activeEmployees').textContent = active;
                document.getElementById('adminEmployees').textContent = admin;
                document.getElementById('inactiveEmployees').textContent = inactive;
            }

            showAddEmployeeModal() {
                this.currentEditingEmployee = null;
                document.getElementById('modalTitle').textContent = 'إضافة موظف جديد';
                document.getElementById('employeeForm').reset();
                document.getElementById('employeeId').value = '';
                document.getElementById('passwordGroup').style.display = 'block';
                document.getElementById('password').required = true;
                this.showModal('employeeModal');
            }

            showEditEmployeeModal(employeeId) {
                const employee = this.employees.find(e => e.id === employeeId);
                if (!employee) return;

                this.currentEditingEmployee = employee;
                document.getElementById('modalTitle').textContent = 'تعديل بيانات الموظف';

                // ملء النموذج
                document.getElementById('employeeId').value = employee.id;
                document.getElementById('firstName').value = employee.firstName || '';
                document.getElementById('lastName').value = employee.lastName || '';
                document.getElementById('username').value = employee.username;
                document.getElementById('email').value = employee.email;
                document.getElementById('phone').value = employee.phone || '';
                document.getElementById('role').value = employee.role;

                // إخفاء حقل كلمة المرور في التعديل
                document.getElementById('passwordGroup').style.display = 'none';
                document.getElementById('password').required = false;

                this.showModal('employeeModal');
            }

            showPasswordModal(employeeId) {
                const employee = this.employees.find(e => e.id === employeeId);
                if (!employee) return;

                document.getElementById('passwordEmployeeId').value = employee.id;
                document.getElementById('passwordEmployeeName').value = employee.fullName || `${employee.firstName} ${employee.lastName}`;
                document.getElementById('passwordForm').reset();
                document.getElementById('passwordEmployeeId').value = employee.id;
                document.getElementById('passwordEmployeeName').value = employee.fullName || `${employee.firstName} ${employee.lastName}`;

                this.showModal('passwordModal');
            }

            showDeleteModal(employeeId) {
                const employee = this.employees.find(e => e.id === employeeId);
                if (!employee) return;

                document.getElementById('deleteEmployeeName').textContent = employee.fullName || `${employee.firstName} ${employee.lastName}`;
                document.getElementById('deleteEmployeeUsername').textContent = employee.username;
                document.getElementById('confirmDeleteBtn').onclick = () => this.handleDeleteEmployee(employeeId);

                this.showModal('deleteModal');
            }

            async handleSaveEmployee(event) {
                event.preventDefault();

                const formData = new FormData(event.target);
                const employeeData = Object.fromEntries(formData.entries());
                const isEditing = !!employeeData.employeeId;

                try {
                    if (isEditing) {
                        // تحديث موظف موجود
                        const employee = this.employees.find(e => e.id == employeeData.employeeId);
                        if (!employee) throw new Error('الموظف غير موجود');

                        employee.firstName = employeeData.firstName;
                        employee.lastName = employeeData.lastName;
                        employee.username = employeeData.username;
                        employee.email = employeeData.email;
                        employee.phone = employeeData.phone || null;
                        employee.role = employeeData.role;
                        employee.fullName = `${employeeData.firstName} ${employeeData.lastName}`;
                        employee.updatedAt = new Date().toISOString();

                        await this.loginDB.updateUser(employee);
                        await this.loginDB.logUserActivity(employee.id, 'profile_updated', {
                            updatedBy: 'admin',
                            fields: ['firstName', 'lastName', 'username', 'email', 'phone', 'role']
                        });

                        this.showAlert('تم تحديث بيانات الموظف بنجاح', 'success');
                    } else {
                        // إضافة موظف جديد
                        if (!employeeData.password) {
                            this.showAlert('كلمة المرور مطلوبة للموظف الجديد', 'error');
                            return;
                        }

                        const newEmployee = {
                            username: employeeData.username,
                            password: await this.loginDB.hashPassword(employeeData.password),
                            email: employeeData.email,
                            fullName: `${employeeData.firstName} ${employeeData.lastName}`,
                            firstName: employeeData.firstName,
                            lastName: employeeData.lastName,
                            phone: employeeData.phone || null,
                            role: employeeData.role,
                            isActive: true,
                            createdAt: new Date().toISOString(),
                            lastLogin: null
                        };

                        const employeeId = await this.loginDB.addUser(newEmployee);
                        await this.loginDB.logUserActivity(employeeId, 'account_created', {
                            createdBy: 'admin',
                            role: employeeData.role
                        });

                        this.showAlert('تم إضافة الموظف الجديد بنجاح', 'success');
                    }

                    this.closeModal('employeeModal');
                    await this.loadEmployees();
                    this.updateStats();

                } catch (error) {
                    console.error('خطأ في حفظ الموظف:', error);
                    if (error.message.includes('موجود بالفعل')) {
                        this.showAlert('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error');
                    } else {
                        this.showAlert('حدث خطأ أثناء حفظ بيانات الموظف', 'error');
                    }
                }
            }

            async handleChangePassword(event) {
                event.preventDefault();

                const formData = new FormData(event.target);
                const employeeId = formData.get('passwordEmployeeId');
                const newPassword = formData.get('newPassword');
                const confirmPassword = formData.get('confirmPassword');

                if (newPassword !== confirmPassword) {
                    this.showAlert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'error');
                    return;
                }

                if (newPassword.length < 6) {
                    this.showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                    return;
                }

                try {
                    const employee = this.employees.find(e => e.id == employeeId);
                    if (!employee) throw new Error('الموظف غير موجود');

                    employee.password = await this.loginDB.hashPassword(newPassword);
                    employee.updatedAt = new Date().toISOString();

                    await this.loginDB.updateUser(employee);
                    await this.loginDB.logUserActivity(employee.id, 'password_changed', {
                        changedBy: 'admin',
                        timestamp: new Date().toISOString()
                    });

                    this.showAlert('تم تغيير كلمة المرور بنجاح', 'success');
                    this.closeModal('passwordModal');

                } catch (error) {
                    console.error('خطأ في تغيير كلمة المرور:', error);
                    this.showAlert('حدث خطأ أثناء تغيير كلمة المرور', 'error');
                }
            }

            async handleDeleteEmployee(employeeId) {
                if (!employeeId) return;

                try {
                    const employee = this.employees.find(e => e.id == employeeId);
                    if (!employee) throw new Error('الموظف غير موجود');

                    // في بيئة الإنتاج، قد تفضل تعطيل الحساب بدلاً من حذفه
                    // employee.isActive = false;
                    // await this.loginDB.updateUser(employee);

                    // حذف فعلي (للتطوير)
                    if (this.loginDB.fallbackMode) {
                        const users = JSON.parse(localStorage.getItem('login_users') || '[]');
                        const updatedUsers = users.filter(u => u.id != employeeId);
                        localStorage.setItem('login_users', JSON.stringify(updatedUsers));
                    } else {
                        // حذف من IndexedDB
                        const transaction = this.loginDB.db.transaction(['users'], 'readwrite');
                        const store = transaction.objectStore('users');
                        await new Promise((resolve, reject) => {
                            const request = store.delete(parseInt(employeeId));
                            request.onsuccess = () => resolve();
                            request.onerror = () => reject(request.error);
                        });
                    }

                    await this.loginDB.logUserActivity(employeeId, 'account_deleted', {
                        deletedBy: 'admin',
                        employeeName: employee.fullName || `${employee.firstName} ${employee.lastName}`,
                        timestamp: new Date().toISOString()
                    });

                    this.showAlert('تم حذف الموظف بنجاح', 'success');
                    this.closeModal('deleteModal');
                    await this.loadEmployees();
                    this.updateStats();

                } catch (error) {
                    console.error('خطأ في حذف الموظف:', error);
                    this.showAlert('حدث خطأ أثناء حذف الموظف', 'error');
                }
            }

            showModal(modalId) {
                document.getElementById(modalId).classList.add('show');
                document.body.style.overflow = 'hidden';
            }

            closeModal(modalId) {
                document.getElementById(modalId).classList.remove('show');
                document.body.style.overflow = 'auto';
            }

            showAlert(message, type) {
                const alertContainer = document.getElementById('alertContainer');
                const alertClass = type === 'success' ? 'alert-success' : type === 'warning' ? 'alert-warning' : 'alert-error';

                alertContainer.innerHTML = `
                    <div class="alert ${alertClass} show">
                        ${message}
                    </div>
                `;

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    const alert = alertContainer.querySelector('.alert');
                    if (alert) {
                        alert.classList.remove('show');
                        setTimeout(() => {
                            alertContainer.innerHTML = '';
                        }, 300);
                    }
                }, 5000);
            }
        }

        // وظائف مساعدة عامة
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleBtn = passwordInput.nextElementSibling;

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        function showAddEmployeeModal() {
            employeeManager.showAddEmployeeModal();
        }

        function closeModal(modalId) {
            employeeManager.closeModal(modalId);
        }

        // تهيئة مدير الموظفين
        let employeeManager;
        document.addEventListener('DOMContentLoaded', () => {
            employeeManager = new EmployeeManager();
        });
    </script>
</body>
</html>