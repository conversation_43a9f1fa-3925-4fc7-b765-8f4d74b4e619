<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حذف البيانات التجريبية - نظام الحالة المدنية</title>
    <style>
        body {
            font-family: 'Segoe UI', '<PERSON><PERSON>', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: #856404;
        }

        .info-box {
            background: #d1ecf1;
            border: 2px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: #0c5460;
        }

        .success-box {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: #155724;
            display: none;
        }

        .error-box {
            background: #f8d7da;
            border: 2px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: #721c24;
            display: none;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #c41e3a;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            display: none;
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ حذف البيانات التجريبية</h1>
            <p>إدارة وحذف البيانات من قاعدة البيانات</p>
        </div>

        <div class="content">
            <div class="warning-box">
                <h3>⚠️ تحذير مهم</h3>
                <p>هذه العملية ستقوم بحذف جميع البيانات الموجودة في قاعدة البيانات بشكل نهائي.</p>
                <p><strong>لا يمكن التراجع عن هذه العملية!</strong></p>
            </div>

            <div class="info-box" id="systemInfo">
                <h3>📊 معلومات النظام</h3>
                <div class="loading">جاري تحميل معلومات النظام...</div>
            </div>

            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <div class="stat-number" id="totalCitizens">-</div>
                    <div class="stat-label">إجمالي المواطنين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="storageType">-</div>
                    <div class="stat-label">نوع التخزين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="dataSize">-</div>
                    <div class="stat-label">حجم البيانات التقريبي</div>
                </div>
            </div>

            <div class="success-box" id="successMessage">
                <h3>✅ تم الحذف بنجاح</h3>
                <p>تم حذف جميع البيانات من قاعدة البيانات بنجاح.</p>
            </div>

            <div class="error-box" id="errorMessage">
                <h3>❌ خطأ في العملية</h3>
                <p id="errorText">حدث خطأ أثناء محاولة حذف البيانات.</p>
            </div>

            <div class="loading" id="loadingIndicator">
                <h3>⏳ جاري حذف البيانات...</h3>
                <p>يرجى الانتظار، لا تغلق الصفحة.</p>
            </div>

            <div class="actions">
                <button class="btn btn-danger" onclick="clearAllData()" id="clearBtn">
                    🗑️ حذف جميع البيانات
                </button>
                <button class="btn btn-secondary" onclick="refreshInfo()">
                    🔄 تحديث المعلومات
                </button>
                <a href="citizens-database-indexeddb.html" class="btn btn-primary">
                    🏠 العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Include IndexedDB Manager -->
    <script src="indexeddb-manager.js"></script>
    <script>
        let dbManager = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            await initializeSystem();
            await loadSystemInfo();
        });

        // Initialize database system
        async function initializeSystem() {
            try {
                // Try to use CitizensDB if available
                if (typeof citizensDB !== 'undefined') {
                    dbManager = citizensDB;
                    await dbManager.init();
                    console.log('✅ تم تهيئة CitizensDB');
                } else if (typeof CitizensDB !== 'undefined') {
                    dbManager = new CitizensDB();
                    await dbManager.init();
                    console.log('✅ تم تهيئة CitizensDB جديد');
                } else {
                    // Fallback to localStorage
                    dbManager = {
                        fallbackMode: true,
                        async getAllCitizens() {
                            const data = localStorage.getItem('citizens');
                            return data ? JSON.parse(data) : [];
                        },
                        async getCount() {
                            const data = localStorage.getItem('citizens');
                            return data ? JSON.parse(data).length : 0;
                        },
                        async clearAllData() {
                            localStorage.removeItem('citizens');
                            return true;
                        }
                    };
                    console.log('⚠️ استخدام localStorage كبديل');
                }
            } catch (error) {
                console.error('خطأ في تهيئة النظام:', error);
                showError('خطأ في تهيئة النظام: ' + error.message);
            }
        }

        // Load system information
        async function loadSystemInfo() {
            try {
                const citizensCount = await dbManager.getCount();
                const storageType = dbManager.fallbackMode ? 'localStorage' : 'IndexedDB';
                
                // Calculate approximate data size
                let dataSize = 'غير محدد';
                if (dbManager.fallbackMode) {
                    const data = localStorage.getItem('citizens');
                    if (data) {
                        const sizeInBytes = new Blob([data]).size;
                        dataSize = formatBytes(sizeInBytes);
                    } else {
                        dataSize = '0 بايت';
                    }
                }

                // Update stats
                document.getElementById('totalCitizens').textContent = citizensCount;
                document.getElementById('storageType').textContent = storageType;
                document.getElementById('dataSize').textContent = dataSize;

                // Update system info
                const systemInfoDiv = document.getElementById('systemInfo');
                systemInfoDiv.innerHTML = `
                    <h3>📊 معلومات النظام</h3>
                    <p><strong>نوع قاعدة البيانات:</strong> ${storageType}</p>
                    <p><strong>عدد السجلات:</strong> ${citizensCount} مواطن</p>
                    <p><strong>حجم البيانات:</strong> ${dataSize}</p>
                    <p><strong>حالة النظام:</strong> ${citizensCount > 0 ? '✅ يحتوي على بيانات' : '⚪ فارغ'}</p>
                `;

                // Enable/disable clear button
                const clearBtn = document.getElementById('clearBtn');
                if (citizensCount === 0) {
                    clearBtn.disabled = true;
                    clearBtn.textContent = '✅ قاعدة البيانات فارغة بالفعل';
                    clearBtn.style.opacity = '0.6';
                } else {
                    clearBtn.disabled = false;
                    clearBtn.textContent = `🗑️ حذف ${citizensCount} سجل`;
                    clearBtn.style.opacity = '1';
                }

            } catch (error) {
                console.error('خطأ في تحميل معلومات النظام:', error);
                showError('خطأ في تحميل معلومات النظام: ' + error.message);
            }
        }

        // Clear all data
        async function clearAllData() {
            const citizensCount = await dbManager.getCount();
            
            if (citizensCount === 0) {
                showError('قاعدة البيانات فارغة بالفعل');
                return;
            }

            // Confirm deletion
            const confirmed = confirm(
                `⚠️ تحذير: سيتم حذف ${citizensCount} سجل نهائياً!\n\n` +
                'هذه العملية لا يمكن التراجع عنها.\n\n' +
                'هل أنت متأكد من المتابعة؟'
            );

            if (!confirmed) {
                return;
            }

            // Double confirmation for large datasets
            if (citizensCount > 10) {
                const doubleConfirmed = confirm(
                    `🚨 تأكيد نهائي: سيتم حذف ${citizensCount} سجل!\n\n` +
                    'اكتب "نعم" للمتابعة أو "لا" للإلغاء'
                );
                if (!doubleConfirmed) {
                    return;
                }
            }

            try {
                showLoading(true);
                hideMessages();

                // Clear data
                await dbManager.clearAllData();

                // Clear any additional localStorage data
                localStorage.removeItem('lastBackupTime');
                localStorage.removeItem('syncHistory');

                showLoading(false);
                showSuccess();
                await loadSystemInfo();

            } catch (error) {
                console.error('خطأ في حذف البيانات:', error);
                showLoading(false);
                showError('خطأ في حذف البيانات: ' + error.message);
            }
        }

        // Refresh information
        async function refreshInfo() {
            await loadSystemInfo();
        }

        // Utility functions
        function formatBytes(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
            document.getElementById('clearBtn').disabled = show;
        }

        function showSuccess() {
            document.getElementById('successMessage').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').style.display = 'block';
        }

        function hideMessages() {
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
        }
    </script>
</body>
</html>
